{{- if ((.Values.metamodel).url) }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: metamodel-exporter
  namespace: {{ .Values.namespace }}
  labels:
    app: metamodel-exporter-app
spec:
  replicas: 1
  selector:
    matchLabels:
      app: metamodel-exporter-app
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: metamodel-exporter-app
    spec:
      containers:
      - image: {{ .Values.repo.server }}/{{ .Values.repo.suffix }}/metamodel-exporter:{{ .Values.metamodel.version }}
        name: exporter-app
        envFrom:
        - configMapRef:
            name: dtpl-cm-metamodel-config
        - secretRef:
            name: dtpl-secret-metamodel-config
        readinessProbe:
          exec:
            command:
            - ls
            - /
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          {{- $LIMITS := default $.Values.resources ($.Values.metamodel) }}
          {{- with $LIMITS }}
          limits:
            cpu: {{ .limits.cpu | quote }}
            memory: {{ .limits.memory | quote }}
          requests:
            cpu: {{ .requests.cpu | quote }}
            memory: {{ .requests.memory | quote }}
          {{- end }}
      - image: {{ $.Values.repo.server }}/{{ $.Values.repo.suffix }}/metamodel-fluentbit:{{ .Values.metamodel.version }}
        env:
        - name: TZ
          value: "Europe/Moscow"
        name: fluent-app
        readinessProbe:
          exec:
            command:
            - ls
            - /
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
          {{- if hasKey $.Values.kafkaLogs "tls" }}
          - name: kf-tls
            mountPath: /kf-tls/
          {{- end }}
          - name: fluent-config
            mountPath: /fluent-bit/etc/fluent-bit.conf
            subPath: fluent-bit.conf
        resources:
          limits:
            cpu: 200m
            memory: 256Mi
          requests:
            cpu: 100m
            memory: 128Mi
      - image: {{ $.Values.repo.server }}/{{ $.Values.repo.suffix }}/metamodel-scraper:{{ .Values.metamodel.version }}
        env:
        - name: TZ
          value: "Europe/Moscow"
        - name: METRIC_SCRAPER__PROMETHEUS__URL
          value: "http://localhost:9808/metrics"
        - name: METRIC_SCRAPER__POLLING__INTERVAL
          value: "60"
        name: scraper-app
        readinessProbe:
          exec:
            command:
            - ls
            - /
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          limits:
            cpu: 200m
            memory: 256Mi
          requests:
            cpu: 100m
            memory: 128Mi
      volumes:
      {{- if hasKey $.Values.kafkaLogs "tls" }}
      - name: kf-tls
        secret:
          secretName: dtpl-secret-metamodel-certs-kf-tls
          defaultMode: 0400
      {{- end }}
      - name: fluent-config
        configMap:
          name: dtpl-cm-metamodel-fluent-files
          items:
          - key: run.sh
            path: run.sh
            mode: 0755
          - key: fluent-bit.conf
            path: fluent-bit.conf
      
      imagePullSecrets:
      - name: dtpl-secret-metamodel-repo
# ---
# apiVersion: v1
# kind: Service
# metadata:
#   name: metamodel-worker-par-service
#   namespace: {{ .Values.namespace }}
# spec:
#   selector:
#     app: metamodel-web-app
#   ports:
#     - protocol: TCP
#       port: 80
#       targetPort: 8000
...
{{- end }}      

