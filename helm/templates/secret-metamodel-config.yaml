{{- if ((.Values.metamodel).url) }}
---
apiVersion: v1
kind: Secret
metadata:
  name: dtpl-secret-metamodel-config
  namespace: {{ .Values.namespace }}
data:
  CEH_METAMODEL__DB__PASSWORD: {{ default "none" (.Values.pg.mon.roles.loader.pass) | b64enc }}
  DATABASE_URL: {{ default "none" (printf "postgresql://%s:%s@%s:5432/%s" .Values.pg.mon.roles.loader.user .Values.pg.mon.roles.loader.pass .Values.pg.mon.host .Values.pg.mon.database) | b64enc }}
  AF_PASSWORD: {{ .Values.metamodel.airflow.pass | b64enc }}
  CEH_DB_PASSWORD: {{ default "none" (.Values.adb.main.roles.srv.pass) | b64enc }}
...
{{- end }}
