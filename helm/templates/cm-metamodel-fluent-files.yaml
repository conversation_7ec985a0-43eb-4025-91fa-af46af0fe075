{{- if ((.Values.metamodel).url) }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: dtpl-cm-metamodel-fluent-files
  namespace: {{ .Values.namespace }}
data:
  run.sh: |
    #!/bin/bash
    while [ $(nc 127.0.0.1 1212 -zv &> /dev/null; echo $?) -ne 0 ]; do echo "wait fbit start"; sleep 1; done

    IDENT="metamodel-web"
    POD_ID=$(hostname | awk -F"-" {'print $(NF-1)"-"$NF'})
    RUN_SCRIPT="/opt/app-root/bin/uvicorn metaloader_rest_api.app:app --host 0.0.0.0 --port 8000"
    { PYTHONUNBUFFERED=1 stdbuf -o0 $RUN_SCRIPT $@ 2>&1 | sed -u "s/^/{{ .Values.namespace }}CeHdLm{{ .Values.settings.instance }}CeHdLm${POD_ID}CeHdLm${IDENT}CeHdLm/g"; echo $? > /tmp/exc; } | tee >(nc 127.0.0.1 1212) | stdbuf -o0 sed -u 's/.*CeHdLm//g'
    exit $(cat /tmp/exc)

  run-par.sh: |
    #!/bin/bash
    while [ $(nc 127.0.0.1 1212 -zv &> /dev/null; echo $?) -ne 0 ]; do echo "wait fbit start"; sleep 1; done

    IDENT="metamodel-par"
    POD_ID=$(hostname | awk -F"-" {'print $(NF-1)"-"$NF'})
    RUN_SCRIPT="celery --app=metaloader_rest_api.celery_tasks.app worker --queues=parallel --pool=prefork --concurrency=4"
    { PYTHONUNBUFFERED=1 stdbuf -o0 $RUN_SCRIPT $@ 2>&1 | sed -u "s/^/{{ .Values.namespace }}CeHdLm{{ .Values.settings.instance }}CeHdLm${POD_ID}CeHdLm${IDENT}CeHdLm/g"; echo $? > /tmp/exc; } | tee >(nc 127.0.0.1 1212) | stdbuf -o0 sed -u 's/.*CeHdLm//g'
    exit $(cat /tmp/exc)

  run-seq.sh: |
    #!/bin/bash
    while [ $(nc 127.0.0.1 1212 -zv &> /dev/null; echo $?) -ne 0 ]; do echo "wait fbit start"; sleep 1; done

    IDENT="metamodel-seq"
    POD_ID=$(hostname | awk -F"-" {'print $(NF-1)"-"$NF'})
    RUN_SCRIPT="celery --app=metaloader_rest_api.celery_tasks.app worker --queues=sequential --pool=prefork --concurrency=1"
    { PYTHONUNBUFFERED=1 stdbuf -o0 $RUN_SCRIPT $@ 2>&1 | sed -u "s/^/{{ .Values.namespace }}CeHdLm{{ .Values.settings.instance }}CeHdLm${POD_ID}CeHdLm${IDENT}CeHdLm/g"; echo $? > /tmp/exc; } | tee >(nc 127.0.0.1 1212) | stdbuf -o0 sed -u 's/.*CeHdLm//g'
    exit $(cat /tmp/exc)

  fluent-bit.conf: |
    [SERVICE]
      storage.path /tmp/buffer
      storage.sync full
      storage.checksum on
      storage.backlog.mem_limit 100MB

    [INPUT]
      Name        tcp
      Listen      0.0.0.0
      Port        1212
      Tag         SERVICE
      Format      none
      Buffer_Size 50000
      Chunk_Size  50000

    [INPUT]
      Name   tcp
      Listen 0.0.0.0
      Port   5170
      Tag    VAULT
      Format none

    [OUTPUT]
      Name        kafka
      Match       SERVICE
      Brokers     {{ regexReplaceAll ";" ( regexReplaceAll "kafka://" $.Values.kafkaLogs.connStr "" ) "," }}
      Topics      {{ $.Values.log.processors.srvLogs.topic }}
      {{- if hasKey $.Values.kafkaLogs "tls" }}
      rdkafka.security.protocol        SSL
      rdkafka.ssl.key.location         /kf-tls/kf-cl.key
      rdkafka.ssl.certificate.location /kf-tls/kf-cl.crt
      rdkafka.ssl.ca.location          /etc/ssl/certs/ca-certificates.crt
      {{- end }}                                                                                                                                                                                                                            
      # Name file                                                                                                                                                                                                                        
      # Match SERVICE                                                                                                                                                                                                                  
      # File metamodel.log                                                                                                                                                                                                               
      # Path /tmp/                                                                                                                                                                                                                       
      # Mkdir true

    [OUTPUT]
      Name        kafka
      Match       VAULT
      storage.total_limit_size  5M
      Brokers     {{ regexReplaceAll ";" ( regexReplaceAll "kafka://" $.Values.kafkaLogs.connStr "" ) "," }}
      Topics      {{ $.Values.log.processors.srvMetrics.topic }}
      {{- if hasKey $.Values.kafkaLogs "tls" }}
      rdkafka.security.protocol        SSL
      rdkafka.ssl.key.location         /kf-tls/kf-cl.key
      rdkafka.ssl.certificate.location /kf-tls/kf-cl.crt
      rdkafka.ssl.ca.location          /etc/ssl/certs/ca-certificates.crt
      {{- end }}
    
    [OUTPUT]
      Name    stdout
      Match   *
...
{{- end }}
