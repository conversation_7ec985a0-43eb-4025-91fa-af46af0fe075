{{- if ((.Values.metamodel).url) }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: metamodel-worker-par
  namespace: {{ .Values.namespace }}
  labels:
    app: metamodel-worker-par-app
spec:
  replicas: 1
  selector:
    matchLabels:
      app: metamodel-worker-par-app
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: metamodel-worker-par-app
    spec:
      containers:
      - image: {{ .Values.repo.server }}/{{ .Values.repo.suffix }}/metamodel-web:{{ .Values.metamodel.version }}
        name: worker-par-app
        command: ["bash", "-c", "/run-par.sh"]
        envFrom:
        - configMapRef:
            name: dtpl-cm-metamodel-config
        - secretRef:
            name: dtpl-secret-metamodel-config
        readinessProbe:
          exec:
            command:
            - ls
            - /
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          {{- $LIMITS := default $.Values.resources ($.Values.metamodel) }}
          {{- with $LIMITS }}
          limits:
            cpu: 750m
            memory: 1Gi
          requests:
            cpu: 375m
            memory: 512Mi
          {{- end }}
        volumeMounts:
          - name: fluent-config
            mountPath: /run-par.sh
            subPath: run-par.sh
      - image: {{ .Values.repo.server }}/{{ .Values.repo.suffix }}/metamodel-fluentbit:{{ .Values.metamodel.version }}
        name: par-fluent
        volumeMounts:
          {{- if hasKey $.Values.kafkaLogs "tls" }}
          - name: kf-tls
            mountPath: /kf-tls/
          {{- end }}
          - name: fluent-config
            mountPath: /fluent-bit/etc/fluent-bit.conf
            subPath: fluent-bit.conf
        readinessProbe:
          exec:
            command:
            - ls
            - /
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          {{- $LIMITS := default $.Values.resources ($.Values.metamodel) }}
          {{- with $LIMITS }}
          limits:
            cpu: {{ .limits.cpu | quote }}
            memory: {{ .limits.memory | quote }}
          requests:
            cpu: {{ .requests.cpu | quote }}
            memory: {{ .requests.memory | quote }}
          {{- end }}
      volumes:
      {{- if hasKey $.Values.kafkaLogs "tls" }}
      - name: kf-tls
        secret:
          secretName: dtpl-secret-metamodel-certs-kf-tls
          defaultMode: 0400
      {{- end }}
      - name: fluent-config
        configMap:
          name: dtpl-cm-metamodel-fluent-files
          items:
          - key: run.sh
            path: run.sh
            mode: 0755
          - key: run-par.sh
            path: run-par.sh
            mode: 0755
          - key: fluent-bit.conf
            path: fluent-bit.conf
      imagePullSecrets:
      - name: dtpl-secret-metamodel-repo
...
{{- end }}      

