{{- if ((.Values.metamodel).url) }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: metamodel-redis
  namespace: {{ .Values.namespace }}
  labels:
    app: metamodel-redis-app
spec:
  replicas: 1
  selector:
    matchLabels:
      app: metamodel-redis-app
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: metamodel-redis-app
    spec:
      containers:
      - image: {{ .Values.repo.server }}/{{ .Values.repo.suffix }}/metamodel-redis:{{ .Values.metamodel.version }}
        name: redis-app
        ports:
        - containerPort: 6379
        volumeMounts:
        - mountPath: /data
          name: redis-storage
        livenessProbe:
          failureThreshold: 3
          initialDelaySeconds: 30
          periodSeconds: 10
          successThreshold: 1
          tcpSocket:
            port: 6379
          timeoutSeconds: 1
        readinessProbe:
          exec:
            command:
            - ls
            - /
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          {{- $LIMITS := default $.Values.resources ($.Values.metamodel) }}
          {{- with $LIMITS }}
          limits:
            cpu: {{ .limits.cpu | quote }}
            memory: {{ .limits.memory | quote }}
          requests:
            cpu: {{ .requests.cpu | quote }}
            memory: {{ .requests.memory | quote }}
          {{- end }}
      volumes:
      - name: redis-storage
        emptyDir: {}
        mode: 0755
      imagePullSecrets:
      - name: dtpl-secret-metamodel-repo
---
apiVersion: v1
kind: Service
metadata:
  name: metamodel-redis
  namespace: {{ .Values.namespace }}
spec:
  clusterIP: None
  selector:
    app: metamodel-redis-app
  ports:
    - protocol: TCP
      port: 6379
      targetPort: 6379
...
{{- end}}