{{- if ((.Values.metamodel).url) }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: dtpl-cm-metamodel-config
  namespace: {{ .Values.namespace }}
data:
  CEH_METAMODEL__DB__HOST: {{ .Values.pg.mon.host | quote }}
  CEH_METAMODEL__DB__LOGIN: {{ .Values.pg.mon.roles.loader.user | quote }}
  CEH_METAMODEL__DB__DATABASE: {{ .Values.pg.mon.database | quote }}
  CEH_METAMODEL__DB__PORT: {{ .Values.pg.mon.port | quote }}
  AF_USERNAME: {{ .Values.metamodel.airflow.user | quote }}
  CEH_DB_NAME: {{ .Values.adb.main.database | quote }}
  CEH_DB_HOST: {{ .Values.adb.main.host | quote }}
  CEH_DB_USER: {{ .Values.adb.main.roles.srv.user | quote }}
  CEH_DB_PORT: {{ .Values.adb.main.port | quote }}
  CELERY_BROKER_URL: "redis://metamodel-redis:6379/0"
  CE_BROKER_URL: "redis://metamodel-redis:6379/0"
...
{{- end }}
