{{- if ((.Values.log).processors) }}
{{- if hasKey .Values.kafkaLogs "tls" }}
---
apiVersion: acm.clearwayintegration.com/v1beta1
kind: AutoCertificate
metadata:
  name: dtpl-ac-metamodel-certs-kf-tls
  namespace: {{ .Values.namespace }}
spec:
  apdCode: "0029"
  risCode: dtpl
  risId: "1480"
  appName: kafka
  certificateSecret: dtpl-secret-metamodel-certs-kf-tls
  certificateFile: kf-cl.crt
  renew: true
  duration: 240h
  dn: CN=APD29.00-1480-kafka-{{ .Values.settings.instance | lower }}-client-metamodel
  hosts:
    - APD29.00-1480-kafka-{{ $.Values.settings.instance | lower }}-client-metamodel{{ (default "" ($.Values.settings.domain)) | lower }}
  extKeyUsages:
  - ClientAuth
...
{{- end }}
{{- end }}