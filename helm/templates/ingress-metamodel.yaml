{{- if ((.Values.metamodel).url) }}
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: dtpl-metamodel-ingress
  namespace: {{ .Values.namespace }}
  annotations:
    haproxy.router.openshift.io/timeout: "600"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "600"
spec:
  {{- if not (empty ($.Values.ingressClassName)) }}
  ingressClassName: {{ $.Values.ingressClassName }}
  {{- end }}
  {{- with .Values.metamodel.url }}
  rules:
    - host: {{ . }}
      http:
        paths:
          - pathType: Prefix
            path: /
            backend:
              service:
                name: metamodel-service
                port:
                  number: 80
  {{- if hasKey $.Values "tls" }}
  tls:
    - hosts: 
        - {{ . }}
      secretName: dtpl-secret-metamodel-certs-tls
  {{- end }}
  {{- end }}
...
{{- end }}
