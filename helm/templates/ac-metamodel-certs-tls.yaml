{{- if ((.Values.metamodel).url) }}
{{- if hasKey .Values "tls" }}
{{- if or (not .Values.tls.crt) (not .Values.tls.key) }}
apiVersion: acm.clearwayintegration.com/v1beta1
kind: AutoCertificate
metadata:
  name: dtpl-ac-metamodel-certs-tls
  namespace: {{ .Values.namespace }}
spec:
  apdCode: "0029"
  risCode: dtpl
  risId: "1480"
  appName: dtpl-metamodel-app
  dn: "CN={{ .Values.metamodel.url }}"
  certificateSecret: dtpl-secret-metamodel-certs-tls
  renew: true
  duration: 240h
  hosts:
  {{- with .Values.metamodel.url }}
  - {{ . }}
  {{- end }}
  extKeyUsages:
  - ServerAuth
{{- end }}
{{- end }}
{{- end }}
