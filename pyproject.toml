[tool.poetry]
name = "metamodel-loader"
version = "0.1.0"
description = ""
authors = []
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.9"
fastapi = "^0.111.0"
pydantic = "^2.8.2"
psycopg2-binary = "^2.9.9"
uvicorn = "^0.30.1"
sqlalchemy = "^2.0.31"
structlog = "21.5.0"
celery = { version = "^5.4.0", extras = ["redis"] }
openpyxl = "^3.1.2"
pandas = "^2.2.3"
prometheus-client = "^0.21.1"
python-jose = "^3.3.0"
pyyaml = "^6.0.2"
pyxlsb = "1.0.10"
orjson = "^3.10.16"
httpx = "^0.28.1"

[tool.poetry.group.dev.dependencies]
ruff = "^0.5.1"
pre-commit = "^3.5.0"
python-dotenv = "^1.0.1"
pytest = "^8.3.2"
pytest-dotenv = "^0.5.2"
pytest-mock = "^3.14.0"
cryptography = "^43.0.1"
polyfactory = "^2.22.0"
requests = "^2.32.4"
docker = "^7.1.0"

[tool.pytest.ini_options]
addopts = [
    "-ra",  # Показывает детальную инфую о failed, skipped, xfailed тестах
    "-q",  # Скрывает вывод succeeded тестов
    "--ignore=docker_/airflow/dags/test_dag.py"
]
pythonpath = ["src"]
testpaths = ["tests"]
env_files = [".env", "tests/.env", "tests/.env.dev"]
log_cli = false  # Не логируем через пайтест, чтобы не дублировать логи от самого приложения
filterwarnings = [
    "ignore::pydantic.warnings.PydanticDeprecatedSince20",
    "ignore::UserWarning:pydantic.*",
    "ignore::PendingDeprecationWarning:starlette.*",
]

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
