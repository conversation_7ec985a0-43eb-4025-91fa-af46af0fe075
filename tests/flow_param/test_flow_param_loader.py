import os

from metaloader_rest_api.flow_param.flow_param_loader import load_flow_param_pass
from metaloader_rest_api.yaml.yaml_loader import load_yaml
from pytest import fixture


def test_load_flow_param_pass_with_trigger_dag_operator(flow_test_15):
    flow_name, flow_content = flow_test_15
    expected = [
        (flow_name, "p_algoritm", "test_alg_test_table_15"),
    ]
    actual = list(load_flow_param_pass(flow_name, flow_content))

    assert expected == actual


def test_load_flow_param_pass_with_sequential_trigger_dag_operator(flow_test_16):
    flow_name, flow_content = flow_test_16
    expected = [
        (flow_name, "instance_id", 1983),
        (flow_name, "src_version_id", 2956),
        (flow_name, "stable_version_id", 3045),
        (flow_name, "init_load_flg", True),
        (flow_name, "algorithm_name", "test_alg_test_table_16"),
    ]
    actual = list(load_flow_param_pass(flow_name, flow_content))

    assert expected == actual


def test_load_flow_param_pass_without_operator(flow_test_17):
    flow_name, flow_content = flow_test_17
    expected = []
    actual = list(load_flow_param_pass(flow_name, flow_content))

    assert expected == actual


def test_load_flow_param_pass_with_trigger_job_step(flow_test_18):
    flow_name, flow_content = flow_test_18

    expected = [
        (flow_name, "src_version_id", 223),
        (flow_name, "common_version_id", 123),
        (flow_name, "init_load_flg", False),
        (flow_name, "algorithm_uid", "test_alg_test_table_18"),
    ]
    actual = list(load_flow_param_pass(flow_name, flow_content))

    assert expected == actual


def test_load_flow_param_pass_with_sequential_trigger_dag_step(flow_test_19):
    flow_name, flow_content = flow_test_19

    expected = [
        (flow_name, "src_version_id", 223),
        (flow_name, "common_version_id", 123),
        (flow_name, "init_load_flg", False),
        (flow_name, "algorithm_name", "test_alg_test_table_19"),
    ]
    actual = list(load_flow_param_pass(flow_name, flow_content))

    assert expected == actual


def test_load_flow_param_pass_with_builder(flow_test_20):
    flow_name, flow_content = flow_test_20

    expected = [
        (flow_name, "init_load_flg", False),
        (flow_name, "algorithm_uid", "test_alg_test_table_20"),
    ]
    actual = list(load_flow_param_pass(flow_name, flow_content))

    assert expected == actual


def load_flow_data(path, file_name):
    yaml_file_path = os.path.join(path, f"{file_name}.yaml")

    with open(yaml_file_path, "r", encoding="utf-8") as file:
        return file_name, load_yaml(file.read())


@fixture(scope="function")
def flow_test_15(yaml_rdv_idl_path):
    return load_flow_data(yaml_rdv_idl_path, "flow_test_15")


@fixture(scope="function")
def flow_test_16(yaml_rdv_idl_path):
    return load_flow_data(yaml_rdv_idl_path, "flow_test_16")


@fixture(scope="function")
def flow_test_17(yaml_rdv_idl_path):
    return load_flow_data(yaml_rdv_idl_path, "flow_test_17")


@fixture(scope="function")
def flow_test_18(yaml_rdv_idl_path):
    return load_flow_data(yaml_rdv_idl_path, "flow_test_18")


@fixture(scope="function")
def flow_test_19(yaml_rdv_idl_path):
    return load_flow_data(yaml_rdv_idl_path, "flow_test_19")


@fixture(scope="function")
def flow_test_20(yaml_rdv_idl_path):
    return load_flow_data(yaml_rdv_idl_path, "flow_test_20")
