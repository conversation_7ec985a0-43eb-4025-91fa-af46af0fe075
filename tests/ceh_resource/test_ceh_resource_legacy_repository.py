from typing import Callable

from helpers import date_time
from metaloader_rest_api.ceh_resource import ceh_resource_legacy_repository
from metaloader_rest_api.ceh_resource.ceh_resource_legacy_model import (
    ResourceType,
)
from metaloader_rest_api.ceh_resource.ceh_resource_legacy_repository import (
    effective_date,
    resource,
)
from metaloader_rest_api.common_model import FIRST_DATE, LAST_DATE
from metaloader_rest_api.helpers import JsonDict
from pytest import fixture, mark, param, raises
from sqlalchemy import bindparam
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.exc import NoResultFound
from sqlalchemy.orm import Session
from table_view import TableViewBuilder


def ceh_resource_factory(name: str) -> JsonDict:
    return {
        "resource_cd": name,
    }


def uni_resource_factory(name: str) -> JsonDict:
    return {
        **ceh_resource_factory(name),
        "connections": ["connection"],
    }


def kafka_resource_factory(name: str) -> JsonDict:
    return {
        **uni_resource_factory(name),
        "ods_source_object": "object",
        "ods_object": ["queue"],
    }


@mark.parametrize(
    "primary_factory,secondary_factory_1,secondary_factory_2,resource_type",
    [
        param(
            ceh_resource_factory,
            uni_resource_factory,
            kafka_resource_factory,
            ResourceType.CEH,
            id="with ceh resource",
        ),
        param(
            uni_resource_factory,
            ceh_resource_factory,
            kafka_resource_factory,
            ResourceType.UNI,
            id="with uni resource",
        ),
        param(
            kafka_resource_factory,
            ceh_resource_factory,
            uni_resource_factory,
            ResourceType.KAFKA,
            id="with kafka resource",
        ),
    ],
)
def test_names(
    primary_factory: Callable[[str], JsonDict],
    secondary_factory_1: Callable[[str], JsonDict],
    secondary_factory_2: Callable[[str], JsonDict],
    resource_type: ResourceType,
    db_session: Session,
    table: TableViewBuilder,
):
    # fmt: off
    table.data(
        (
            (1, "resource_1", True, True, primary_factory("resource_1"), 1, date_time(2020, 1, 1), LAST_DATE, False),
            (2, "resource_2", True, True, primary_factory("resource_2"), 1, date_time(2020, 1, 1), LAST_DATE, False),
            (3, "resource_3", True, True, primary_factory("resource_3"), 1, date_time(2020, 1, 1), LAST_DATE, True),
            (4, "resource_4", True, True, secondary_factory_1("resource_4"), 1, date_time(2020, 1, 1), LAST_DATE, False),
            (5, "resource_5", True, True, secondary_factory_2("resource_5"), 1, date_time(2020, 1, 1), LAST_DATE, False),
        )
    ).bind_params([
        bindparam("resource_json", type_=JSONB(none_as_null=True))
    ]).insert()
    # fmt: on

    assert ceh_resource_legacy_repository.names(
        session=db_session,
        type=resource_type,
        effective_date=FIRST_DATE,
        offset=0,
        limit=100,
    ) == ["resource_1", "resource_2", "resource_3"]


@mark.parametrize(
    "primary_factory,secondary_factory_1,secondary_factory_2,resource_type",
    [
        param(
            ceh_resource_factory,
            uni_resource_factory,
            kafka_resource_factory,
            ResourceType.CEH,
            id="with ceh resource",
        ),
        param(
            uni_resource_factory,
            ceh_resource_factory,
            kafka_resource_factory,
            ResourceType.UNI,
            id="with uni resource",
        ),
        param(
            kafka_resource_factory,
            ceh_resource_factory,
            uni_resource_factory,
            ResourceType.KAFKA,
            id="with kafka resource",
        ),
    ],
)
def test_names_increment(
    primary_factory: Callable[[str], JsonDict],
    secondary_factory_1: Callable[[str], JsonDict],
    secondary_factory_2: Callable[[str], JsonDict],
    resource_type: ResourceType,
    db_session: Session,
    table: TableViewBuilder,
):
    effective_date = date_time(2020, 1, 1)
    # fmt: off
    table.data(
        (
            (1, "resource_1", True, True, primary_factory("resource_1"), 1, effective_date, LAST_DATE, False),
            (2, "resource_2", True, True, primary_factory("resource_2"), 1, date_time(2020, 10, 1), LAST_DATE, False),
            (3, "resource_3", True, True, primary_factory("resource_3"), 1, date_time(2020, 10, 1), LAST_DATE, True),
            (4, "resource_4", True, True, secondary_factory_1("resource_4"), 1, date_time(2020, 10, 1), LAST_DATE, False),
            (5, "resource_5", True, True, secondary_factory_2("resource_5"), 1, date_time(2020, 10, 1), LAST_DATE, False),
        )
    ).bind_params([
        bindparam("resource_json", type_=JSONB(none_as_null=True))
    ]).insert()
    # fmt: on

    assert ceh_resource_legacy_repository.names(
        session=db_session,
        effective_date=effective_date,
        type=resource_type,
        offset=0,
        limit=100,
    ) == ["resource_2", "resource_3"]


@mark.parametrize(
    "factory,resource_type",
    [
        param(
            ceh_resource_factory,
            ResourceType.CEH,
            id="with ceh resource",
        ),
        param(
            uni_resource_factory,
            ResourceType.UNI,
            id="with uni resource",
        ),
        param(
            kafka_resource_factory,
            ResourceType.KAFKA,
            id="with kafka resource",
        ),
    ],
)
def test_get_as_of(
    factory: Callable[[str], JsonDict],
    resource_type: ResourceType,
    db_session: Session,
    table: TableViewBuilder,
):
    date = date_time(2020, 1, 1)

    # fmt: off
    table.data(
        ((1, "resource_1", True, True, factory("resource_1"), 1, date, LAST_DATE, False),)
    ).bind_params([
        bindparam("resource_json", type_=JSONB(none_as_null=True))
    ]).insert()
    # fmt: on

    assert date == effective_date(session=db_session, type=resource_type)

    date = date_time(2020, 12, 1)
    # fmt: off
    table.data(
        ((2, "resource_2", True, True, factory("resource_1"), 1, date, LAST_DATE, True),)
    ).bind_params([
        bindparam("resource_json", type_=JSONB(none_as_null=True))
    ]).insert()
    # fmt: on

    assert date == effective_date(session=db_session, type=resource_type)


@mark.parametrize(
    "factory",
    [
        param(
            ceh_resource_factory,
            id="with ceh resource",
        ),
        param(
            uni_resource_factory,
            id="with uni resource",
        ),
        param(
            kafka_resource_factory,
            id="with kafka resource",
        ),
    ],
)
def test_resource_if_exists(
    factory: Callable[[str], JsonDict],
    db_session: Session,
    table: TableViewBuilder,
):
    # fmt: off
    table.data(
        ((1, "resource_1", True, True, factory("resource_1"), 1, date_time(2020, 1, 1), LAST_DATE, False),)
    ).bind_params([
        bindparam("resource_json", type_=JSONB(none_as_null=True))
    ]).insert()
    # fmt: on

    assert resource(db_session, "resource_1") == factory("resource_1")


@mark.parametrize(
    "factory",
    [
        param(
            ceh_resource_factory,
            id="with ceh resource",
        ),
        param(
            uni_resource_factory,
            id="with uni resource",
        ),
        param(
            kafka_resource_factory,
            id="with kafka resource",
        ),
    ],
)
def test_get_resource_with_name_if_exists_and_deleted(
    factory: Callable[[str], JsonDict],
    db_session: Session,
    table: TableViewBuilder,
):
    # fmt: off
    table.data(
        ((1, "resource_1", True, True, factory("resource_1"), 1, date_time(2020, 1, 1), LAST_DATE, True),)
    ).bind_params([
        bindparam("resource_json", type_=JSONB(none_as_null=True))
    ]).insert()
    # fmt: on

    assert resource(db_session, "resource_1") is None


def test_get_resource_with_name_if_not_exists(
    db_session: Session,
    table: TableViewBuilder,
):
    with raises(NoResultFound):
        assert resource(db_session, "resource_1") == (None, None)


@fixture(scope="function")
def table(
    db_session: Session,
    table_view_builder,
) -> TableViewBuilder:
    table = (
        table_view_builder()
        .table("metamodel.bridge_resource")
        .columns(
            "resource_rk",
            "resource_cd",
            "is_readonly_flg",
            "is_maintenance_flg",
            "resource_json",
            "version_rk",
            "effective_from_dttm",
            "effective_to_dttm",
            "deleted_flg",
        )
    )
    table.truncate()
    return table
