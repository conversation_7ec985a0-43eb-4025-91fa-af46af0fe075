import argparse
from pathlib import Path

import structlog
from metaloader_rest_api.flow_resource.flow_resource_processor_unit_rdv import (
    RdvFlowResourceProcessorUnit,
)
from metaloader_rest_api.local_file_profider.local_file_provider import (
    LocalFileProvider,
)
from metaloader_rest_api.yaml.yaml_loader import load_yaml

WORKFLOWS_PATH = Path("general_ledger") / "src_rdv" / "schema" / "work_flows"


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--ceh-etl-root", required=True, type=Path)
    args = parser.parse_args()

    logger = structlog.get_logger()

    file_provider = LocalFileProvider(args.ceh_etl_root)
    processor = RdvFlowResourceProcessorUnit(file_provider)

    workflows_dir = args.ceh_etl_root / WORKFLOWS_PATH
    yaml_files = list(workflows_dir.glob("*.yaml"))

    for yaml_file in yaml_files:
        logger.info("processing_workflow", file=yaml_file.name)

        with open(yaml_file, "r", encoding="utf-8") as f:
            flow_yaml = load_yaml(f.read())

        flow_resources = list(
            processor.process(
                flow_id=-1,
                flow_name=yaml_file.stem,
                flow=flow_yaml,
            )
        )

        logger.info(
            "workflow_processed",
            file=yaml_file.name,
            flow_resources_count=len(flow_resources),
        )


if __name__ == "__main__":
    main()
