import json
import os
import sys
import time
import warnings

import requests
import urllib3
from polyfactory.factories.pydantic_factory import ModelFactory
from requests import Response

sys.path.append(os.path.abspath("src"))
warnings.filterwarnings("ignore", category=UserWarning)
from metaloader_rest_api.resource_schemas import CehResource, UniResource  # noqa: E402
from metaloader_rest_api.schemas import ResourceType  # noqa: E402

BASE_URL = os.getenv("CEH_METAMODEL_BASE_URL", "http://localhost:8000/v1")
BODY_LINES_PRINT_THRESHOLD = 20

session = requests.Session()
session.verify = False
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class CehResourceFactory(ModelFactory[CehResource]):
    __model__ = CehResource


class UniResourceFactory(ModelFactory[UniResource]):
    __model__ = UniResource


def handle_response(response: Response):
    print(f"{response.request.method} {response.request.url}")
    print(f"{response.status_code=}")

    try:
        body = json.dumps(json.loads(response.text), indent=4)
        if len(body_lines := body.split("\n")) > BODY_LINES_PRINT_THRESHOLD:
            body_lines.append("...")
            body = "\n".join(body_lines[:BODY_LINES_PRINT_THRESHOLD])
    except Exception:
        body = response.text
    print(body, end="\n\n")

    if 400 <= response.status_code <= 599:
        sys.exit(1)


def create_resource_action(tx_id, resource_type, action):
    definition = (
        CehResourceFactory.build()
        if resource_type == ResourceType.CEH
        else UniResourceFactory.build()
    )

    payload = {
        "tx_uid": tx_id,
        "resource_cd": definition.resource_cd,
        "resource_type": resource_type.value,
    }
    if action != "deleted":
        payload["definition"] = definition.model_dump(mode="json")

    endpoint = f"{BASE_URL}/resources/actions/{action}"

    r = session.post(
        endpoint,
        headers={
            "Content-Type": "application/json",
        },
        json=payload,
    )
    handle_response(r)


#
# Create a transaction
#
r = session.post(
    f"{BASE_URL}/resources/tx",
    headers={
        "Content-Type": "application/json",
    },
    json={
        "timeout": 3,
        "release_metadata": {
            "release_num": "51.0.2",
            "release_desc": "Resource transaction test",
            "release_date": "2025-04-04",
            "effective_from_date": "2025-04-04",
            "release_module": "core",
            "git_revision_hash": "a82ec6b",
        },
    },
)
handle_response(r)

tx_id = r.json()


#
# Get transaction info
#
handle_response(
    session.get(
        f"{BASE_URL}/resources/tx/{tx_id}",
    )
)


#
# Create resource actions
#
create_resource_action(tx_id, ResourceType.CEH, "added")
create_resource_action(tx_id, ResourceType.UNI, "modified")
create_resource_action(tx_id, ResourceType.CEH, "deleted")


#
# Commit the transaction
#
r = session.post(
    f"{BASE_URL}/resources/tx/{tx_id}/commit",
    headers={
        "Content-Type": "application/json",
    },
    json={
        "mode": "snapshot",
    },
)
handle_response(r)


#
# Check transaction status
#
time.sleep(1)
handle_response(
    session.get(
        f"{BASE_URL}/resources/tx/{tx_id}",
    )
)
