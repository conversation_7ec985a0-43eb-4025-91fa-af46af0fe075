{"endpoint": "added", "resource_cd": "resource_a5", "definition": {"resource_cd": "resource_a5", "resource_desc": "Resource already existed, deleted_flg=true, undeleted with different attributes", "status": {"is_readonly": false, "is_maintenance": false}, "tags": ["test", "case_a5", "to_be_deleted"], "features": {"source_system": "source_3"}, "datasets": [{"name": "table_5", "schema_name": "schema_1"}]}}