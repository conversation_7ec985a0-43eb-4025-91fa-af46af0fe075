import argparse
import itertools
import json
import os
import re
import sys
import time
from collections import OrderedDict, defaultdict
from datetime import datetime
from pathlib import Path
from typing import Any, Literal, Optional
from uuid import UUID

import requests
import structlog
import urllib3
from pydantic import BaseModel
from sqlalchemy import Connection, create_engine, text

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

logger = structlog.get_logger()

DB_URL = os.environ.get(
    "CEH_METAMODEL_DB_URL",
    "postgresql://d0_mon_loader_tec:d0_mon_loader_tec@localhost:5432/d0_mon",
)
API_BASE_URL = os.environ.get(
    "CEH_METAMODEL_BASE_URL",
    "http://localhost:8000/v1",
)
DATA_DIR = Path(__file__).parent / "data" / "resources_increment"

ResourceActionType = Literal["added", "modified", "deleted"]


class TestPayload(BaseModel):
    endpoint: ResourceActionType
    resource_cd: str
    definition: Optional[dict[str, Any]]


class ResourceLoadError(Exception):
    pass


def extract_transaction_id(filename: str) -> str:
    match = re.match(r"tx(\d+)_", filename)
    if not match:
        raise ValueError(f"Invalid filename format: {filename}")
    return match.group(1)


def load_and_validate_test_payloads(
    data_dir: Path,
) -> OrderedDict[str, list[TestPayload]]:
    if not data_dir.exists():
        logger.error("Data directory does not exist", path=str(data_dir))
        raise ResourceLoadError("Data directory does not exist")

    files = data_dir.glob("*.json")
    if not files:
        logger.error("No JSON files found in data directory", path=str(data_dir))
        raise ResourceLoadError("No JSON files found in data directory")

    grouped_payloads: OrderedDict[str, list[TestPayload]] = OrderedDict()
    file_groups: dict[str, list[Path]] = defaultdict(list)

    for file_path in files:
        tx_id = extract_transaction_id(file_path.name)
        file_groups[tx_id].append(file_path)

    for tx_id in sorted(file_groups.keys(), key=int):
        payloads = []
        for file_path in file_groups[tx_id]:
            with open(file_path, "r") as f:
                data = json.load(f)
                payload = TestPayload(**data)
                payloads.append(payload)

        if payloads:
            grouped_payloads[tx_id] = payloads

    return grouped_payloads


def create_transaction(session: requests.Session) -> UUID:
    url = f"{API_BASE_URL}/resources/tx"
    payload = {
        "release_metadata": {
            "release_num": "0.0.0",
            "release_desc": "Test loading resources increment",
            "release_date": "2000-01-01",
            "effective_from_date": "2000-01-01",
            "release_module": "ETL-SCALE-core",
            "git_revision_hash": "script",
        },
    }
    response = session.post(url, json=payload)
    response.raise_for_status()
    return UUID(response.json())


def process_resource_payload(
    session: requests.Session,
    tx_uid: UUID,
    payload: TestPayload,
) -> None:
    logger.info(
        "Processing resource",
        resource_cd=payload.resource_cd,
        endpoint=payload.endpoint,
    )

    if payload.endpoint in ("added", "modified"):
        create_resource_action_added_or_modified(
            session, tx_uid, payload.resource_cd, payload.endpoint, payload.definition
        )
    elif payload.endpoint == "deleted":
        create_resource_action_deleted(session, tx_uid, payload.resource_cd)
    else:
        raise


def create_resource_action_added_or_modified(
    session: requests.Session,
    tx_uid: UUID,
    resource_cd: str,
    endpoint: ResourceActionType,
    definition: dict[str, Any],
) -> None:
    url = f"{API_BASE_URL}/resources/actions/{endpoint}"
    payload = {
        "tx_uid": str(tx_uid),
        "resource_cd": resource_cd,
        "resource_type": "ceh",  # Assuming all resources are CEH type
        "definition": definition,
    }
    response = session.post(url, json=payload)
    response.raise_for_status()


def create_resource_action_deleted(
    session: requests.Session, tx_uid: UUID, resource_cd: str
) -> None:
    url = f"{API_BASE_URL}/resources/actions/deleted"
    payload = {"tx_uid": str(tx_uid), "resource_cd": resource_cd}
    response = session.post(url, json=payload)
    response.raise_for_status()


def commit_transaction(session: requests.Session, tx_uid: UUID) -> None:
    url = f"{API_BASE_URL}/resources/tx/{tx_uid}/commit"
    payload = {"mode": "incremental"}
    response = session.post(url, json=payload)
    response.raise_for_status()


def rollback_transaction(session: requests.Session, tx_uid: UUID) -> None:
    url = f"{API_BASE_URL}/resources/tx/{tx_uid}/rollback"
    response = session.post(url, json={})
    response.raise_for_status()


def get_transaction_status(
    session: requests.Session,
    tx_id: UUID,
) -> Optional[str]:
    response = session.get(
        url=f"{API_BASE_URL}/resources/tx/{str(tx_id)}",
    )
    response.raise_for_status()
    payload = response.json()
    return payload.get("status")


def process_transaction(
    session: requests.Session,
    tx_file_id: str,
    payloads: list[TestPayload],
) -> UUID:
    log = logger.bind(tx_file_id=tx_file_id)
    log.info("Processing transaction", payload_count=len(payloads))

    tx_uid = None
    try:
        tx_uid = create_transaction(session)

        log = log.bind(tx_uid=str(tx_uid))
        log.info("Created transaction")

        for payload in payloads:
            process_resource_payload(session, tx_uid, payload)

        commit_transaction(session, tx_uid)
        log.info("Committed transaction")

        return tx_uid

    except Exception as err:
        if isinstance(err, requests.exceptions.RequestException):
            log.error("API error", response=err.response.text)

        if tx_uid:
            try:
                rollback_transaction(session, tx_uid)
                log.info("Rolled back transaction")
            except Exception as rollback_error:
                log.error(
                    "Failed to rollback transaction",
                    error=str(rollback_error),
                )

        raise ResourceLoadError(str(err)) from err


def fetch_source_cds(conn: Connection) -> list[str]:
    query = """
    SELECT source_cd
    FROM metamodel.bridge_source
    WHERE
        effective_to_dttm = '2999-12-31 00:00:00.000000 +00:00' AND
        deleted_flg IS FALSE
    LIMIT 1000
    """
    result = conn.execute(text(query)).scalars().all()
    return result


def fetch_table_schema_and_name_pairs(conn: Connection) -> list[tuple[str, str]]:
    query = """
    SELECT schema_name, table_name
    FROM metamodel.bridge_table
    WHERE
        effective_to_dttm = '2999-12-31 00:00:00.000000 +00:00' AND
        deleted_flg IS FALSE
    LIMIT 1000
    """
    result = conn.execute(text(query)).tuples().all()
    return result


def replace_resource_attributes_inplace(
    source_cds: list[str],
    table_schema_and_name_pairs: list[tuple[str, str]],
    grouped_payloads: OrderedDict[str, list[TestPayload]],
) -> None:
    script_launched_at = datetime.now().strftime("%Y%m%d%H%M%S")

    source_cds_iter = iter(source_cds)
    tables_iter = iter(table_schema_and_name_pairs)
    source_cds_mapping = defaultdict(lambda: next(source_cds_iter))
    tables_mapping = defaultdict(lambda: next(tables_iter))

    for payload in itertools.chain.from_iterable(grouped_payloads.values()):
        new_resource_cd = f"test.{payload.resource_cd}.{script_launched_at}"
        payload.resource_cd = new_resource_cd

        resource = payload.definition
        if not resource:
            continue

        resource["resource_cd"] = new_resource_cd

        if (features := resource.get("features")) and "source_system" in features:
            try:
                source_cd_from_file = features["source_system"]
                features["source_system"] = source_cds_mapping[source_cd_from_file]
            except StopIteration:
                raise ResourceLoadError("Not enough source_cds available from the DB")

        datasets = filter(None, resource.get("datasets") or ())
        for dataset in datasets:
            try:
                schema_from_db, name_from_db = tables_mapping[
                    (dataset["schema_name"], dataset["name"])
                ]
                dataset["schema_name"] = schema_from_db
                dataset["name"] = name_from_db
            except StopIteration:
                raise ResourceLoadError(
                    "Not enough table schema-name pairs available from the DB"
                )


def main(replace_resource_attrs: bool, tx_completion_waiting_time: float) -> None:
    grouped_payloads = load_and_validate_test_payloads(DATA_DIR)
    if not grouped_payloads:
        logger.error("No valid transaction files found")
        raise ResourceLoadError("No valid transaction files found")

    logger.info(
        "Found transactions",
        transaction_count=len(grouped_payloads),
        transactions=list(grouped_payloads.keys()),
    )

    if replace_resource_attrs:
        logger.info("Fetching source_cds and table schema-name pairs from the DB")
        with create_engine(DB_URL).connect() as conn:
            source_cds = fetch_source_cds(conn)
            table_schema_and_name_pairs = fetch_table_schema_and_name_pairs(conn)
        replace_resource_attributes_inplace(
            source_cds,
            table_schema_and_name_pairs,
            grouped_payloads,
        )
        logger.info("Replaced test resource attributes in-place")

    session = requests.Session()
    session.verify = False

    tx_uids: dict[str, UUID] = OrderedDict()
    for tx_file_id in grouped_payloads.keys():
        tx_uid = process_transaction(session, tx_file_id, grouped_payloads[tx_file_id])
        tx_uids[tx_file_id] = tx_uid

    logger.info(
        f"Pausing for {tx_completion_waiting_time} sec for transactions to complete ..."
    )
    time.sleep(tx_completion_waiting_time)

    tx_statuses: dict[str, str] = OrderedDict()
    for tx_file_id, tx_uid in tx_uids.items():
        tx_statuses[tx_file_id] = get_transaction_status(session, tx_uid)
    logger.info("Transaction statuses", **tx_statuses)


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--disable-replacing-resource-attrs",
        action=argparse.BooleanOptionalAction,
        default=False,
        help="Replace source systems and tables in test data with real values from the metamodel",
    )
    parser.add_argument(
        "--tx-completion-waiting-time",
        type=float,
        default=3.0,
        help="Time in seconds to wait for transactions to complete",
    )
    args = parser.parse_args()

    logger.info("Starting resource load increment script")
    try:
        main(
            replace_resource_attrs=not args.disable_replacing_resource_attrs,
            tx_completion_waiting_time=args.tx_completion_waiting_time,
        )
        logger.info("Completed resource load increment script")
    except ResourceLoadError as err:
        logger.error("Resource loading failed", error=str(err))
        sys.exit(1)
