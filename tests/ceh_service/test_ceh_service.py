from datetime import datetime, timezone
from uuid import UUID

from metaloader_rest_api.ceh_service import ceh_service_api
from metaloader_rest_api.ceh_service.ceh_service_api import LoadServicesParams
from pytest import fixture
from sqlalchemy import text


def test_init_load_service(
    db_session,
    effective_date_init,
    load_id,
    page_size,
    data_init,
    truncate_service,
    dict_service_type,
    dict_environment,
    dict_code_delivery,
):
    ceh_service_api.load_services(
        session=db_session,
        load_id=load_id,
        effective_date=effective_date_init,
        params=data_init,
    )
    db_session.commit()

    rows = db_session.execute(text("SELECT * FROM metamodel.bridge_service")).fetchall()
    assert len(rows) == 3

    rows = db_session.execute(
        text("SELECT * FROM metamodel.link_service_delivery")
    ).fetchall()
    assert len(rows) == 3

    ceh_service_api.load_services(
        session=db_session,
        load_id=load_id,
        effective_date=effective_date_init,
        params=data_init,
    )
    db_session.commit()

    rows = db_session.execute(text("SELECT * FROM metamodel.bridge_service")).fetchall()
    assert len(rows) == 3

    rows = db_session.execute(
        text("SELECT * FROM metamodel.link_service_delivery")
    ).fetchall()
    assert len(rows) == 3


def test_incr_load_service(
    db_session,
    effective_date_init,
    effective_date_inc,
    load_id,
    page_size,
    data_init,
    data_incr,
    truncate_service,
    dict_service_type,
    dict_environment,
    dict_code_delivery,
):
    ceh_service_api.load_services(
        session=db_session,
        load_id=load_id,
        effective_date=effective_date_init,
        params=data_init,
    )
    db_session.commit()

    rows = db_session.execute(text("SELECT * FROM metamodel.bridge_service")).fetchall()
    assert len(rows) == 3

    rows = db_session.execute(
        text("SELECT * FROM metamodel.link_service_delivery")
    ).fetchall()
    assert len(rows) == 3

    ceh_service_api.load_services(
        session=db_session,
        load_id=load_id,
        effective_date=effective_date_inc,
        params=data_incr,
    )
    db_session.commit()

    rows = db_session.execute(text("SELECT * FROM metamodel.bridge_service")).fetchall()
    assert len(rows) == 6

    rows = db_session.execute(
        text("SELECT * FROM metamodel.link_service_delivery")
    ).fetchall()
    assert len(rows) == 2


@fixture(scope="session")
def effective_date_init() -> datetime:
    return datetime(2025, 1, 1, tzinfo=timezone.utc)


@fixture(scope="session")
def effective_date_inc() -> datetime:
    return datetime(2025, 1, 3, tzinfo=timezone.utc)


@fixture(scope="session")
def load_id() -> UUID:
    return UUID("00000000-0000-0000-0000-000000000000")


@fixture(scope="session")
def page_size() -> int:
    return 3


@fixture(scope="function")
def dict_service_type(db_session) -> None:
    db_session.execute(text("TRUNCATE TABLE dict.dict_service_type"))
    db_session.execute(
        text("""INSERT INTO dict.dict_service_type(service_type_rk
                                                 , service_type_cd
                                                 , service_type_name
                                                 , service_type_desc
                                                 , is_etl_flg
                                                 , is_rdbms_flg
                                                 , is_core_flg) 
                     VALUES (1, 'service_type_cd_1', 'service_type_name_1', 'service_type_desc_1', TRUE, FALSE, FALSE)
                          , (2, 'service_type_cd_3', 'service_type_name_1', 'service_type_desc_1', TRUE, FALSE, FALSE)
        """)
    )
    db_session.commit()


@fixture(scope="function")
def dict_environment(db_session) -> None:
    db_session.execute(text("TRUNCATE TABLE dict.dict_environment"))
    db_session.execute(
        text("""INSERT INTO dict.dict_environment(environment_rk
                                                , environment_cd
                                                , environment_name
                                                , environment_desc
                                                , is_dev_environment
                                                , is_ift_environment
                                                , is_test_environment
                                                , is_prod_environment)
                     VALUES (1, 'environment_cd_1', 'environment_name_1', 'env_desc_1', TRUE, FALSE, FALSE, FALSE)
                          , (2, 'environment_cd_2', 'environment_name_2', 'env_desc_2', TRUE, FALSE, FALSE, FALSE)
        """)
    )
    db_session.commit()


@fixture(scope="function")
def dict_code_delivery(db_session) -> None:
    db_session.execute(text("TRUNCATE TABLE dict.dict_code_delivery"))
    db_session.execute(
        text("""INSERT INTO dict.dict_code_delivery(code_delivery_rk
                                                  , code_delivery_cd
                                                  , code_delivery_name
                                                  , is_ddl_flg
                                                  , is_flow_flg
                                                  , is_config_flg
                                                  , deleted_flg)
                     VALUES (1, 'code_delivery_cd_1', 'code_delivery_name_1', TRUE, TRUE, TRUE, FALSE)
                          , (2, 'code_delivery_cd_2', 'code_delivery_name_2', TRUE, TRUE, TRUE, FALSE)
                          , (3, 'code_delivery_cd_3', 'code_delivery_name_3', TRUE, TRUE, TRUE, FALSE)
        """)
    )
    db_session.commit()


@fixture(scope="function")
def truncate_service(db_session):
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_service"))
    db_session.commit()


@fixture(scope="session")
def data_init() -> LoadServicesParams:
    return LoadServicesParams(
        [
            {
                "service_type_cd": "service_type_cd_1",
                "environment_cd": "environment_cd_1",
                "service_cd": "service_cd_1",
                "service_name": "service_name_1",
                "service_desc": "service_desc_1",
                "service_host_name": "service_host_name_1",
                "service_url": "service_url_1",
                "service_alt_url": "service_alt_url_1",
                "code_delivery_cd": "code_delivery_cd_1",
                "ris_src_id": "ris_src_id_1",
                "ris_src_code": "ris_src_code_1",
            },
            {
                "service_type_cd": "service_type_cd_2",
                "environment_cd": "environment_cd_2",
                "service_cd": "service_cd_2",
                "service_name": "service_name_2",
                "service_desc": "service_desc_2",
                "service_host_name": "service_host_name_2",
                "service_url": "service_url_2",
                "service_alt_url": "service_alt_url_2",
                "code_delivery_cd": "code_delivery_cd_2",
                "ris_src_id": "ris_src_id_2",
                "ris_src_code": "ris_src_code_2",
            },
            {
                "service_type_cd": "service_type_cd_3",
                "environment_cd": "environment_cd_3",
                "service_cd": "service_cd_3",
                "service_name": "service_name_3",
                "service_desc": "service_desc_3",
                "service_host_name": "service_host_name_3",
                "service_url": "service_url_3",
                "service_alt_url": "service_alt_url_3",
                "code_delivery_cd": "code_delivery_cd_3",
                "ris_src_id": "ris_src_id_3",
                "ris_src_code": "ris_src_code_3",
            },
        ]
    )


@fixture(scope="session")
def data_incr() -> LoadServicesParams:
    return LoadServicesParams(
        [
            {
                "service_type_cd": "service_type_cd_1",
                "environment_cd": "environment_cd_1",
                "service_cd": "service_cd_1",
                "service_name": "service_name_1",
                "service_desc": "service_desc_1",
                "service_host_name": "service_host_name_1",
                "service_url": "service_url_1",
                "service_alt_url": "service_alt_url_1",
                "code_delivery_cd": "code_delivery_cd_1",
                "ris_src_id": "ris_src_id_1",
                "ris_src_code": "ris_src_code_1",
            },
            {
                "service_type_cd": "service_type_cd_3",
                "environment_cd": "environment_cd_3",
                "service_cd": "service_cd_3",
                "service_name": "service_name_3",
                "service_desc": "service_desc_3_edit",
                "service_host_name": "service_host_name_3",
                "service_url": "service_url_3",
                "service_alt_url": "service_alt_url_3",
                "code_delivery_cd": "code_delivery_cd_3",
                "ris_src_id": "ris_src_id_3",
                "ris_src_code": "ris_src_code_3",
            },
            {
                "service_type_cd": "service_type_cd_4",
                "environment_cd": "environment_cd_4",
                "service_cd": "service_cd_4",
                "service_name": "service_name_4",
                "service_desc": "service_desc_4",
                "service_host_name": "service_host_name_4",
                "service_url": "service_url_4",
                "service_alt_url": "service_alt_url_4",
                "code_delivery_cd": "code_delivery_cd_4",
                "ris_src_id": "ris_src_id_4",
                "ris_src_code": "ris_src_code_4",
            },
        ]
    )
