import json
from dataclasses import asdict, dataclass, field
from pathlib import Path
from typing import Callable, Iterable, Optional, Protocol, Sequence

from helpers import TableData
from metaloader_rest_api.common_model import LAST_DATE
from metaloader_rest_api.models import ResourceAction, ResourceType
from metaloader_rest_api.resource_repository import ResourceRepository
from pytest import fixture
from sqlalchemy import text
from sqlalchemy.orm import Session

_CURRENT_DIR = Path(__file__).parent
with open(_CURRENT_DIR / "setup.sql", "r") as _f:
    _TEST_SCHEMA_SETUP_DDL = _f.read()

TEST_SCHEMA = "test_resource_repository"
DATE_FROM_MIN = "2000-01-01"
DATE_TO_MAX = str(LAST_DATE)[:10]


@dataclass
class ResourceRecord:
    resource_rk: int
    resource_cd: str
    resource_desc: str = "Foo"
    is_readonly_flg: bool = False
    is_maintenance_flg: bool = False
    tag_list: list[str] = field(default_factory=list)
    resource_json: dict = field(default_factory=dict)
    version_rk: int = 1
    effective_from_dttm: str = DATE_FROM_MIN
    effective_to_dttm: str = DATE_TO_MAX
    deleted_flg: bool = False


@dataclass
class TableRecord:
    table_rk: int
    table_name: str
    schema_name: str
    effective_from_dttm: str = DATE_FROM_MIN
    effective_to_dttm: str = DATE_TO_MAX
    deleted_flg: bool = False


@dataclass
class TableLinkRecord:
    resource_rk: int
    table_rk: int
    effective_from_dttm: str = DATE_FROM_MIN
    effective_to_dttm: str = DATE_TO_MAX
    deleted_flg: bool = False


@dataclass
class SourceRecord:
    source_rk: int
    source_cd: str
    effective_from_dttm: str = DATE_FROM_MIN
    effective_to_dttm: str = DATE_TO_MAX
    deleted_flg: bool = False


@dataclass
class SourceLinkRecord:
    resource_rk: int
    source_rk: int
    effective_from_dttm: str = DATE_FROM_MIN
    effective_to_dttm: str = DATE_TO_MAX
    deleted_flg: bool = False


@dataclass
class StageResourceRecord:
    resource_cd: str
    data_action_type_rk: ResourceAction
    resource_type: Optional[ResourceType] = None
    definition: Optional[dict] = None


class TableReader:
    def __init__(self, session: Session):
        self._session = session

    def _make_table_data_instance(
        self, table_name: str, order_by: Sequence[str] = ()
    ) -> TableData:
        query = f"SELECT * FROM {TEST_SCHEMA}.{table_name}"
        if order_by:
            query += " ORDER BY " + ", ".join(order_by)
        return TableData.from_sqlalchemy_result(
            result=self._session.execute(text(query)),
            stringify_datetime_columns="truncate_to_date",
        )

    def bridge_resource(self, order_by: Sequence[str] = ()) -> TableData:
        return self._make_table_data_instance("bridge_resource", order_by)

    def bridge_table(self, order_by: Sequence[str] = ()) -> TableData:
        return self._make_table_data_instance("bridge_table", order_by)

    def bridge_source(self, order_by: Sequence[str] = ()) -> TableData:
        return self._make_table_data_instance("bridge_source", order_by)

    def link_resource_source(self, order_by: Sequence[str] = ()) -> TableData:
        return self._make_table_data_instance("link_resource_source", order_by)

    def link_resource_table(self, order_by: Sequence[str] = ()) -> TableData:
        return self._make_table_data_instance("link_resource_table", order_by)


@fixture(scope="session")
def setup_test_schema(db_session_factory: Callable[[], Session]) -> None:
    with db_session_factory() as session:
        session.execute(text(_TEST_SCHEMA_SETUP_DDL))
        session.commit()


@fixture
def read_table(db_session: Session) -> TableReader:
    return TableReader(db_session)


@fixture
def resource_repository(db_session: Session, monkeypatch) -> ResourceRepository:
    for attr_name, test_table in [
        ("_TABLE_BRIDGE_RESOURCE", "bridge_resource"),
        ("_TABLE_LINK_RESOURCE_SOURCE", "link_resource_source"),
        ("_TABLE_LINK_RESOURCE_TABLE", "link_resource_table"),
        ("_TABLE_BRIDGE_SOURCE", "bridge_source"),
        ("_TABLE_BRIDGE_TABLE", "bridge_table"),
    ]:
        monkeypatch.setattr(
            ResourceRepository, attr_name, f"{TEST_SCHEMA}.{test_table}"
        )
    return ResourceRepository(db_session)


class ResetTablesFunc(Protocol):
    def __call__(
        self,
        resources_staging: Iterable[StageResourceRecord] = (),
        resources: Iterable[ResourceRecord] = (),
        tables: Iterable[TableRecord] = (),
        table_links: Iterable[TableLinkRecord] = (),
        sources: Iterable[SourceRecord] = (),
        source_links: Iterable[SourceLinkRecord] = (),
    ) -> None: ...


@fixture
def reset_tables(
    db_session: Session,
    setup_test_schema,  # noqa
) -> ResetTablesFunc:
    db_session.rollback()

    db_session.execute(
        text(f"""
        ALTER SEQUENCE {TEST_SCHEMA}.md_seq RESTART WITH 1;
        TRUNCATE TABLE
        {TEST_SCHEMA}.bridge_resource,
        {TEST_SCHEMA}.bridge_source,
        {TEST_SCHEMA}.bridge_table,
        {TEST_SCHEMA}.link_resource_source,
        {TEST_SCHEMA}.link_resource_table,
        {TEST_SCHEMA}.bridge_resource_staging
        CASCADE
        """)
    )
    db_session.commit()

    def _fill_all_tables(
        resources_staging: Iterable[StageResourceRecord] = (),
        resources: Iterable[ResourceRecord] = (),
        tables: Iterable[TableRecord] = (),
        table_links: Iterable[TableLinkRecord] = (),
        sources: Iterable[SourceRecord] = (),
        source_links: Iterable[SourceLinkRecord] = (),
    ) -> None:
        _fill_resources_staging(db_session, resources_staging)
        _fill_resources(db_session, resources)
        _fill_tables(db_session, tables)
        _fill_table_links(db_session, table_links)
        _fill_sources(db_session, sources)
        _fill_source_links(db_session, source_links)
        db_session.commit()

    yield _fill_all_tables


def _fill_resources(db_session: Session, resources: Iterable[ResourceRecord]) -> None:
    params = list(map(asdict, resources))
    for item in params:
        item["resource_json"] = json.dumps(item["resource_json"])

    if not params:
        return

    db_session.execute(
        text(f"""
        INSERT INTO {TEST_SCHEMA}.bridge_resource (resource_rk
                                                 , resource_cd
                                                 , resource_desc
                                                 , is_readonly_flg
                                                 , is_maintenance_flg
                                                 , tag_list
                                                 , resource_json
                                                 , version_rk
                                                 , effective_from_dttm
                                                 , effective_to_dttm
                                                 , deleted_flg)
        VALUES (:resource_rk
              , :resource_cd
              , :resource_desc
              , :is_readonly_flg
              , :is_maintenance_flg
              , :tag_list
              , :resource_json
              , :version_rk
              , :effective_from_dttm
              , :effective_to_dttm
              , :deleted_flg)
        """),
        params,
    )


def _fill_tables(db_session: Session, tables: Iterable[TableRecord]) -> None:
    params = list(map(asdict, tables))

    if not params:
        return

    db_session.execute(
        text(f"""
        INSERT INTO {TEST_SCHEMA}.bridge_table (table_rk
                                              , table_name
                                              , schema_name
                                              , effective_from_dttm
                                              , effective_to_dttm
                                              , deleted_flg)
        VALUES (:table_rk
              , :table_name
              , :schema_name
              , :effective_from_dttm
              , :effective_to_dttm
              , :deleted_flg)
        """),
        params,
    )


def _fill_table_links(
    db_session: Session,
    table_links: Iterable[TableLinkRecord],
) -> None:
    params = list(map(asdict, table_links))

    if not params:
        return

    db_session.execute(
        text(f"""
        INSERT INTO {TEST_SCHEMA}.link_resource_table (resource_rk
                                                     , table_rk
                                                     , effective_from_dttm
                                                     , effective_to_dttm
                                                     , deleted_flg)
        VALUES (:resource_rk
              , :table_rk
              , :effective_from_dttm
              , :effective_to_dttm
              , :deleted_flg)
        """),
        params,
    )


def _fill_sources(db_session: Session, sources: Iterable[SourceRecord]) -> None:
    params = list(map(asdict, sources))

    if not params:
        return

    db_session.execute(
        text(f"""
        INSERT INTO {TEST_SCHEMA}.bridge_source (source_rk
                                               , source_cd
                                               , effective_from_dttm
                                               , effective_to_dttm
                                               , deleted_flg)
        VALUES (:source_rk
              , :source_cd
              , :effective_from_dttm
              , :effective_to_dttm
              , :deleted_flg)
        """),
        params,
    )


def _fill_source_links(
    db_session: Session,
    source_links: Iterable[SourceLinkRecord],
) -> None:
    params = list(map(asdict, source_links))

    if not params:
        return

    db_session.execute(
        text(f"""
        INSERT INTO {TEST_SCHEMA}.link_resource_source (resource_rk
                                                      , source_rk
                                                      , effective_from_dttm
                                                      , effective_to_dttm
                                                      , deleted_flg)
        VALUES (:resource_rk
              , :source_rk
              , :effective_from_dttm
              , :effective_to_dttm
              , :deleted_flg)
        """),
        params,
    )


def _fill_resources_staging(
    db_session: Session,
    staging: Iterable[StageResourceRecord],
) -> None:
    params = [
        {
            "resource_cd": record.resource_cd,
            "data_action_type_rk": record.data_action_type_rk.value,
            "resource_type": record.resource_type and record.resource_type.value,
            "definition": json.dumps(record.definition),
        }
        for record in staging
    ]

    if not params:
        return

    db_session.execute(
        text(f"""
        INSERT INTO {TEST_SCHEMA}.bridge_resource_staging (resource_cd
                                                         , data_action_type_rk
                                                         , resource_type
                                                         , definition)
        VALUES (:resource_cd
              , :data_action_type_rk
              , :resource_type
              , :definition)
        """),
        params,
    )
