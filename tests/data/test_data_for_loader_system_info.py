import uuid
from datetime import datetime, timezone

from metaloader_rest_api.common_model import LAST_DATE

NOW_INIT = datetime(2024, 1, 1, 0, 0, 0, tzinfo=timezone.utc)
NOW_INC_1 = datetime(2024, 1, 1, 3, 0, 0, tzinfo=timezone.utc)
NOW_INC_2 = datetime(2024, 1, 1, 5, 0, 0, tzinfo=timezone.utc)

LOAD_ID_INIT = uuid.UUID("1a000000-0000-0000-0000-000000000000")
LOAD_ID_INC_1 = uuid.UUID("1b000000-0000-0000-0000-000000000000")
LOAD_ID_INC_2 = uuid.UUID("2c000000-0000-0000-0000-000000000000")

INIT_BRIDGE_SOURCE_DATA = [
    {
        "source_cd": "TEST_SRC_1",
        "parent_source_cd": None,
        "short_cd": None,
        "ris_src_id": "101",
        "ris_src_code": "Test_src_1_code",
        "source_desc": "Test_src_1_desc",
        "data_layer_rk": 1,
    },
    {
        "source_cd": "TEST_SRC_2",
        "parent_source_cd": "TEST_SRC_1",
        "short_cd": None,
        "ris_src_id": "201",
        "ris_src_code": "Test_src_2_code",
        "source_desc": "Test_src_2_desc",
        "data_layer_rk": 2,
    },
    {
        "source_cd": "TEST_SRC_3",
        "parent_source_cd": None,
        "short_cd": "short_cd_3",
        "ris_src_id": "301",
        "ris_src_code": "Test_src_3_code",
        "source_desc": "Test_src_3_desc",
        "data_layer_rk": 3,
    },
]

EXPECTED_INIT_BRIDGE_SOURCE_DATA = [
    {
        "source_cd": "TEST_SRC_1",
        "short_cd": None,
        "ris_src_id": "101",
        "ris_src_code": "Test_src_1_code",
        "source_desc": "Test_src_1_desc",
        "data_layer_rk": 1,
        "effective_from_dttm": NOW_INIT,
        "effective_to_dttm": LAST_DATE,
        "deleted_flg": False,
    },
    {
        "source_cd": "TEST_SRC_2",
        "short_cd": None,
        "ris_src_id": "201",
        "ris_src_code": "Test_src_2_code",
        "source_desc": "Test_src_2_desc",
        "data_layer_rk": 2,
        "effective_from_dttm": NOW_INIT,
        "effective_to_dttm": LAST_DATE,
        "deleted_flg": False,
    },
    {
        "source_cd": "TEST_SRC_3",
        "short_cd": "short_cd_3",
        "ris_src_id": "301",
        "ris_src_code": "Test_src_3_code",
        "source_desc": "Test_src_3_desc",
        "data_layer_rk": 3,
        "effective_from_dttm": NOW_INIT,
        "effective_to_dttm": LAST_DATE,
        "deleted_flg": False,
    },
]

# increment 1
# 1. delete Test_src_3
# 2. edit Test_src_2: short_cd, parent_source_cd, short_cd
# 3. Upper source_cd "Test_srC_1"
INC_1_BRIDGE_SOURCE_DATA = [
    {
        "source_cd": "TEST_SRC_1",
        "parent_source_cd": None,
        "short_cd": None,
        "ris_src_id": "101",
        "ris_src_code": "Test_src_1_code",
        "source_desc": "Test_src_1_desc",
        "data_layer_rk": 1,
    },
    {
        "source_cd": "TEST_SRC_2",
        "parent_source_cd": "TEST_SRC_3",
        "short_cd": "Ururu",
        "ris_src_id": "201",
        "ris_src_code": "Test_src_2_code_new",
        "source_desc": "Test_src_2_desc",
        "data_layer_rk": 2,
    },
    {
        "source_cd": "TEST_SRC_4",
        "parent_source_cd": None,
        "short_cd": None,
        "ris_src_id": "401",
        "ris_src_code": "Test_src_4_code",
        "source_desc": "Test_src_4_desc",
        "data_layer_rk": 4,
    },
]


EXPECTED_INC_1_BRIDGE_SOURCE_DATA = [
    {
        "source_cd": "TEST_SRC_1",
        "short_cd": None,
        "ris_src_id": "101",
        "ris_src_code": "Test_src_1_code",
        "source_desc": "Test_src_1_desc",
        "data_layer_rk": 1,
        "effective_from_dttm": NOW_INIT,
        "effective_to_dttm": LAST_DATE,
        "deleted_flg": False,
    },
    {
        "source_cd": "TEST_SRC_2",
        "short_cd": None,
        "ris_src_id": "201",
        "ris_src_code": "Test_src_2_code",
        "source_desc": "Test_src_2_desc",
        "data_layer_rk": 2,
        "effective_from_dttm": NOW_INIT,
        "effective_to_dttm": NOW_INC_1,
        "deleted_flg": False,
    },
    {
        "source_cd": "TEST_SRC_2",
        "short_cd": "Ururu",
        "ris_src_id": "201",
        "ris_src_code": "Test_src_2_code_new",
        "source_desc": "Test_src_2_desc",
        "data_layer_rk": 2,
        "effective_from_dttm": NOW_INC_1,
        "effective_to_dttm": LAST_DATE,
        "deleted_flg": False,
    },
    {
        "source_cd": "TEST_SRC_3",
        "short_cd": "short_cd_3",
        "ris_src_id": "301",
        "ris_src_code": "Test_src_3_code",
        "source_desc": "Test_src_3_desc",
        "data_layer_rk": 3,
        "effective_from_dttm": NOW_INIT,
        "effective_to_dttm": NOW_INC_1,
        "deleted_flg": False,
    },
    {
        "source_cd": "TEST_SRC_3",
        "short_cd": "short_cd_3",
        "ris_src_id": "301",
        "ris_src_code": "Test_src_3_code",
        "source_desc": "Test_src_3_desc",
        "data_layer_rk": 3,
        "effective_from_dttm": NOW_INC_1,
        "effective_to_dttm": LAST_DATE,
        "deleted_flg": True,
    },
    {
        "source_cd": "TEST_SRC_4",
        "short_cd": None,
        "ris_src_id": "401",
        "ris_src_code": "Test_src_4_code",
        "source_desc": "Test_src_4_desc",
        "data_layer_rk": 4,
        "effective_from_dttm": NOW_INC_1,
        "effective_to_dttm": LAST_DATE,
        "deleted_flg": False,
    },
]

# increment 2
# 1. recover Test_src_3
# 2. edit Test_src_3: short_cd, parent_source_cd, short_cd
# 3. edit Test_src_1: source_desc, data_layer_rk
INC_2_BRIDGE_SOURCE_DATA = [
    {
        "source_cd": "TEST_SRC_1",
        "parent_source_cd": None,
        "short_cd": None,
        "ris_src_id": "101",
        "ris_src_code": "Test_src_1_code",
        "source_desc": "Test_src_1_desc_new",
        "data_layer_rk": 10,
    },
    {
        "source_cd": "TEST_SRC_2",
        "parent_source_cd": "TEST_SRC_3",
        "short_cd": "short_cd_2",
        "ris_src_id": "201",
        "ris_src_code": "Test_src_2_code_new",
        "source_desc": "Test_src_2_desc",
        "data_layer_rk": 2,
    },
    {
        "source_cd": "TEST_SRC_3",
        "parent_source_cd": None,
        "short_cd": None,
        "ris_src_id": "301",
        "ris_src_code": "Test_src_3_code",
        "source_desc": "Test_src_3_desc_new",
        "data_layer_rk": 3,
    },
    {
        "source_cd": "TEST_SRC_4",
        "parent_source_cd": None,
        "short_cd": None,
        "ris_src_id": "401",
        "ris_src_code": "Test_src_4_code",
        "source_desc": "Test_src_4_desc",
        "data_layer_rk": 4,
    },
]

EXPECTED_INC_2_BRIDGE_SOURCE_DATA = [
    {
        "source_cd": "TEST_SRC_1",
        "short_cd": None,
        "ris_src_id": "101",
        "ris_src_code": "Test_src_1_code",
        "source_desc": "Test_src_1_desc_new",
        "data_layer_rk": 10,
        "effective_from_dttm": NOW_INC_2,
        "effective_to_dttm": LAST_DATE,
        "deleted_flg": False,
    },
    {
        "source_cd": "TEST_SRC_1",
        "short_cd": None,
        "ris_src_id": "101",
        "ris_src_code": "Test_src_1_code",
        "source_desc": "Test_src_1_desc",
        "data_layer_rk": 1,
        "effective_from_dttm": NOW_INIT,
        "effective_to_dttm": NOW_INC_2,
        "deleted_flg": False,
    },
    {
        "source_cd": "TEST_SRC_2",
        "short_cd": None,
        "ris_src_id": "201",
        "ris_src_code": "Test_src_2_code",
        "source_desc": "Test_src_2_desc",
        "data_layer_rk": 2,
        "effective_from_dttm": NOW_INIT,
        "effective_to_dttm": NOW_INC_1,
        "deleted_flg": False,
    },
    {
        "source_cd": "TEST_SRC_2",
        "short_cd": "Ururu",
        "ris_src_id": "201",
        "ris_src_code": "Test_src_2_code_new",
        "source_desc": "Test_src_2_desc",
        "data_layer_rk": 2,
        "effective_from_dttm": NOW_INC_1,
        "effective_to_dttm": LAST_DATE,
        "deleted_flg": False,
    },
    {
        "source_cd": "TEST_SRC_3",
        "short_cd": None,
        "ris_src_id": "301",
        "ris_src_code": "Test_src_3_code",
        "source_desc": "Test_src_3_desc_new",
        "data_layer_rk": 3,
        "effective_from_dttm": NOW_INC_2,
        "effective_to_dttm": LAST_DATE,
        "deleted_flg": False,
    },
    {
        "source_cd": "TEST_SRC_3",
        "short_cd": "short_cd_3",
        "ris_src_id": "301",
        "ris_src_code": "Test_src_3_code",
        "source_desc": "Test_src_3_desc",
        "data_layer_rk": 3,
        "effective_from_dttm": NOW_INC_1,
        "effective_to_dttm": NOW_INC_2,
        "deleted_flg": True,
    },
    {
        "source_cd": "TEST_SRC_3",
        "short_cd": "short_cd_3",
        "ris_src_id": "301",
        "ris_src_code": "Test_src_3_code",
        "source_desc": "Test_src_3_desc",
        "data_layer_rk": 3,
        "effective_from_dttm": NOW_INIT,
        "effective_to_dttm": NOW_INC_1,
        "deleted_flg": False,
    },
    {
        "source_cd": "TEST_SRC_4",
        "short_cd": None,
        "ris_src_id": "401",
        "ris_src_code": "Test_src_4_code",
        "source_desc": "Test_src_4_desc",
        "data_layer_rk": 4,
        "effective_from_dttm": NOW_INC_1,
        "effective_to_dttm": LAST_DATE,
        "deleted_flg": False,
    },
]


# code_delivery
# init
CODE_DELIVERY_DATA = [
    {
        "code_delivery_rk": 1,
        "code_delivery_cd": "code_delivery_cd_1",
        "code_delivery_name": "code_delivery_name_1",
        "ris_src_id": "101",
        "code_delivery_desc": "code_delivery_desc_1",
        "is_ddl_flg": True,
        "is_flow_flg": True,
        "is_config_flg": True,
    },
    {
        "code_delivery_rk": 2,
        "code_delivery_cd": "code_delivery_cd_2",
        "code_delivery_name": "code_delivery_name_2",
        "ris_src_id": "201",
        "code_delivery_desc": None,
        "is_ddl_flg": True,
        "is_flow_flg": False,
        "is_config_flg": True,
    },
    {
        "code_delivery_rk": 3,
        "code_delivery_cd": "code_delivery_cd_3",
        "code_delivery_name": "code_delivery_cd_3",
        "ris_src_id": "103",
        "code_delivery_desc": "code_delivery_desc_3",
        "is_ddl_flg": True,
        "is_flow_flg": True,
        "is_config_flg": False,
    },
]


EXPECTED_DICT_CODE_DELIVERY_DATA = [
    {
        "code_delivery_rk": 1,
        "code_delivery_cd": "code_delivery_cd_1",
        "code_delivery_name": "code_delivery_name_1",
        "code_delivery_desc": "code_delivery_desc_1",
        "is_ddl_flg": True,
        "is_flow_flg": True,
        "is_config_flg": True,
    },
    {
        "code_delivery_rk": 2,
        "code_delivery_cd": "code_delivery_cd_2",
        "code_delivery_name": "code_delivery_name_2",
        "code_delivery_desc": None,
        "is_ddl_flg": True,
        "is_flow_flg": False,
        "is_config_flg": True,
    },
    {
        "code_delivery_rk": 3,
        "code_delivery_cd": "code_delivery_cd_3",
        "code_delivery_name": "code_delivery_cd_3",
        "code_delivery_desc": "code_delivery_desc_3",
        "is_ddl_flg": True,
        "is_flow_flg": True,
        "is_config_flg": False,
    },
]
