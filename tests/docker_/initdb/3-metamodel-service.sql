create table metamodel.service_transaction_status (
    service_transaction_uid       uuid default gen_random_uuid() not null
        constraint service_transaction_status_pk
            primary key,
    entity_type                   smallint                       not null,
    request_dttm                  timestamp with time zone       not null,
    status_dttm                   timestamp with time zone       not null,
    response_dttm                 timestamp with time zone,
    service_rk                    smallint                       not null,
    service_transaction_status_rk smallint                       not null,
    data_processing_mode_rk       smallint,
    transaction_timeout_duration  bigint,
    transaction_request_json      jsonb,
    transaction_response_json     jsonb,
    request_param_list            text[]
);
create unique index service_transaction_status_idx_pk
    on metamodel.service_transaction_status (service_transaction_uid);

create table metamodel.service_transaction_data (
    service_transaction_data_rk bigint default nextval('metamodel.md_seq'::regclass) not null
        constraint service_transaction_data_pk
            primary key,
    create_dttm                 timestamp with time zone default now()               not null,
    name                        text                                                 not null,
    old_name                    text,
    content_json                jsonb,
    service_transaction_uid     uuid                                                 not null
        constraint service_transaction_status_fk
            references metamodel.service_transaction_status
            on update cascade on delete restrict,
    data_action_type_rk         smallint                                             not null
);
create unique index service_transaction_data_idx_uk
    on metamodel.service_transaction_data (service_transaction_uid, name);
