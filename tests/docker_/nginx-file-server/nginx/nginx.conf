events {
    worker_connections 1024;
}

http {
    server {
        listen 80;
        server_name localhost;

        location / {
            root /usr/share/nginx/html/files;
            autoindex on;
            autoindex_exact_size off;
            autoindex_localtime on;
            index index.html;
        }

        location ~* \.(yaml|yml|json)$ {
            root /usr/share/nginx/html/files;
            add_header Content-Disposition "attachment";
        }
    }
}
