master_flow_name: master_flow_test_2
metadata:
  src_cd: src_test_2
  mode: eod
  receiver_names:
    - receiver_test_1
    - receiver_test_2
  source_resource_cds:
    source_resource_test_1: source_resource_test_1_0
    source_resource_test_2: source_resource_test_2_0
  target_resource_cd: target_resource_test_2
  algorithm_uid: algorithm_test_2
  journal_table_name: journal_table_test_2
  delta_schema_name: delta_schema_test_2
  wrk_dag_timeout_duration: PT12H
  cf_dag_timeout_duration: PT8H
  commit_timeout_duration: PT2H
  timeout_before_trigger_attempt_duration: PT6M
  await_eod_metrics_retry_cnt: 5
  await_eod_metrics_retry_delay_duration: PT3M
  allow_switch_mode_flg: false
  cut_right_bound_flg: false
  check_eod_dependant_resources_flg: true
  enable_distribution_flg: true
sequencer:
  await_metrics_retry_cnt: 5
  await_metrics_retry_delay_duration: PT6M
  condition: any
  on_false_condition: fail
  dependencies:
    - resource_cd: resource_test_1
      algorithm_uids:
        - algorithm_test_1_1
        - algorithm_test_1_2
      resource_provider: uni
      metric_jq: metric_jq_test_1_1
      metric_name: metric_test_1_1
    - resource_cd: resource_test_2
      algorithm_uids:
        - algorithm_test_2_1
        - algorithm_test_2_2
      resource_provider: uni
      metric_jq: metric_jq_test_2_1
      metric_name: metric_test_2_1
groups:
  - group_id: group_test_2_1
    steps:
      - cf_flow_name: cf_test_2_1_1
        wrk_flow_name: wf_test_2_1_1
        run_flg: false
        retry_cnt: 1
        retry_timeout: 2
        target_resource_cds:
          - target_resource_test_1
          - target_resource_test_2
        conf:
          conf_key_test_1: conf_value_test_1
          conf_key_test_2: conf_value_test_2
        ignore_no_delta: false
        ignore_cf_fail: true
        ignore_wrk_fail: true
        ignore_resource_status: false
        ignore_task_fail: true
        timeout_before_trigger: 3
        cf_trigger_cnt: 4
        cut_right_bound_flg: false
      - cf_flow_id: cf_test_2_1_2
        wrk_flow_id: wf_test_2_1_2
        dependencies:
          - cf_test_2_1_1
  - group_id: group_test_2_2
    steps:
      - cf_flow_name: cf_test_2_2_1
        wrk_flow_name: wrk_test_2_2_1
      - cf_flow_id: cf_test_2_2_2
        wrk_flow_id: wrk_test_2_2_2
        dependencies:
          - cf_test_2_2_1
      - cf_flow_id: cf_test_2_2_3
        wrk_flow_id: wrk_test_2_2_3
        dependencies:
          - cf_test_2_2_2
          - cf_test_2_2_3
