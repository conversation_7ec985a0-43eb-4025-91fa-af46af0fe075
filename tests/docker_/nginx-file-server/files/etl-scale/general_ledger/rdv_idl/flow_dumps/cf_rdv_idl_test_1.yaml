schema_version: 2.0
flows:
  - id: cf_rdv_idl_test_1
    builder: ceh_core_idl.app.builders.simple_flow_builder
    tasks:
      - id: task_test_resource_1
        type: ceh_core_idl.app.operators.services.get_max_loaded_version_operator
        properties:
          target_resource_name: target_resource_test_1
          source_resource_names:
            - source_resource_test_1
          algo_name: algorithm_uid_test_idl_1
      - id: task_test_param_pass_1
        type: ceh_core_idl.app.operators.core.trigger_dag_operator
        properties:
          trigger_dag_id: wrk_rdv_idl_test_1
          conf:
            algorithm_uid: algorithm_uid_test_idl_1
