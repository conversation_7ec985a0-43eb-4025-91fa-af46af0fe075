schema_version: 2.0
flows:
  - id: wrk_rdv_idl_test_1
    builder: ceh_core_idl.app.builders.simple_flow_builder
    metadata:
      - name: algorithm_uid
        datatype: str
    tasks:
      - id: task_test_resource_1
        type: ceh_core_idl.app.operators.services.reg_delta_operator
        properties:
          target_resource_cd: target_resource_test_1
          target_dataset: idl.bbridge_test_1
          algorithm_uid: ${algorithm_uid}
