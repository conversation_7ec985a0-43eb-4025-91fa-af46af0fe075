schema_version: 2.0
metadata:
  imports:
    - rdv_cf_uni_template.cf_uni_template
flows:
  - id: cf_src_rdv_test_1
    builder: ceh_core_idl.app.builders.simple_flow_builder
    tasks:
      - id: task_test_resource_1
        builder: ceh_core_idl.app.builders.include_flow_builder
        properties:
          ref: cf_uni_template
          properties:
            algos_map:
              algorithm_uid_test_rdv_1:
                - ceh: target_ceh_resource_test_1
                  uni: source_uni_resource_test_1
