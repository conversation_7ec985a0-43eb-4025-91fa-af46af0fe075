type: WORK_FLOW
targets:
  - short_name: tgt_alias_1
    resource_cd: test.resource.tgt.1
    table: table_1
    schema: rdv
sources:
  - short_name: src_uni_missing
    type: COMPUTED_TABLE
    # Мы определяем, является ли COMPUTED_TABLE unified-loader-S2T-маппингом,
    # по захардкоженным значениям в атрибуте "object": "stg_uni_map", "stg_uni_ref"
    object: stg_uni_map_missing
    sub_sources_map: {}
mappings:
  marts:
    - algorithm_uid: ALGORITHM_UNI_MISSING_TEST
      target: tgt_alias_1
      source: src_uni_missing
