type: WORK_FLOW
targets:
  - short_name: tgt_mart
    resource_cd: test.resource.tgt.mart
    table: table_mart
    schema: rdv
  - short_name: hub_entity1
    resource_cd: test.resource.hub.entity1
    table: hub_entity1
    schema: rdv
  - short_name: hub_entity2
    resource_cd: test.resource.hub.entity2
    table: hub_entity2
    schema: rdv
  - short_name: hub_entity3
    resource_cd: test.resource.hub.entity3
    table: hub_entity3
    schema: rdv
sources:
  - short_name: src_data
    type: DB_TABLE
    resource_cd: test.resource.src.data
mappings:
  marts:
    - algorithm_uid: ALGORITHM_BK_TEST
      target: tgt_mart
      source: src_data
      hub_map:
        - target: hub_entity1
          business_key_schema: BK-key-schema-entity-1-TEST
        - target: hub_entity2
          business_key_schema: BK-key-schema-entity-2-TEST
        - target: hub_entity3
          business_key_schema: BK-key-schema-entity-3-TEST
