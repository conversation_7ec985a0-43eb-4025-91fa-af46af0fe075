type: WORK_FLOW
targets:
  - short_name: target_1
    resource_cd: test.resource.tgt.1
    table: target_1
    schema: rdv
  - short_name: target_2
    resource_cd: test.resource.tgt.2
    table: target_2
    schema: rdv
  - short_name: target_3
    resource_cd: test.resource.tgt.3
    table: target_3
    schema: rdv
  - short_name: target_4
    resource_cd: test.resource.tgt.4
    table: target_4
    schema: rdv
  - short_name: target_5
    resource_cd: test.resource.tgt.5
    table: target_5
    schema: rdv
sources:
  - short_name: source_1
    type: DB_TABLE
    resource_cd: test.resource.src.1
mappings:
  hub_satellites:
    - source: source_1
      target: target_1
      algorithm_uid: ALGORITHM_HUB_SAT
      rk_map:
        target: target_2
        business_key_schema: bk-schema-hub-sat-1-TEST
  link_satellites:
    - source: source_1
      target: target_3
      algorithm_uid: ALGORITHM_LINK_SAT
      rk_map:
        target: target_4
        rk_map:
          - target: target_5
            business_key_schema: bk-schema-link-sat-1-TEST
          - target: target_5
            business_key_schema: bk-schema-link-sat-2-TEST
