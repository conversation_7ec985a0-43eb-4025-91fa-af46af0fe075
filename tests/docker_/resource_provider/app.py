import json
import logging
import os
from pathlib import Path
from typing import Any, Dict
from urllib.parse import unquote

from fastapi import FastAP<PERSON>, Query
from fastapi.responses import JSONResponse

logger = logging.getLogger("uvicorn.error")

DELETED_RESOURCE_CD = "test.resource.was_deleted"


def get_provider_type_and_resources_dir():
    provider_type = os.getenv("PROVIDER_TYPE", "ceh").lower()
    if provider_type not in (p := ["ceh", "uni"]):
        raise ValueError(f"Invalid {provider_type=}, must be one of {p}")

    resources_dir = Path(__file__).parent / "resources" / provider_type
    if not resources_dir.exists():
        raise RuntimeError(f"Resources directory not found: {resources_dir}")

    return provider_type, resources_dir


def load_resources(resources_dir: Path) -> Dict[str, Dict[str, Any]]:
    resources_cache = {}
    for json_file in resources_dir.rglob("*.json"):
        with open(json_file, "r", encoding="utf-8") as f:
            resource_data = json.load(f)
            resource_cd = resource_data.get("resource_cd")
            if resource_cd:
                resources_cache[resource_cd] = resource_data
                logger.info(f"Loaded resource: {resource_cd}")

    logger.info(f"Total resources loaded: {len(resources_cache)}")
    return resources_cache


provider_type, resources_dir = get_provider_type_and_resources_dir()
resources_cache = load_resources(resources_dir)
app = FastAPI(title=f"Mock {provider_type.upper()} Resource Provider")


@app.get("/resources")
async def list_resources(
    limit: int = Query(100, ge=1, le=100),
    offset: int = Query(0, ge=0),
):
    global resources_cache

    resource_cds = list(resources_cache.keys())
    paginated_cds = resource_cds[offset : offset + limit]

    # Ceh-провайдер по какой-то причине может возвращать один и тот же resource_cd несколько раз
    if resource_cds and offset == 0:
        first_resource_cd = resource_cds[0]
        paginated_cds.append(first_resource_cd)
        logger.info(f"Added duplicate resource_cd for testing: {first_resource_cd}")

    # Ceh-провайдер по какой-то причине иногда может возвращать в списке такое имя ресурса, которое
    # при запросе его тела приводит к HTTP_410
    paginated_cds.append(DELETED_RESOURCE_CD)
    content = [{"resource_cd": cd} for cd in paginated_cds]

    return JSONResponse(content)


@app.get("/resources/{resource_cd}")
async def get_resource(resource_cd: str):
    global resources_cache

    decoded_resource_cd = unquote(resource_cd)
    if decoded_resource_cd == DELETED_RESOURCE_CD:
        return JSONResponse(status_code=410, content={"message": "Ресурс удален"})
    if decoded_resource_cd not in resources_cache:
        return JSONResponse(
            status_code=404, content={"message": "Ресурс не зарегистрирован"}
        )

    return resources_cache[decoded_resource_cd]


@app.get("/health")
async def health_check():
    global resources_cache
    return {
        "provider_type": provider_type,
        "resources_count": len(resources_cache),
    }


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8000)
