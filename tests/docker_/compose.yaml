name: ceh-metamodel-test-env

services:
  db:
    image: postgres:15.6-bullseye
    environment:
      POSTGRES_DB: d0_mon
      POSTGRES_USER: d0_mon_loader_tec
      POSTGRES_PASSWORD: d0_mon_loader_tec
    volumes:
      - "./initdb:/docker-entrypoint-initdb.d"
    ports:
      - "5432:5432"

  airflow:
    image: ceh-metamodel/airflow-efficient:2.7.3
    environment:
      PYTHONUNBUFFERED: True
      PYTHONOPTIMIZE: True
      AIRFLOW__WEBSERVER__WORKERS: 1
      AIRFLOW__API__AUTH_BACKENDS: airflow.api.auth.backend.basic_auth
    volumes:
      - "./airflow/dags:/opt/airflow/dags"
    command: standalone
    ports:
      - "8080:8080"
    healthcheck:
      test: [ "CMD", "curl", "--fail", "http://localhost:8080/health" ]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    restart: always

  nginx:
    image: nginx:latest
    container_name: nginx-file-server
    ports:
      - "5090:80"
    volumes:
      - ./nginx-file-server/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx-file-server/files:/usr/share/nginx/html/files:ro
    restart: unless-stopped

  ceh_db:
    image: datagrip/greenplum:6.8
#    environment:
#      GP_DB: dwh
#      GP_USER: dwh_loader_tec
#      GP_PASSWORD: dwh_loader_tec
    security_opt:
      - seccomp:unconfined
    ports:
      - "54321:5432"
    healthcheck:
      test: grep -r -i --include \*.log "Database successfully started" || exit 1
