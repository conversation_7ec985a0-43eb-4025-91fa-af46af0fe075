set DOCKER_BUILDKIT=1

docker build . ^
    --file tests\docker_\airflow\Dockerfile ^
    --pull ^
    --no-cache ^
    --build-arg PYTHON_BASE_IMAGE="python:3.8-slim-bookworm" ^
    --build-arg AIRFLOW_INSTALLATION_METHOD="apache-airflow" ^
    --build-arg AIRFLOW_VERSION="2.7.3" ^
    --build-arg AIRFLOW_EXTRAS="" ^
    --build-arg INSTALL_MYSQL_CLIENT="false" ^
    --build-arg INSTALL_MSSQL_CLIENT="false" ^
    --build-arg INSTALL_POSTGRES_CLIENT="false" ^
    --build-arg DEV_APT_DEPS="sudo" ^
    --build-arg RUNTIME_APT_DEPS="sudo dumb-init" ^
    --tag ceh-metamodel/airflow-minimal:2.7.3
