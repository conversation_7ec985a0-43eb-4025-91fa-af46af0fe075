from pathlib import Path

from airflow.decorators import dag
from airflow.utils.dates import days_ago
from ceh_core_management.master_cf.cf_builder import (
    ControlFLowBuilder,
    ControlFlowLoader,
)


@dag(
    dag_id=Path(__file__).stem,
    start_date=days_ago(2),
    catchup=False,
    orientation="TB",
    schedule_interval=None,
)
def master_control_flow():
    control_flow = ControlFlowLoader(
        cf_filename="cf_master_test_1.yaml",
    ).load_from_yaml()
    ControlFLowBuilder(
        control_flow=control_flow,
    ).build()


dag = master_control_flow()
