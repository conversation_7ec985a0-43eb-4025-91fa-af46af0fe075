from pathlib import Path

from airflow.decorators import dag
from airflow.utils.dates import days_ago
from ceh_core_management.master_cf.cf_builder import (
    ControlFLowBuilder,
    ControlFlowLoader,
)


@dag(
    dag_id=Path(__file__).stem,
    start_date=days_ago(2),
    catchup=False,
    orientation="TB",
    schedule_interval=None,
)
def master_control_flow():
    control_flow = ControlFlowLoader(
        cf_filename=f"{Path(__file__).stem}.yaml",
    ).load_from_yaml()
    ControlFLowBuilder(
        control_flow=control_flow,
        load_eod=True,
        load_reglament=True,
        receiver_names=[
            "test_receiver_name_1",
            "test_receiver_name_2",
        ],
        source_resource_name={
            "test_source_resource_name_1": "test_another_source_resource_name_1",
            "test_source_resource_name_2": "test_another_source_resource_name_2",
        },
        target_resource_name="test_target_resource_name",
        src_cd="test_src_cd",
        journal_table_name="test_journal_table_name",
        delta_schema_name="test_delta_schema_name",
    ).build()


dag = master_control_flow()
