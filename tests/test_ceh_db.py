from metaloader_rest_api import ceh_db
from metaloader_rest_api.ceh_db import load
from pytest import fixture, raises


def test_load(load_table):
    load_iterator = load(
        dsn=ceh_db.get_dsn(),
        query="""
            SELECT a_value
              FROM stg.test_gp_load
             WHERE a_feature = %(feature)s
          ORDER BY a_value
        """,
        parameters={"feature": 42},
        fetch_size=3,
    )

    assert next(load_iterator) == [
        {"a_value": "a"},
        {"a_value": "b"},
        {"a_value": "c"},
    ]
    assert next(load_iterator) == [
        {"a_value": "d"},
        {"a_value": "e"},
        {"a_value": "f"},
    ]
    assert next(load_iterator) == [
        {"a_value": "g"},
        {"a_value": "h"},
    ]
    with raises(StopIteration):
        next(load_iterator)


@fixture(scope="function")
def load_table(ceh_cursor):
    ceh_cursor.execute("DROP SCHEMA IF EXISTS stg CASCADE")
    ceh_cursor.execute("CREATE SCHEMA stg")
    ceh_cursor.execute("""
        CREATE TABLE stg.test_gp_load AS
        SELECT *
          FROM (VALUES ('a', 42)
                     , ('b', 42) 
                     , ('c', 42) 
                     , ('x', 41) 
                     , ('d', 42) 
                     , ('e', 42) 
                     , ('f', 42) 
                     , ('y', 41) 
                     , ('g', 42) 
                     , ('h', 42) 
                     , ('z', 41)) t (a_value
                                   , a_feature)
    """)
    ceh_cursor.connection.commit()
