from datetime import datetime
from typing import Any
from uuid import UUID

from metaloader_rest_api.ceh_service.ceh_service_model import CehServiceType
from metaloader_rest_api.common_model import FIRST_DATE, LAST_DATE
from metaloader_rest_api.common_param import (
    DEPLOYMENT_MODULE_DEFAULT,
    DEPLOYMENT_SERVICE_DEFAULT,
)
from metaloader_rest_api.flow_processor.flow_processor import FlowProcessor
from metaloader_rest_api.flow_processor.flow_processor_api import (
    ProcessFlowsParams,
    process_flows,
)
from metaloader_rest_api.flow_processor.flow_processor_registry import (
    FlowProcessorRegistry,
)
from metaloader_rest_api.http_file_provider.http_file_provider import HttpFileProvider
from metaloader_rest_api.yaml.yaml_loader import Yaml
from pytest import fixture
from sqlalchemy import text
from sqlalchemy.orm import Session
from structlog.stdlib import <PERSON>und<PERSON>ogger


def test_process_flows(
    db_session: Session,
    load_id: UUID,
    effective_date: datetime,
    service_id: int,
    flows,
    flow_paths,
    stub_flow_processor: str,
):
    result = process_flows(
        session=db_session,
        load_id=load_id,
        effective_date=effective_date,
        params=ProcessFlowsParams(
            processors=[stub_flow_processor],
        ),
    )

    assert result == {
        stub_flow_processor: {
            "cf_test_1": (
                4,
                {
                    "key_test_1": "value_test_1",
                },
            )
        }
    }


class StubFlowProcessor(FlowProcessor[None]):
    def __init__(
        self,
        session: Session,
        file_provider: HttpFileProvider,
        load_id: UUID,
        module_id: int,
        version_id: int,
        effective_date: datetime,
        page_size: int,
        log: BoundLogger,
    ):
        super().__init__(
            session,
            file_provider,
            load_id,
            module_id,
            version_id,
            effective_date,
        )

        self._result = {}

    def process(
        self,
        flow_id: int,
        flow_name: str,
        flow: Yaml,
    ) -> None:
        self._result[flow_name] = (flow_id, flow)

    @property
    def result(self) -> Any:
        return self._result


@fixture(scope="module")
def stub_flow_processor() -> str:
    FlowProcessorRegistry.register("stub_flow_processor", StubFlowProcessor)

    return "stub_flow_processor"


@fixture(scope="function")
def flows(
    db_session: Session,
    module_id: int,
    version_id: int,
) -> None:
    db_session.execute(text("TRUNCATE metamodel.bridge_flow"))
    db_session.execute(
        statement=text("""
            INSERT INTO metamodel.bridge_flow(flow_rk
                                            , flow_name
                                            , flow_type_rk
                                            , flow_release_status_rk
                                            , code_delivery_rk
                                            , version_rk
                                            , effective_from_dttm 
                                            , effective_to_dttm 
                                            , deleted_flg) 
                 VALUES (4
                       , 'cf_test_1'
                       , 2
                       , 1
                       , :code_delivery_rk
                       , :version_rk
                       , '2000-01-01 +0'
                       , '2999-12-31 +0'
                       , FALSE)
        """),
        params={
            "code_delivery_rk": module_id,
            "version_rk": version_id,
        },
    )
    db_session.commit()


@fixture(scope="function")
def flow_paths(
    db_session: Session,
    module_id: int,
    flow_path_id: int,
) -> None:
    db_session.execute(text("TRUNCATE dict.link_code_delivery_folder"))
    db_session.execute(
        statement=text("""
            INSERT INTO dict.link_code_delivery_folder(code_delivery_rk
                                                     , code_folder_rk
                                                     , is_required_flg
                                                     , is_flow_flg
                                                     , is_master_flow_flg
                                                     , is_ddl_flg
                                                     , is_resource_flg)
                 VALUES (:code_delivery_rk
                       , :code_folder_rk
                       , TRUE
                       , TRUE
                       , FALSE
                       , FALSE
                       , FALSE)
        """),
        params={
            "code_delivery_rk": module_id,
            "code_folder_rk": flow_path_id,
        },
    )
    db_session.commit()


@fixture(scope="function")
def flow_path_id(
    db_session: Session,
    module_id: int,
    version_id: int,
) -> int:
    db_session.execute(text("TRUNCATE dict.dict_code_folder"))
    db_session.execute(
        statement=text("""
            INSERT INTO dict.dict_code_folder(code_folder_rk
                                            , code_folder_cd  
                                            , code_folder_path) 
                 VALUES (3
                       , 'flow_path_test_1'
                       , 'test_flow_processor')
        """),
    )
    db_session.commit()

    return 3


@fixture(scope="function")
def service_id(
    db_session: Session,
    module_id: int,
    airflow_url: str,
    local_file_server_url: str,
) -> int:
    db_session.execute(
        statement=text(f"""
            TRUNCATE metamodel.bridge_service;
            INSERT INTO metamodel.bridge_service (service_rk
                                                , service_cd
                                                , service_host_name
                                                , service_url
                                                , service_alt_url
                                                , service_type_rk
                                                , environment_rk
                                                , effective_from_dttm
                                                , effective_to_dttm
                                                , deleted_flg)
            VALUES (3
                  , '{DEPLOYMENT_SERVICE_DEFAULT}'
                  , ''
                  , '{airflow_url}'
                  , '{local_file_server_url}'
                  , {CehServiceType.AIRFLOW}
                  , -1
                  , :effective_from_dttm
                  , :effective_to_dttm
                  , FALSE);

            TRUNCATE metamodel.link_service_delivery;
            INSERT INTO metamodel.link_service_delivery (service_rk
                                                       , code_delivery_rk)
            VALUES (3
                  , :code_delivery_rk)
        """),
        params={
            "effective_from_dttm": FIRST_DATE,
            "effective_to_dttm": LAST_DATE,
            "code_delivery_rk": module_id,
        },
    )
    db_session.commit()

    return 3


@fixture(scope="function")
def version_id(db_session: Session, module_id: int) -> int:
    db_session.execute(text("TRUNCATE metamodel.bridge_version"))
    db_session.execute(
        statement=text("""
            INSERT INTO metamodel.bridge_version (version_rk
                                                , code_delivery_rk
                                                , release_major_num
                                                , release_minor_num
                                                , release_fix_num
                                                , release_delivery_dttm) 
                 VALUES (2
                       , :code_delivery_rk
                       , 999
                       , 999
                       , 999
                       , '2000-01-01 +0')
        """),
        params={
            "code_delivery_rk": module_id,
        },
    )
    db_session.commit()
    return 2


@fixture(scope="function")
def module_id(db_session: Session) -> int:
    db_session.execute(text("TRUNCATE dict.dict_code_delivery"))
    db_session.execute(
        statement=text(f"""
            INSERT INTO dict.dict_code_delivery(code_delivery_rk
                                              , code_delivery_cd
                                              , code_delivery_name
                                              , is_ddl_flg
                                              , is_flow_flg
                                              , is_config_flg
                                              , deleted_flg)
                 VALUES (1
                      , '{DEPLOYMENT_MODULE_DEFAULT}'
                      , '{DEPLOYMENT_MODULE_DEFAULT}'
                      , FALSE
                      , TRUE
                      , FALSE
                      , FALSE);
        """)
    )
    db_session.commit()

    return 1


@fixture(scope="module")
def load_id() -> UUID:
    return UUID("12345678123456781234567812345678")


@fixture(scope="module")
def effective_date() -> datetime:
    return datetime(2000, 1, 1)
