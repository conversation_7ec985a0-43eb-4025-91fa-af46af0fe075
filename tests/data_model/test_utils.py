from typing import Any, Dict, Optional, Sequence, Tuple

from sqlalchemy import BindParameter
from sqlalchemy.sql import text

DEFAULT_STRING_QUOTE = '"'
DEFAULT_NULL_PLACEHOLDER = "<NULL>"
DEFAULT_COLUMN_SEPARATOR = "\t"
DEFAULT_ALIGN_RIGHT = False
DEFAULT_QUOTE_STRINGS = True
DEFAULT_DISPLAY_TABLE_NAME = True


class TableViewTest:
    def __init__(
        self,
        columns: Tuple[str, ...],
        data: Tuple[Tuple[Any, ...], ...] = (),
        table_name: Optional[str] = None,
        *,
        bind_params: Optional[Sequence[BindParameter]] = None,
        align_right: bool = DEFAULT_ALIGN_RIGHT,
        quote_strings: bool = DEFAULT_QUOTE_STRINGS,
        display_table_name: bool = DEFAULT_DISPLAY_TABLE_NAME,
        string_quote: str = DEFAULT_STRING_QUOTE,
        null_placeholder: str = DEFAULT_NULL_PLACEHOLDER,
        column_separator: str = DEFAULT_COLUMN_SEPARATOR,
    ):
        self._columns = columns
        self._data = self._normalize_data(data)
        self._table_name = table_name
        self._bind_params = bind_params or []
        self._align_right = (
            align_right if align_right is not None else DEFAULT_ALIGN_RIGHT
        )
        self._quote_strings = (
            quote_strings if quote_strings is not None else DEFAULT_QUOTE_STRINGS
        )
        self._display_table_name = (
            display_table_name
            if display_table_name is not None
            else DEFAULT_DISPLAY_TABLE_NAME
        )
        self._string_quote = (
            string_quote if string_quote is not None else DEFAULT_STRING_QUOTE
        )
        self._null_placeholder = (
            null_placeholder
            if null_placeholder is not None
            else DEFAULT_NULL_PLACEHOLDER
        )
        self._column_separator = (
            column_separator
            if column_separator is not None
            else DEFAULT_COLUMN_SEPARATOR
        )

    def __eq__(self, other):
        if not isinstance(other, TableViewTest):
            return NotImplemented
        return self._columns == other._columns and self._data == other._data

    def __repr__(self):
        str_columns = list(map(str, self._columns))
        num_cols = len(str_columns)

        str_data = []
        for row_data in self._data:
            str_data.append([self._format_value(item) for item in row_data])

        all_rows = [str_columns] + str_data
        col_widths = [
            max(len(r[col_idx]) for r in all_rows) for col_idx in range(num_cols)
        ]

        header = f"TableViewTest{f' (table: {self._table_name})' if self._table_name and self._display_table_name else ''}:\n"

        if not str_data:
            return header + self._format_row(str_columns, col_widths)

        lines = [self._format_row(str_columns, col_widths)] + [
            self._format_row(row, col_widths) for row in str_data
        ]
        return header + "\n".join(lines)

    def insert(self, session):
        if not self._table_name:
            raise ValueError("Table name must be specified to perform insert.")

        if self._data:
            session.execute(
                statement=text(f"""
                    INSERT INTO {self._table_name} ({", ".join(self._columns)})
                         VALUES ({", ".join(f":{col}" for col in self._columns)})
                """).bindparams(*self._bind_params),
                params=self._to_dicts(),
            )
            session.commit()

    def truncate(self, session):
        if not self._table_name:
            raise ValueError("Table name must be specified to perform cleanup.")

        session.execute(text(f"TRUNCATE TABLE {self._table_name}"))
        session.commit()

    def _normalize_data(
        self, data: Tuple[Tuple[Any, ...], ...]
    ) -> Tuple[Tuple[Any, ...], ...]:
        num_cols = len(self._columns)
        normalized_data = []

        for idx, row in enumerate(data):
            if len(row) > num_cols:
                raise ValueError(
                    f"Row {idx} has more values ({len(row)}) than columns ({num_cols}): {row}"
                )
            padded_row = row + (None,) * (num_cols - len(row))
            normalized_data.append(padded_row)

        return tuple(normalized_data)

    def _to_dicts(self) -> Sequence[Dict[str, Any]]:
        return [dict(zip(self._columns, row)) for row in self._data]

    def _format_value(self, val: Any) -> str:
        if val is None:
            return self.null_placeholder
        if isinstance(val, str):
            return (
                f"{self._string_quote}{val}{self._string_quote}"
                if self._quote_strings
                else val
            )
        return str(val)

    def _format_row(self, row: Sequence[Any], col_widths: Sequence[int]) -> str:
        return self.column_separator.join(
            str(val).rjust(col_widths[i])
            if self._align_right
            else str(val).ljust(col_widths[i])
            for i, val in enumerate(row)
        )

    @property
    def table_name(self) -> Optional[str]:
        return self._table_name

    @property
    def columns(self) -> Tuple[str, ...]:
        return self._columns

    @property
    def data(self) -> Tuple[Tuple[Any, ...], ...]:
        return self._data

    @property
    def align_right(self) -> bool:
        return self._align_right

    @property
    def quote_strings(self) -> bool:
        return self._quote_strings

    @property
    def display_table_name(self) -> bool:
        return self._display_table_name

    @property
    def string_quote(self) -> str:
        return self._string_quote

    @property
    def null_placeholder(self) -> str:
        return self._null_placeholder

    @property
    def column_separator(self) -> str:
        return self._column_separator

    @staticmethod
    def _build_query(
        table_name: str,
        columns: Tuple[str, ...],
        sort: Optional[Tuple[str, ...]] = None,
    ):
        return text(f"""
               SELECT {", ".join(f'{col}' for col in columns)}
                 FROM {table_name}
           {"ORDER BY " + ", ".join(f'{col}' for col in sort) if sort else ""}
        """)

    @staticmethod
    def _from_result(
        result,
        table_name: Optional[str] = None,
        *,
        align_right: Optional[bool] = None,
        quote_strings: Optional[bool] = None,
        display_table_name: Optional[bool] = None,
        string_quote: Optional[str] = None,
        null_placeholder: Optional[str] = None,
        column_separator: Optional[str] = None,
    ) -> "TableViewTest":
        return TableViewTest(
            tuple(result.keys()),
            tuple(tuple(row) for row in result.fetchall()),
            table_name,
            align_right=align_right,
            quote_strings=quote_strings,
            display_table_name=display_table_name,
            string_quote=string_quote,
            null_placeholder=null_placeholder,
            column_separator=column_separator,
        )

    @staticmethod
    def table_rows(
        session,
        table_name: str,
        columns: Tuple[str, ...],
        sort: Optional[Tuple[str, ...]] = None,
        *,
        align_right: Optional[bool] = None,
        quote_strings: Optional[bool] = None,
        display_table_name: Optional[bool] = None,
        string_quote: Optional[str] = None,
        null_placeholder: Optional[str] = None,
        column_separator: Optional[str] = None,
    ) -> "TableViewTest":
        result = session.execute(TableViewTest._build_query(table_name, columns, sort))
        return TableViewTest._from_result(
            result,
            table_name,
            align_right=align_right,
            quote_strings=quote_strings,
            display_table_name=display_table_name,
            string_quote=string_quote,
            null_placeholder=null_placeholder,
            column_separator=column_separator,
        )

    @staticmethod
    def table_rows_for_query(
        session,
        query: str,
        *,
        quote_strings: Optional[bool] = None,
        align_right: Optional[bool] = None,
        display_table_name: Optional[bool] = None,
        string_quote: Optional[str] = None,
        null_placeholder: Optional[str] = None,
        column_separator: Optional[str] = None,
    ) -> "TableViewTest":
        result = session.execute(text(query))
        return TableViewTest._from_result(
            result,
            align_right=align_right,
            quote_strings=quote_strings,
            display_table_name=display_table_name,
            string_quote=string_quote,
            null_placeholder=null_placeholder,
            column_separator=column_separator,
        )

    @staticmethod
    def table_rows_for_table_view(
        session,
        table_view: "TableViewTest",
        sort: Optional[Tuple[str, ...]] = None,
    ) -> "TableViewTest":
        if not table_view.table_name:
            raise ValueError("Table name must be specified to perform select.")

        return TableViewTest.table_rows(
            session,
            table_view.table_name,
            table_view.columns,
            sort,
            align_right=table_view.align_right,
            quote_strings=table_view.quote_strings,
            display_table_name=table_view.display_table_name,
            string_quote=table_view.string_quote,
            null_placeholder=table_view.null_placeholder,
            column_separator=table_view.column_separator,
        )
