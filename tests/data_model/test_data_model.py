from datetime import datetime, timezone
from typing import Sequence
from uuid import UUID

from metaloader_rest_api.data_model.data_model import load_data_model
from metaloader_rest_api.data_model.physical_model import load_physical_model
from pytest import fixture
from sqlalchemy import text


def test_load_logical_model(
    db_session,
    version_id,
    effective_date,
    schemas,
    tables,
    table_id,
):
    load_data_model(
        db_session,
        version_id,
        effective_date,
        schemas,
        table_id,
    )
    db_session.commit()

    rows = db_session.execute(text("SELECT * FROM metamodel.bridge_table")).fetchall()
    assert len(rows) == 34

    rows = db_session.execute(
        text("SELECT * FROM metamodel.bridge_attribute")
    ).fetchall()
    assert len(rows) == 90

    rows = db_session.execute(text("SELECT * FROM metamodel.bridge_key")).fetchall()
    assert len(rows) == 20

    rows = db_session.execute(
        text("SELECT * FROM metamodel.link_key_attribute")
    ).fetchall()
    assert len(rows) == 28

    load_data_model(
        db_session,
        version_id,
        effective_date,
        schemas,
        table_id,
    )
    db_session.commit()

    rows = db_session.execute(text("SELECT * FROM metamodel.bridge_table")).fetchall()
    assert len(rows) == 34

    rows = db_session.execute(
        text("SELECT * FROM metamodel.bridge_attribute")
    ).fetchall()
    assert len(rows) == 90

    rows = db_session.execute(text("SELECT * FROM metamodel.bridge_key")).fetchall()
    assert len(rows) == 20

    rows = db_session.execute(
        text("SELECT * FROM metamodel.link_key_attribute")
    ).fetchall()
    assert len(rows) == 28


def test_load_bridge_table_incr(
    db_session,
    version_id,
    effective_date,
    effective_date_incr,
    ceh_dsn,
    schemas,
    table_id,
    page_size,
    truncate_all_model_tables,
    source,
    tables_bridge_table_incr,
    ceh_cursor,
):
    load_physical_model(
        db_session,
        version_id,
        effective_date,
        ceh_dsn,
        schemas,
        table_id,
        page_size,
    )
    db_session.commit()

    load_data_model(
        db_session,
        version_id,
        effective_date,
        schemas,
        table_id,
    )
    db_session.commit()

    rows = db_session.execute(text("SELECT * FROM metamodel.bridge_table")).fetchall()
    assert len(rows) == 4

    rows = db_session.execute(
        text("SELECT * FROM metamodel.bridge_attribute")
    ).fetchall()
    assert len(rows) == 28

    edit_incr_tables(ceh_cursor)
    load_physical_model(
        db_session,
        version_id,
        effective_date_incr,
        ceh_dsn,
        schemas,
        table_id,
        page_size,
    )
    db_session.commit()

    load_data_model(
        db_session,
        version_id,
        effective_date_incr,
        schemas,
        table_id,
    )
    db_session.commit()

    rows = db_session.execute(text("SELECT * FROM metamodel.bridge_table")).fetchall()
    assert len(rows) == 6

    rows = db_session.execute(
        text("SELECT * FROM metamodel.bridge_attribute")
    ).fetchall()
    assert len(rows) == 30


def test_load_bridge_attribute_incr(
    db_session,
    version_id,
    effective_date,
    effective_date_incr,
    ceh_dsn,
    schemas,
    table_id,
    page_size,
    truncate_all_model_tables,
    source,
    tables_bridge_attr_incr,
    ceh_cursor,
):
    load_physical_model(
        db_session,
        version_id,
        effective_date,
        ceh_dsn,
        schemas,
        table_id,
        page_size,
    )
    db_session.commit()

    load_data_model(
        db_session,
        version_id,
        effective_date,
        schemas,
        table_id,
    )
    db_session.commit()

    rows = db_session.execute(text("SELECT * FROM metamodel.bridge_table")).fetchall()
    assert len(rows) == 1

    rows = db_session.execute(
        text("SELECT * FROM metamodel.bridge_attribute")
    ).fetchall()
    assert len(rows) == 6

    edit_incr_attr(ceh_cursor)

    load_physical_model(
        db_session,
        version_id,
        effective_date_incr,
        ceh_dsn,
        schemas,
        table_id,
        page_size,
    )
    db_session.commit()

    load_data_model(
        db_session,
        version_id,
        effective_date_incr,
        schemas,
        table_id,
    )
    db_session.commit()

    rows = db_session.execute(text("SELECT * FROM metamodel.bridge_table")).fetchall()
    assert len(rows) == 1

    rows = db_session.execute(
        text("SELECT * FROM metamodel.bridge_attribute")
    ).fetchall()
    assert len(rows) == 11


def test_load_key_incr(
    db_session,
    version_id,
    effective_date,
    effective_date_incr,
    ceh_dsn,
    schemas,
    table_id,
    page_size,
    truncate_all_model_tables,
    source,
    tables_bridge_table_incr,
    ceh_cursor,
):
    load_physical_model(
        db_session,
        version_id,
        effective_date,
        ceh_dsn,
        schemas,
        table_id,
        page_size,
    )
    db_session.commit()

    load_data_model(
        db_session,
        version_id,
        effective_date,
        schemas,
        table_id,
    )
    db_session.commit()

    rows = db_session.execute(text("SELECT * FROM metamodel.bridge_key")).fetchall()
    assert len(rows) == 3

    rows = db_session.execute(
        text("SELECT * FROM metamodel.link_key_attribute")
    ).fetchall()
    assert len(rows) == 4

    edit_incr_key(ceh_cursor)

    load_physical_model(
        db_session,
        version_id,
        effective_date_incr,
        ceh_dsn,
        schemas,
        table_id,
        page_size,
    )
    db_session.commit()

    load_data_model(
        db_session,
        version_id,
        effective_date_incr,
        schemas,
        table_id,
    )
    db_session.commit()

    rows = db_session.execute(text("SELECT * FROM metamodel.bridge_key")).fetchall()
    assert len(rows) == 5

    rows = db_session.execute(
        text("SELECT * FROM metamodel.link_key_attribute")
    ).fetchall()
    assert len(rows) == 8


@fixture(scope="function")
def tables(db_session):
    db_session.execute(text("TRUNCATE metamodel.bridge_table"))
    db_session.execute(text("TRUNCATE metamodel.bridge_attribute"))
    db_session.execute(text("TRUNCATE metamodel.bridge_key"))
    db_session.execute(text("TRUNCATE metamodel.link_key_attribute"))

    db_session.execute(text("TRUNCATE metamodel.bridge_physical_table"))
    db_session.execute(
        text("""
        INSERT INTO metamodel.bridge_physical_table (effective_from_dttm
                                                   , effective_to_dttm
                                                   , deleted_flg
                                                   , version_rk
                                                   , physical_table_rk
                                                   , source_rk
                                                   , data_layer_rk
                                                   , schema_name
                                                   , table_name
                                                   , table_desc
                                                   , table_type_rk
                                                   , table_type_cd
                                                   , is_dict_flg
                                                   , is_map_flg
                                                   , is_hub_flg
                                                   , is_sal_flg
                                                   , is_mart_flg
                                                   , is_bridge_flg
                                                   , is_link_flg
                                                   , is_accessor_flg
                                                   , table_distribution_type_rk
                                                   , table_distribution_type_cd
                                                   , version_scd_type_rk
                                                   , history_scd_type_rk)
        SELECT '2025-01-01 +0' effective_from_dttm
             , '2999-12-31 +0' effective_to_dttm
             , FALSE           deleted_flg
             , 1               version_rk
             , physical_table_rk
             , 1 source_rk
             , data_layer_rk
             , schema_name
             , table_name
             , table_desc
             , table_type_rk
             , table_type_cd
             , is_dict_flg
             , is_map_flg
             , is_hub_flg
             , is_sal_flg
             , is_mart_flg
             , is_bridge_flg
             , is_link_flg
             , is_accessor_flg
             , table_distribution_type_rk
             , table_distribution_type_cd
             , version_scd_type_rk
             , history_scd_type_rk
          FROM (
                VALUES (1,   3, 'idl', 'dict_test_1',                    'Dict Test 1',                     1, 'r', TRUE,  FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, -1, 'r',  2, 3)
                     , (2,   3, 'idl', 'hub_test_2',                     'Hub Test 2',                      1, 'r', FALSE, FALSE, TRUE,  FALSE, FALSE, FALSE, FALSE, FALSE, -1, 'p',  2, 3)
                     , (3,   3, 'idl', 'sal_test_3',                     'SAL Test 3',                      1, 'r', FALSE, FALSE, FALSE, TRUE,  FALSE, FALSE, FALSE, FALSE, -1, 'p',  2, 3)
                     , (4,   3, 'idl', 'bbridge_test_4',                 'BBridge Test 4',                  1, 'r', FALSE, FALSE, FALSE, FALSE, FALSE, TRUE,  FALSE, FALSE, -1, 'p',  2, 3)
                     , (5,   3, 'idl', 'bbridge_test_5',                 'BBridge Test 5',                  1, 'r', FALSE, FALSE, FALSE, FALSE, FALSE, TRUE,  FALSE, FALSE, -1, 'p',  3, 2)
                     , (6,   3, 'idl', 'tbridge_test_6',                 'TBridge Test 6',                  1, 'r', FALSE, FALSE, FALSE, FALSE, FALSE, TRUE,  FALSE, FALSE, -1, 'p',  2, 2)
                     , (7,   3, 'idl', 'blink_test_4_x_test_5',          'BLink Test 4 to Test 5',          1, 'r', FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, TRUE,  FALSE, -1, 'p',  2, 3)
                     , (8,   3, 'idl', 'tlink_test_6_x_test_6',          'TLink Test 6 to Test 6',          1, 'r', FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, TRUE,  FALSE, -1, 'p',  3, 3)
                     , (9,   3, 'idl', 'v_sn_all_dict_test_1',           'Accessor Dict Test 1',            2, 'v', FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, TRUE,  -1, NULL, 2, 3)
                     , (10,  3, 'idl', 'v_sn_all_hub_test_2',            'Accessor Hub Test 2',             2, 'v', FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, TRUE,  -1, NULL, 2, 3)
                     , (11,  3, 'idl', 'v_sn_all_sal_test_3',            'Accessor SAL Test 3',             2, 'v', FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, TRUE,  -1, NULL, 2, 3)
                     , (12,  3, 'idl', 'v_sn_all_bbridge_test_4',        'Accessor BBridge Test 4',         2, 'v', FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, TRUE,  -1, NULL, 2, 3)
                     , (13,  3, 'idl', 'v_sn_all_bbridge_test_5',        'Accessor BBridge Test 5',         2, 'v', FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, TRUE,  -1, NULL, 2, 3)
                     , (14,  3, 'idl', 'v_sn_all_tbridge_test_6',        'Accessor TBridge Test 6',         2, 'v', FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, TRUE,  -1, NULL, 2, 3)
                     , (15,  3, 'idl', 'v_sn_all_blink_test_4_x_test_5', 'Accessor BLink Test 4 to Test 5', 2, 'v', FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, TRUE,  -1, NULL, 2, 3)
                     , (16,  3, 'idl', 'v_sn_all_tlink_test_6_x_test_6', 'Accessor TLink Test 6 to Test 6', 2, 'v', FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, TRUE,  -1, NULL, 2, 3)
                     , (17,  4, 'bdm', 'dict_test_1',                    'Dict Test 1',                     2, 'v', FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, -1, NULL, 2, 3)
                     , (18,  4, 'bdm', 'hub_test_2',                     'Hub Test 2',                      2, 'v', FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, -1, NULL, 2, 3)
                     , (19,  4, 'bdm', 'sal_test_3',                     'SAL Test 3',                      2, 'v', FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, -1, NULL, 2, 3)
                     , (20,  4, 'bdm', 'test_4',                         'Test 4',                          2, 'v', FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, -1, NULL, 2, 3)
                     , (21,  4, 'bdm', 'test_5',                         'Test 5',                          2, 'v', FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, -1, NULL, 2, 3)
                     , (22,  4, 'bdm', 'test_4_x_test_5',                'Link Test 4 to Test 5',           2, 'v', FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, -1, NULL, 2, 3)
                     , (23,  4, 'bdm', 'r_dict_test_1',                  'Accessor Dict Test 1',            2, 'v', FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, TRUE,  -1, NULL, 2, 3)
                     , (24,  4, 'bdm', 'v_sn_all_dict_test_1',           'Accessor Dict Test 1',            2, 'v', FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, TRUE,  -1, NULL, 2, 3)
                     , (25,  4, 'bdm', 'r_dict_hub_test_2',              'Accessor Hub Test 2',             2, 'v', FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, TRUE,  -1, NULL, 2, 3)
                     , (26,  4, 'bdm', 'v_sn_all_hub_test_2',            'Accessor Hub Test 2',             2, 'v', FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, TRUE,  -1, NULL, 2, 3)
                     , (27,  4, 'bdm', 'r_sal_test_3',                   'Accessor SAL Test 3',             2, 'v', FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, TRUE,  -1, NULL, 2, 3)
                     , (28,  4, 'bdm', 'v_sn_all_sal_test_3',            'Accessor SAL Test 3',             2, 'v', FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, TRUE,  -1, NULL, 2, 3)
                     , (29,  4, 'bdm', 'r_test_4',                       'Accessor Test 4',                 2, 'v', FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, TRUE,  -1, NULL, 2, 3)
                     , (30,  4, 'bdm', 'v_sn_all_test_4',                'Accessor Test 4',                 2, 'v', FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, TRUE,  -1, NULL, 2, 3)
                     , (31,  4, 'bdm', 'v_sn_all_test_5',                'Accessor Test 5',                 2, 'v', FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, TRUE,  -1, NULL, 2, 3)
                     , (32,  4, 'bdm', 'r_test_5',                       'Accessor Test 5',                 2, 'v', FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, TRUE,  -1, NULL, 2, 3)
                     , (33,  4, 'bdm', 'r_test_4_x_test_5',              'Accessor Test 4 to Test 5',       2, 'v', FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, TRUE,  -1, NULL, 2, 3)
                     , (34,  4, 'bdm', 'v_sn_all_test_4_x_test_5',       'Accessor Test 4 to Test 5',       2, 'v', FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, TRUE,  -1, NULL, 2, 3) 
               ) t (physical_table_rk
                  , data_layer_rk
                  , schema_name
                  , table_name
                  , table_desc
                  , table_type_rk
                  , table_type_cd
                  , is_dict_flg
                  , is_map_flg
                  , is_hub_flg
                  , is_sal_flg
                  , is_mart_flg
                  , is_bridge_flg
                  , is_link_flg
                  , is_accessor_flg
                  , table_distribution_type_rk
                  , table_distribution_type_cd
                  , version_scd_type_rk
                  , history_scd_type_rk)
    """)
    )

    db_session.execute(text("TRUNCATE metamodel.bridge_physical_attribute"))
    db_session.execute(
        text("""
        INSERT INTO metamodel.bridge_physical_attribute (effective_from_dttm
                                                       , effective_to_dttm
                                                       , deleted_flg
                                                       , version_rk
                                                       , physical_attribute_rk
                                                       , schema_name
                                                       , table_name
                                                       , physical_table_rk
                                                       , attribute_name
                                                       , attribute_desc
                                                       , attribute_no
                                                       , data_type_id
                                                       , data_type_rk
                                                       , data_type_size_cnt
                                                       , data_type_scale_cnt
                                                       , is_nullable_flg
                                                       , default_value_txt
                                                       , distribution_no
                                                       , data_domain_rk
                                                       , metadata_type_rk)
        SELECT '2025-01-01 +0' effective_from_dttm
             , '2999-12-31 +0' effective_to_dttm
             , FALSE           deleted_flg
             , 1               version_rk
             , physical_attribute_rk
             , schema_name
             , table_name
             , physical_table_rk
             , attribute_name
             , attribute_desc
             , attribute_no
             , data_type_id
             , data_type_rk
             , data_type_size_cnt
             , data_type_scale_cnt
             , is_nullable_flg
             , default_value_txt
             , distribution_no
             , data_domain_rk
             , metadata_type_rk
          FROM (
                VALUES (1,  1, 'idl', 'dict_test_1',           'src_cd',               1,  -1, 25,   NULL, NULL, FALSE, NULL, 'Source code',       NULL, -1, -1)
                     , (2,  1, 'idl', 'dict_test_1',           'hash_diff',            2,  -1, 1042, 32,   NULL, FALSE, NULL, 'Hash',              NULL, -1, -1)
                     , (3,  1, 'idl', 'dict_test_1',           'deleted_flg',          3,  -1, 16,   NULL, NULL, FALSE, NULL, 'Deleted',           NULL, -1, -1)
                     , (4,  1, 'idl', 'dict_test_1',           'version_id',           4,  -1, 20,   NULL, NULL, FALSE, NULL, 'Version',           NULL, -1, -1)
                     , (5,  1, 'idl', 'dict_test_1',           'effective_from_date',  5,  -1, 1082, NULL, NULL, FALSE, NULL, 'Effective from',    NULL, -1, -1)
                     , (6,  1, 'idl', 'dict_test_1',           'effective_to_date',    6,  -1, 1082, NULL, NULL, FALSE, NULL, 'Effective to',      NULL, -1, -1)
                     , (7,  1, 'idl', 'dict_test_1',           'test_1_cd',            7,  -1, 25,   NULL, NULL, TRUE,  NULL, 'Test 1 code',       NULL, -1, -1)
                     , (8,  1, 'idl', 'dict_test_1',           'test_1_name',          6,  -1, 25,   NULL, NULL, TRUE,  NULL, 'Test 1 name',       NULL, -1, -1)
                     
                     , (9,  2, 'idl', 'hub_test_2',            'src_cd',               1,  -1, 25,   NULL, NULL, FALSE, NULL, 'Source code',       NULL, -1, -1)
                     , (10, 2, 'idl', 'hub_test_2',            'hash_diff',            2,  -1, 1042, 32,   NULL, FALSE, NULL, 'Hash',              NULL, -1, -1)
                     , (11, 2, 'idl', 'hub_test_2',            'deleted_flg',          3,  -1, 16,   NULL, NULL, FALSE, NULL, 'Deleted',           NULL, -1, -1)
                     , (12, 2, 'idl', 'hub_test_2',            'version_id',           4,  -1, 20,   NULL, NULL, FALSE, NULL, 'Version',           NULL, -1, -1)
                     , (13, 2, 'idl', 'hub_test_2',            'test_2_rk',            5,  -1, 20,   NULL, NULL, TRUE,  NULL, 'Test 2 key',        1,    -1, -1)
                     , (14, 2, 'idl', 'hub_test_2',            'test_2_id',            6,  -1, 25,   NULL, NULL, TRUE,  NULL, 'Test 2 id',         NULL, -1, -1)
                     , (15, 2, 'idl', 'hub_test_2',            'bk_schema_cd',         7,  -1, 25,   NULL, NULL, TRUE,  NULL, 'Source key type',   2,    -1, -1)
                     
                     , (16, 3, 'idl', 'sal_test_3',            'src_cd',               1,  -1, 25,   NULL, NULL, FALSE, NULL, 'Source code',       NULL, -1, -1)
                     , (17, 3, 'idl', 'sal_test_3',            'hash_diff',            2,  -1, 1042, 32,   NULL, FALSE, NULL, 'Hash',              NULL, -1, -1)
                     , (18, 3, 'idl', 'sal_test_3',            'deleted_flg',          3,  -1, 16,   NULL, NULL, FALSE, NULL, 'Deleted',           NULL, -1, -1)
                     , (19, 3, 'idl', 'sal_test_3',            'version_id',           4,  -1, 20,   NULL, NULL, FALSE, NULL, 'Version',           NULL, -1, -1)
                     , (20, 3, 'idl', 'sal_test_3',            'primary_rk',           5,  -1, 20,   NULL, NULL, TRUE,  NULL, 'Test 3 rdv key',    NULL, -1, -1)
                     , (21, 3, 'idl', 'sal_test_3',            'secondary_rk',         6,  -1, 25,   NULL, NULL, TRUE,  NULL, 'Test 3 idl key',    1,    -1, -1)
                     , (22, 3, 'idl', 'sal_test_3',            'bk_schema_cd',         7,  -1, 25,   NULL, NULL, TRUE,  NULL, 'Source key type',   2,    -1, -1)
                     
                     , (23, 4, 'idl', 'bbridge_test_4',        'src_cd',               1,  -1, 25,   NULL, NULL, FALSE, NULL, 'Source code',       NULL, -1, -1)
                     , (24, 4, 'idl', 'bbridge_test_4',        'hash_diff',            2,  -1, 1042, 32,   NULL, FALSE, NULL, 'Hash',              NULL, -1, -1)
                     , (25, 4, 'idl', 'bbridge_test_4',        'deleted_flg',          3,  -1, 16,   NULL, NULL, FALSE, NULL, 'Deleted',           NULL, -1, -1)
                     , (26, 4, 'idl', 'bbridge_test_4',        'version_id',           4,  -1, 20,   NULL, NULL, FALSE, NULL, 'Version',           NULL, -1, -1)
                     , (27, 4, 'idl', 'bbridge_test_4',        'effective_from_date',  5,  -1, 1082, NULL, NULL, FALSE, NULL, 'Effective from',    NULL, -1, -1)
                     , (28, 4, 'idl', 'bbridge_test_4',        'effective_to_date',    6,  -1, 1082, NULL, NULL, FALSE, NULL, 'Effective to',      NULL, -1, -1)
                     , (29, 4, 'idl', 'bbridge_test_4',        'test_4_rk',            7,  -1, 20,   NULL, NULL, FALSE, NULL, 'Test 4 key',        1,    -1, -1)
                     , (30, 4, 'idl', 'bbridge_test_4',        'test_4_code',          8,  -1, 25,   NULL, NULL, TRUE,  NULL, 'Test 4 code',       NULL, -1, -1)
                     , (31, 4, 'idl', 'bbridge_test_4',        'test_4_cd',            9,  -1, 25,   NULL, NULL, TRUE,  NULL, 'Test 4 cd',         NULL, -1, -1)
                     , (32, 4, 'idl', 'bbridge_test_4',        'test_4_num',           10, -1, 1700, NULL, NULL, TRUE,  NULL, 'Test 4 num',        NULL, -1, -1)
                     , (33, 4, 'idl', 'bbridge_test_4',        'test_4_txt_num',       11, -1, 25,   NULL, NULL, TRUE,  NULL, 'Test 4 num',        NULL, -1, -1)
                     , (34, 4, 'idl', 'bbridge_test_4',        'test_4_no',            12, -1, 1700, NULL, NULL, TRUE,  NULL, 'Test 4 no',         NULL, -1, -1)
                     , (35, 4, 'idl', 'bbridge_test_4',        'test_4_id',            13, -1, 25,   NULL, NULL, TRUE,  NULL, 'Test 4 id',         NULL, -1, -1)
                     , (36, 4, 'idl', 'bbridge_test_4',        'test_4_name',          14, -1, 25,   NULL, NULL, TRUE,  NULL, 'Test 4 name',       NULL, -1, -1)
                     , (37, 4, 'idl', 'bbridge_test_4',        'test_4_desc',          15, -1, 25,   NULL, NULL, TRUE,  NULL, 'Test 4 desc',       NULL, -1, -1)
                     , (38, 4, 'idl', 'bbridge_test_4',        'test_4_txt',           16, -1, 25,   NULL, NULL, TRUE,  NULL, 'Test 4 txt',        NULL, -1, -1)
                     , (39, 4, 'idl', 'bbridge_test_4',        'test_4_flg',           17, -1, 16,   NULL, NULL, TRUE,  NULL, 'Test 4 flg',        NULL, -1, -1)
                     , (40, 4, 'idl', 'bbridge_test_4',        'test_4_date',          18, -1, 1082, NULL, NULL, TRUE,  NULL, 'Test 4 date',       NULL, -1, -1)
                     , (41, 4, 'idl', 'bbridge_test_4',        'test_4_dttm',          19, -1, 1083, NULL, NULL, TRUE,  NULL, 'Test 4 dttm',       NULL, -1, -1)
                     , (42, 4, 'idl', 'bbridge_test_4',        'test_4_sum',           20, -1, 1700, NULL, NULL, TRUE,  NULL, 'Test 4 sum',        NULL, -1, -1)
                     , (43, 4, 'idl', 'bbridge_test_4',        'test_4_cnt',           21, -1, 1700, NULL, NULL, TRUE,  NULL, 'Test 4 cnt',        NULL, -1, -1)
                     , (44, 4, 'idl', 'bbridge_test_4',        'test_4_pc',            22, -1, 1700, 5,    2,    TRUE,  NULL, 'Test 4 pc',         NULL, -1, -1)
                     , (45, 4, 'idl', 'bbridge_test_4',        'test_4_rate',          23, -1, 1700, NULL, NULL, TRUE,  NULL, 'Test 4 rate',       NULL, -1, -1)
                     , (46, 4, 'idl', 'bbridge_test_4',        'test_4_score',         24, -1, 1700, NULL, NULL, TRUE,  NULL, 'Test 4 score',      NULL, -1, -1)
                     , (47, 4, 'idl', 'bbridge_test_4',        'test_4_uid',           25, -1, 2950, NULL, NULL, TRUE,  NULL, 'Test 4 uid',        NULL, -1, -1)
                     , (48, 4, 'idl', 'bbridge_test_4',        'test_4_url',           26, -1, 25,   NULL, NULL, TRUE,  NULL, 'Test 4 url',        NULL, -1, -1)
                     , (49, 4, 'idl', 'bbridge_test_4',        'test_4_duration',      27, -1, 1186, NULL, NULL, TRUE,  NULL, 'Test 4 duration',   NULL, -1, -1)
                     , (50, 4, 'idl', 'bbridge_test_4',        'test_4_num_duration',  28, -1, 20,   NULL, NULL, TRUE,  NULL, 'Test 4 duration',   NULL, -1, -1)
                     , (51, 4, 'idl', 'bbridge_test_4',        'test_4_txt_duration',  29, -1, 25,   NULL, NULL, TRUE,  NULL, 'Test 4 duration',   NULL, -1, -1)
                     , (52, 4, 'idl', 'bbridge_test_4',        'test_4_json',          30, -1, 114,  NULL, NULL, TRUE,  NULL, 'Test 4 json',       NULL, -1, -1)
                     , (53, 4, 'idl', 'bbridge_test_4',        'test_4_b_json',        30, -1, 3802, NULL, NULL, TRUE,  NULL, 'Test 4 json',       NULL, -1, -1)
                     , (54, 4, 'idl', 'bbridge_test_4',        'test_4_regexp',        31, -1, 25,   NULL, NULL, TRUE,  NULL, 'Test 4 regexp',     NULL, -1, -1)
                     , (55, 4, 'idl', 'bbridge_test_4',        'test_4_version',       32, -1, 25,   NULL, NULL, TRUE,  NULL, 'Test 4 version',    NULL, -1, -1)
                     , (56, 4, 'idl', 'bbridge_test_4',        'test_4_tbi',           33, -1, 20,   NULL, NULL, TRUE,  '-1', 'Test 4 tbi',        NULL, -1, -1)
                     
                     , (57, 5, 'idl', 'bbridge_test_5',        'src_cd',               1,  -1, 25,   NULL, NULL, FALSE, NULL, 'Source code',       NULL, -1, -1)
                     , (58, 5, 'idl', 'bbridge_test_5',        'hash_diff',            2,  -1, 1042, 32,   NULL, FALSE, NULL, 'Hash',              NULL, -1, -1)
                     , (59, 5, 'idl', 'bbridge_test_5',        'deleted_flg',          3,  -1, 16,   NULL, NULL, FALSE, NULL, 'Deleted',           NULL, -1, -1)
                     , (60, 5, 'idl', 'bbridge_test_5',        'version_id',           4,  -1, 20,   NULL, NULL, FALSE, NULL, 'Version',           NULL, -1, -1)
                     , (61, 5, 'idl', 'bbridge_test_5',        'to_version_id',        5,  -1, 20,   NULL, NULL, FALSE, NULL, 'Version to',        NULL, -1, -1)
                     , (62, 5, 'idl', 'bbridge_test_5',        'effective_date',       6,  -1, 1082, NULL, NULL, FALSE, NULL, 'Effective',         NULL, -1, -1)
                     , (63, 5, 'idl', 'bbridge_test_5',        'test_5_rk',            7,  -1, 20,   NULL, NULL, FALSE, NULL, 'Test 5 key',        1,    -1, -1)
                     , (64, 5, 'idl', 'bbridge_test_5',        'test_5_cd',            8,  -1, 25,   NULL, NULL, TRUE,  NULL, 'Test 5 code',       NULL, -1, -1)
                     
                     , (65, 6, 'idl', 'tbridge_test_6',        'src_cd',               1,  -1, 25,   NULL, NULL, FALSE, NULL, 'Source code',       NULL, -1, -1)
                     , (66, 6, 'idl', 'tbridge_test_6',        'hash_diff',            2,  -1, 1042, 32,   NULL, FALSE, NULL, 'Hash',              NULL, -1, -1)
                     , (67, 6, 'idl', 'tbridge_test_6',        'deleted_flg',          3,  -1, 16,   NULL, NULL, FALSE, NULL, 'Deleted',           NULL, -1, -1)
                     , (68, 6, 'idl', 'tbridge_test_6',        'version_id',           4,  -1, 20,   NULL, NULL, FALSE, NULL, 'Version',           NULL, -1, -1)
                     , (69, 6, 'idl', 'tbridge_test_6',        'effective_dttm',       5,  -1, 1083, NULL, NULL, FALSE, NULL, 'Effective',         NULL, -1, -1)
                     , (70, 6, 'idl', 'tbridge_test_6',        'test_6_rk',            6,  -1, 20,   NULL, NULL, FALSE, NULL, 'Test 6 key',        1,    -1, -1)
                     , (71, 6, 'idl', 'tbridge_test_6',        'test_6_cd',            7,  -1, 25,   NULL, NULL, TRUE,  NULL, 'Test 6 code',       NULL, -1, -1)
                     
                     , (72, 7, 'idl', 'blink_test_4_x_test_5', 'src_cd',               1,  -1, 25,   NULL, NULL, FALSE, NULL, 'Source code',       NULL, -1, -1)
                     , (73, 7, 'idl', 'blink_test_4_x_test_5', 'hash_diff',            2,  -1, 1042, 32,   NULL, FALSE, NULL, 'Hash',              NULL, -1, -1)
                     , (74, 7, 'idl', 'blink_test_4_x_test_5', 'deleted_flg',          3,  -1, 16,   NULL, NULL, FALSE, NULL, 'Deleted',           NULL, -1, -1)
                     , (75, 7, 'idl', 'blink_test_4_x_test_5', 'version_id',           4,  -1, 20,   NULL, NULL, FALSE, NULL, 'Version',           NULL, -1, -1)
                     , (76, 7, 'idl', 'blink_test_4_x_test_5', 'effective_from_date',  5,  -1, 1082, NULL, NULL, FALSE, NULL, 'Effective from',    NULL, -1, -1)
                     , (77, 7, 'idl', 'blink_test_4_x_test_5', 'effective_to_date',    6,  -1, 1082, NULL, NULL, FALSE, NULL, 'Effective to',      NULL, -1, -1)
                     , (78, 7, 'idl', 'blink_test_4_x_test_5', 'test_4_rk',            7,  -1, 20,   NULL, NULL, FALSE, NULL, 'Test 4 key',        1,    -1, -1)
                     , (79, 7, 'idl', 'blink_test_4_x_test_5', 'test_5_rk',            8,  -1, 29,   NULL, NULL, FALSE, NULL, 'Test 5 key',        NULL, -1, -1)
                     , (80, 7, 'idl', 'blink_test_4_x_test_5', 'test_4_5_cd',          9,  -1, 25,   NULL, NULL, TRUE,  NULL, 'Test 4 5 code',     NULL, -1, -1)
                     
                     , (81, 8, 'idl', 'tlink_test_6_x_test_6', 'src_cd',               1,  -1, 25,   NULL, NULL, FALSE, NULL, 'Source code',       NULL, -1, -1)
                     , (82, 8, 'idl', 'tlink_test_6_x_test_6', 'hash_diff',            2,  -1, 1042, 32,   NULL, FALSE, NULL, 'Hash',              NULL, -1, -1)
                     , (83, 8, 'idl', 'tlink_test_6_x_test_6', 'deleted_flg',          3,  -1, 16,   NULL, NULL, FALSE, NULL, 'Deleted',           NULL, -1, -1)
                     , (84, 8, 'idl', 'tlink_test_6_x_test_6', 'version_id',           4,  -1, 20,   NULL, NULL, FALSE, NULL, 'Version',           NULL, -1, -1)
                     , (85, 8, 'idl', 'tlink_test_6_x_test_6', 'to_version_id',        4,  -1, 20,   NULL, NULL, FALSE, NULL, 'Version to',        NULL, -1, -1)
                     , (86, 8, 'idl', 'tlink_test_6_x_test_6', 'effective_from_dttm',  5,  -1, 1083, NULL, NULL, FALSE, NULL, 'Effective from',    NULL, -1, -1)
                     , (87, 8, 'idl', 'tlink_test_6_x_test_6', 'effective_to_dttm',    6,  -1, 1083, NULL, NULL, FALSE, NULL, 'Effective to',      NULL, -1, -1)
                     , (88, 8, 'idl', 'tlink_test_6_x_test_6', 'test_6_child_rk',      7,  -1, 20,   NULL, NULL, FALSE, NULL, 'Test 6 child key',  1,    -1, -1)
                     , (89, 8, 'idl', 'tlink_test_6_x_test_6', 'test_6_parent_rk',     8,  -1, 29,   NULL, NULL, FALSE, NULL, 'Test 6 parent key', NULL, -1, -1)
                     , (90, 8, 'idl', 'tlink_test_6_x_test_6', 'test_6_6_cd',          9,  -1, 25,   NULL, NULL, TRUE,  NULL, 'Test 6 6 code',     NULL, -1, -1)
               ) a (physical_attribute_rk
                  , physical_table_rk
                  , schema_name
                  , table_name
                  , attribute_name
                  , attribute_no
                  , data_type_rk
                  , data_type_id
                  , data_type_size_cnt
                  , data_type_scale_cnt
                  , is_nullable_flg
                  , default_value_txt
                  , attribute_desc
                  , distribution_no
                  , data_domain_rk
                  , metadata_type_rk)
    """)
    )

    db_session.execute(text("TRUNCATE metamodel.bridge_physical_key"))
    db_session.execute(
        text("""
        INSERT INTO metamodel.bridge_physical_key (effective_from_dttm
                                                 , effective_to_dttm
                                                 , deleted_flg
                                                 , version_rk
                                                 , physical_key_rk
                                                 , key_name
                                                 , schema_name
                                                 , physical_table_rk
                                                 , table_name)
        SELECT '2025-01-01 +0' effective_from_dttm
             , '2999-12-31 +0' effective_to_dttm
             , FALSE           deleted_flg
             , 1               version_rk
             , physical_key_rk
             , key_name
             , schema_name
             , physical_table_rk
             , table_name
          FROM (
                VALUES (1, 'dict_test_1_dk',           'idl', 1, 'dict_test_1')
                     , (2, 'hub_test_2_dk',            'idl', 2, 'hub_test_2')
                     , (3, 'sal_test_3_dk',            'idl', 3, 'sal_test_3')
                     , (4, 'bbridge_test_4_dk',        'idl', 4, 'bbridge_test_4')
                     , (5, 'bbridge_test_5_dk',        'idl', 5, 'bbridge_test_5')
                     , (6, 'tbridge_test_6_dk',        'idl', 6, 'tbridge_test_6')
                     , (7, 'blink_test_4_x_test_5_dk', 'idl', 7, 'blink_test_4_x_test_5')
                     , (8, 'tlink_test_6_x_test_6_dk', 'idl', 8, 'tlink_test_6_x_test_6')
               ) t (physical_key_rk
                  , key_name
                  , schema_name
                  , physical_table_rk
                  , table_name)
    """)
    )

    db_session.execute(text("TRUNCATE metamodel.link_physical_key_attribute"))
    db_session.execute(
        text("""
        INSERT INTO metamodel.link_physical_key_attribute (effective_from_dttm
                                                         , effective_to_dttm
                                                         , deleted_flg
                                                         , version_rk
                                                         , physical_key_rk
                                                         , physical_attribute_rk
                                                         , schema_name
                                                         , key_name
                                                         , table_name
                                                         , attribute_name
                                                         , attribute_no)
        SELECT '2025-01-01 +0' effective_from_dttm
             , '2999-12-31 +0' effective_to_dttm
             , FALSE           deleted_flg
             , 1               version_rk
             , physical_key_rk
             , physical_attribute_rk
             , schema_name
             , key_name
             , table_name
             , attribute_name
             , attribute_no
          FROM (
                VALUES (1, 7,  'dict_test_1_dk',           'idl', 'dict_test_1',           'test_1_cd',        1)
                     , (2, 14, 'hub_test_2_dk',            'idl', 'hub_test_2',            'test_2_id',        1)
                     , (2, 15, 'hub_test_2_dk',            'idl', 'hub_test_2',            'bk_schema_cd',     2)
                     , (3, 21, 'sal_test_3_dk',            'idl', 'sal_test_3',            'secondary_rk',     1)
                     , (3, 22, 'sal_test_3_dk',            'idl', 'sal_test_3',            'bk_schema_cd',     2)
                     , (4, 29, 'bbridge_test_4_dk',        'idl', 'bbridge_test_4',        'test_4_rk',        1)
                     , (5, 63, 'bbridge_test_5_dk',        'idl', 'bbridge_test_5',        'test_5_rk',        1)
                     , (6, 70, 'tbridge_test_6_dk',        'idl', 'tbridge_test_6',        'test_6_rk',        1)
                     , (7, 78, 'blink_test_4_x_test_5_dk', 'idl', 'blink_test_4_x_test_5', 'test_4_rk',        1)
                     , (7, 79, 'blink_test_4_x_test_5_dk', 'idl', 'blink_test_4_x_test_5', 'test_5_rk',        2)
                     , (8, 88, 'tlink_test_6_x_test_6_dk', 'idl', 'tlink_test_6_x_test_6', 'test_6_child_rk',  1)
                     , (8, 89, 'tlink_test_6_x_test_6_dk', 'idl', 'tlink_test_6_x_test_6', 'test_6_parent_rk', 2)
               ) t (physical_key_rk
                  , physical_attribute_rk
                  , key_name
                  , schema_name
                  , table_name
                  , attribute_name
                  , attribute_no)
    """)
    )

    db_session.execute(text("TRUNCATE metamodel.bridge_logical_domain"))
    db_session.execute(
        text("""
        INSERT INTO metamodel.bridge_logical_domain (effective_from_dttm
                                                   , effective_to_dttm
                                                   , deleted_flg
                                                   , version_rk
                                                   , logical_domain_rk
                                                   , data_layer_rk
                                                   , domain_rk
                                                   , domain_name
                                                   , model_major_version_num
                                                   , model_minor_version_num
                                                   , model_effective_date)
        SELECT '2025-01-01 +0' effective_from_dttm
             , '2999-12-31 +0' effective_to_dttm
             , FALSE           deleted_flg
             , 1               version_rk
            , logical_domain_rk
            , data_layer_rk
            , domain_rk
            , domain_name
            , model_major_version_num
            , model_minor_version_num
            , '2025-01-01 +0' model_effective_date
          FROM (
                VALUES (1, 3, 1, 'вовлеченная сторона', 1, 0)
                     , (2, 3, 2, 'главная книга',       2, 3)
               ) d (logical_domain_rk
                  , data_layer_rk
                  , domain_rk
                  , domain_name
                  , model_major_version_num
                  , model_minor_version_num)
    """)
    )

    db_session.execute(text("TRUNCATE metamodel.bridge_logical_table"))
    db_session.execute(
        text("""
        INSERT INTO metamodel.bridge_logical_table (effective_from_dttm
                                                  , effective_to_dttm
                                                  , deleted_flg
                                                  , version_rk
                                                  , logical_table_rk
                                                  , source_rk
                                                  , source_cd
                                                  , data_layer_rk
                                                  , schema_name
                                                  , table_name
                                                  , table_extra_name
                                                  , table_desc
                                                  , table_extra_desc
                                                  , is_dict_flg
                                                  , is_map_flg
                                                  , is_hub_flg
                                                  , is_sal_flg
                                                  , is_mart_flg
                                                  , is_bridge_flg
                                                  , is_link_flg
                                                  , table_data_vault_type_cd
                                                  , version_scd_type_rk
                                                  , history_scd_type_rk
                                                  , attribute_cnt
                                                  , is_ref_flg
                                                  , logical_domain_rk
                                                  , domain_rk
                                                  , domain_name
                                                  , sub_domain_name
                                                  , extra_sub_domain_name
                                                  , axon_id
                                                  , axon_url
                                                  , model_major_version_from_num
                                                  , model_minor_version_from_num
                                                  , model_major_version_to_num
                                                  , model_minor_version_to_num
                                                  , is_deprecated_flg
                                                  , model_effective_date)
        SELECT '2025-01-01 +0' effective_from_dttm
             , '2999-12-31 +0' effective_to_dttm
             , FALSE           deleted_flg
             , 1               version_rk
             , logical_table_rk
             , 1      source_rk
             , 'DTPL' source_cd
             , data_layer_rk
             , schema_name
             , table_name
             , NULL table_extra_name
             , table_desc
             , NULL table_extra_desc
             , is_dict_flg
             , is_map_flg
             , is_hub_flg
             , is_sal_flg
             , is_mart_flg
             , is_bridge_flg
             , is_link_flg
             , table_data_vault_type_cd
             , version_scd_type_rk
             , history_scd_type_rk
             , attribute_cnt
             , FALSE is_ref_flg
             , logical_domain_rk
             , domain_rk
             , domain_name
             , NULL sub_domain_name
             , NULL extra_sub_domain_name
             , NULL axon_id
             , NULL axon_url
             , model_major_version_from_num
             , model_minor_version_from_num
             , model_major_version_to_num
             , model_minor_version_to_num
             , is_deprecated_flg
             , '2025-01-01 +0' model_effective_date
          FROM (
                VALUES (1,  3, 'idl', 'dict_test_1',           'Dict Test 1',            TRUE,  FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, 'IDL Dictionary',   2, 3, 9,  1, 1, 'главная книга',       1, 0, NULL, NULL, FALSE)
                     , (2,  3, 'idl', 'hub_test_2',            'Hub Test 2',             FALSE, FALSE, TRUE,  FALSE, FALSE, FALSE, FALSE, 'IDL Hub',          2, 3, 7,  1, 1, 'главная книга',       1, 0, NULL, NULL, FALSE)
                     , (3,  3, 'idl', 'sal_test_3',            'SAL Test 3',             FALSE, FALSE, FALSE, TRUE,  FALSE, FALSE, FALSE, 'IDL Same-As-Link', 2, 3, 7,  1, 1, 'главная книга',       1, 0, NULL, NULL, FALSE)
                     , (4,  3, 'idl', 'bbridge_test_4',        'BBridge Test 4',         FALSE, FALSE, FALSE, FALSE, FALSE, TRUE,  FALSE, 'IDL Main Table',   2, 3, 33, 1, 1, 'главная книга',       1, 0, NULL, NULL, FALSE)
                     , (5,  3, 'idl', 'bbridge_test_5',        'BBridge Test 5',         FALSE, FALSE, FALSE, FALSE, FALSE, TRUE,  FALSE, 'IDL Bbridge',      3, 2, 8,  1, 1, 'главная книга',       1, 0, NULL, NULL, FALSE)
                     , (6,  3, 'idl', 'tbridge_test_6',        'TBridge Test 6',         FALSE, FALSE, FALSE, FALSE, FALSE, TRUE,  FALSE, 'IDL Tbridge',      2, 2, 7,  2, 2, 'вовлеченная сторона', 1, 0, 2,    0,    TRUE)
                     , (7,  3, 'idl', 'blink_test_4_x_test_5', 'BLink Test 4 to Test 5', FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, TRUE,  'IDL Blink',        2, 3, 9,  1, 1, 'главная книга',       1, 0, NULL, NULL, FALSE)
                     , (8,  3, 'idl', 'tlink_test_6_x_test_6', 'TLink Test 6 to Test 6', FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, TRUE,  'IDL Tlink',        3, 3, 9,  2, 2, 'вовлеченная сторона', 2, 3, 3,    1,    FALSE)
                     , (9,  4, 'bdm', 'dict_test_1',           'Dict Test 1',            TRUE,  FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, 'BDM Dictionary',   2, 3, 8,  1, 1, 'главная книга',       1, 0, NULL, NULL, FALSE)
                     , (10, 4, 'bdm', 'test_4',                'Test 4',                 FALSE, FALSE, FALSE, FALSE, FALSE, TRUE,  FALSE, 'BDM Main Table',   2, 3, 32, 1, 1, 'главная книга',       1, 0, NULL, NULL, FALSE)
                     , (11, 4, 'bdm', 'test_5',                'Test 5',                 FALSE, FALSE, FALSE, FALSE, FALSE, TRUE,  FALSE, 'BDM Main Table',   2, 3, 7,  1, 1, 'главная книга',       1, 0, NULL, NULL, FALSE)
                     , (12, 4, 'bdm', 'test_4_x_test_5',       'Link Test 4 to Test 5',  FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, TRUE,  'BDM Relation',     2, 3, 7,  1, 1, 'главная книга',       1, 0, NULL, NULL, FALSE)
               ) t (logical_table_rk
                  , data_layer_rk
                  , schema_name
                  , table_name
                  , table_desc
                  , is_dict_flg
                  , is_map_flg
                  , is_hub_flg
                  , is_sal_flg
                  , is_mart_flg
                  , is_bridge_flg
                  , is_link_flg
                  , table_data_vault_type_cd
                  , version_scd_type_rk
                  , history_scd_type_rk
                  , attribute_cnt
                  , logical_domain_rk
                  , domain_rk
                  , domain_name
                  , model_major_version_from_num
                  , model_minor_version_from_num
                  , model_major_version_to_num
                  , model_minor_version_to_num
                  , is_deprecated_flg)
    """)
    )

    db_session.execute(text("TRUNCATE metamodel.bridge_logical_attribute"))
    db_session.execute(
        text("""
        INSERT INTO metamodel.bridge_logical_attribute (effective_from_dttm
                                                      , effective_to_dttm
                                                      , deleted_flg
                                                      , version_rk
                                                      , logical_attribute_rk
                                                      , record_no
                                                      , schema_name
                                                      , logical_table_rk
                                                      , table_name
                                                      , attribute_name
                                                      , attribute_extra_name
                                                      , attribute_desc
                                                      , attribute_extra_desc
                                                      , data_type_rk
                                                      , data_type_cd
                                                      , data_type_size_cnt
                                                      , data_type_scale_cnt
                                                      , is_nullable_flg
                                                      , data_domain_rk
                                                      , data_domain_cd
                                                      , is_metadata_flg
                                                      , is_primary_key_flg
                                                      , is_foreign_key_flg
                                                      , foreign_schema_name
                                                      , foreign_table_name
                                                      , foreign_attribute_name
                                                      , axon_id
                                                      , axon_url
                                                      , model_major_version_from_num
                                                      , model_minor_version_from_num
                                                      , model_major_version_to_num
                                                      , model_minor_version_to_num
                                                      , is_deprecated_flg
                                                      , model_effective_date)
        SELECT '2025-01-01 +0' effective_from_dttm
             , '2999-12-31 +0' effective_to_dttm
             , FALSE           deleted_flg
             , 1               version_rk
             , logical_attribute_rk
             , logical_attribute_rk + 1 record_no
             , schema_name
             , logical_table_rk
             , table_name
             , attribute_name
             , NULL attribute_extra_name
             , attribute_desc
             , NULL attribute_extra_desc
             , data_type_rk
             , data_type_cd
             , data_type_size_cnt
             , data_type_scale_cnt::SMALLINT
             , is_nullable_flg
             , data_domain_rk
             , data_domain_cd
             , is_metadata_flg
             , is_primary_key_flg
             , is_foreign_key_flg
             , foreign_schema_name
             , foreign_table_name
             , foreign_attribute_name
             , NULL axon_id
             , NULL axon_url
             , model_major_version_from_num
             , model_minor_version_from_num
             , model_major_version_to_num
             , model_minor_version_to_num
             , is_deprecated_flg
             , '2025-01-01 +0' model_effective_date
          FROM (
                VALUES (1,  1, 'idl', 'dict_test_1',           'src_cd',              'Source code',       16, 'TEXT',      NULL, NULL, FALSE, 17, 'dCodeIdentificator',      TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (2,  1, 'idl', 'dict_test_1',           'hash_diff',           'Hash',              14, 'CHAR',      32,   NULL, FALSE, 8,  'dHashIdentificator',      TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (3,  1, 'idl', 'dict_test_1',           'deleted_flg',         'Deleted',           1,  'BOOLEAN',   NULL, NULL, FALSE, 4,  'dDeletedFlag',            TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (4,  1, 'idl', 'dict_test_1',           'version_id',          'Version',           4,  'BIGINT',    NULL, NULL, FALSE, 2,  'dVersionIdentificator',   TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (5,  1, 'idl', 'dict_test_1',           'effective_from_date', 'Effective from',    8,  'DATE',      NULL, NULL, FALSE, 6,  'dEffectiveBeginDate',     TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (6,  1, 'idl', 'dict_test_1',           'effective_to_date',   'Effective to',      8,  'DATE',      NULL, NULL, FALSE, 7,  'dEffectiveEndDate',       TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (7,  1, 'idl', 'dict_test_1',           'test_1_cd',           'Test 1 code',       16, 'TEXT',      NULL, NULL, TRUE,  17, 'dCodeIdentificator',      FALSE, TRUE,  FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (8,  1, 'idl', 'dict_test_1',           'test_1_name',         'Test 1 name',       16, 'TEXT',      NULL, NULL, TRUE,  23, 'dName',                   FALSE, FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)

                     , (9,  2, 'idl', 'hub_test_2',            'src_cd',              'Source code',       16, 'TEXT',      NULL, NULL, FALSE, 17, 'dCodeIdentificator',      TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (10, 2, 'idl', 'hub_test_2',            'hash_diff',           'Hash',              14, 'CHAR',      32,   NULL, FALSE, 8,  'dHashIdentificator',      TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (11, 2, 'idl', 'hub_test_2',            'deleted_flg',         'Deleted',           1,  'BOOLEAN',   NULL, NULL, FALSE, 4,  'dDeletedFlag',            TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (12, 2, 'idl', 'hub_test_2',            'version_id',          'Version',           4,  'BIGINT',    NULL, NULL, FALSE, 2,  'dVersionIdentificator',   TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (13, 2, 'idl', 'hub_test_2',            'test_2_rk',           'Test 2 key',        4,  'BIGINT',    NULL, NULL, TRUE,  13, 'dRetainedKey',            FALSE, FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (14, 2, 'idl', 'hub_test_2',            'test_2_id',           'Test 2 id',         16, 'TEXT',      NULL, NULL, TRUE,  21, 'dIdentificator',          FALSE, TRUE,  FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (15, 2, 'idl', 'hub_test_2',            'bk_schema_cd',        'Source key type',   16, 'TEXT',      NULL, NULL, TRUE,  17, 'dCodeIdentificator',      FALSE, TRUE,  FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     
                     , (16, 3, 'idl', 'sal_test_3',            'src_cd',              'Source code',       16, 'TEXT',      NULL, NULL, FALSE, 17, 'dCodeIdentificator',      TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (17, 3, 'idl', 'sal_test_3',            'hash_diff',           'Hash',              14, 'CHAR',      32,   NULL, FALSE, 8,  'dHashIdentificator',      TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (18, 3, 'idl', 'sal_test_3',            'deleted_flg',         'Deleted',           1,  'BOOLEAN',   NULL, NULL, FALSE, 4,  'dDeletedFlag',            TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (19, 3, 'idl', 'sal_test_3',            'version_id',          'Version',           4,  'BIGINT',    NULL, NULL, FALSE, 2,  'dVersionIdentificator',   TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (20, 3, 'idl', 'sal_test_3',            'primary_rk',          'Test 3 rdv key',    4,  'BIGINT',    NULL, NULL, TRUE,  13, 'dRetainedKey',            FALSE, FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (21, 3, 'idl', 'sal_test_3',            'secondary_rk',        'Test 3 idl key',    4,  'BIGINT',    NULL, NULL, TRUE,  13, 'dRetainedKey',            FALSE, TRUE,  FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (22, 3, 'idl', 'sal_test_3',            'bk_schema_cd',        'Source key type',   16, 'TEXT',      NULL, NULL, TRUE,  17, 'dCodeIdentificator',      FALSE, TRUE,  FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     
                     , (23, 4, 'idl', 'bbridge_test_4',        'src_cd',              'Source code',       16, 'TEXT',      NULL, NULL, FALSE, 17, 'dCodeIdentificator',      TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (24, 4, 'idl', 'bbridge_test_4',        'hash_diff',           'Hash',              14, 'CHAR',      32,   NULL, FALSE, 8,  'dHashIdentificator',      TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (25, 4, 'idl', 'bbridge_test_4',        'deleted_flg',         'Deleted',           1,  'BOOLEAN',   NULL, NULL, FALSE, 4,  'dDeletedFlag',            TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (26, 4, 'idl', 'bbridge_test_4',        'version_id',          'Version',           4,  'BIGINT',    NULL, NULL, FALSE, 2,  'dVersionIdentificator',   TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (27, 4, 'idl', 'bbridge_test_4',        'effective_from_date', 'Effective from',    8,  'DATE',      NULL, NULL, FALSE, 6,  'dEffectiveBeginDate',     TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (28, 4, 'idl', 'bbridge_test_4',        'effective_to_date',   'Effective to',      8,  'DATE',      NULL, NULL, FALSE, 7,  'dEffectiveEndDate',       TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (29, 4, 'idl', 'bbridge_test_4',        'test_4_rk',           'Test 4 key',        4,  'BIGINT',    NULL, NULL, FALSE, 13, 'dRetainedKey',            FALSE, TRUE,  FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (30, 4, 'idl', 'bbridge_test_4',        'test_4_code',         'Test 4 code',       16, 'TEXT',      NULL, NULL, TRUE,  16, 'dCode',                   FALSE, FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (31, 4, 'idl', 'bbridge_test_4',        'test_4_cd',           'Test 4 cd',         16, 'TEXT',      NULL, NULL, TRUE,  17, 'dCodeIdentificator',      FALSE, FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (32, 4, 'idl', 'bbridge_test_4',        'test_4_num',          'Test 4 num',        4,  'BIGINT',    NULL, NULL, TRUE,  18, 'dNumber',                 FALSE, FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (33, 4, 'idl', 'bbridge_test_4',        'test_4_txt_num',      'Test 4 num',        16, 'TEXT',      NULL, NULL, TRUE,  19, 'dNumberLarge',            FALSE, FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (34, 4, 'idl', 'bbridge_test_4',        'test_4_no',           'Test 4 no',         4,  'BIGINT',    NULL, NULL, TRUE,  20, 'dNumberOrder',            FALSE, FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (35, 4, 'idl', 'bbridge_test_4',        'test_4_id',           'Test 4 id',         16, 'TEXT',      NULL, NULL, TRUE,  21, 'dIdentificator',          FALSE, FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (36, 4, 'idl', 'bbridge_test_4',        'test_4_name',         'Test 4 name',       16, 'TEXT',      NULL, NULL, TRUE,  23, 'dName',                   FALSE, FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (37, 4, 'idl', 'bbridge_test_4',        'test_4_desc',         'Test 4 desc',       16, 'TEXT',      NULL, NULL, TRUE,  24, 'dDescription',            FALSE, FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (38, 4, 'idl', 'bbridge_test_4',        'test_4_txt',          'Test 4 txt',        16, 'TEXT',      NULL, NULL, TRUE,  25, 'dText',                   FALSE, FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (39, 4, 'idl', 'bbridge_test_4',        'test_4_flg',          'Test 4 flg',        1,  'BOOLEAN',   NULL, NULL, TRUE,  26, 'dFlag',                   FALSE, FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (40, 4, 'idl', 'bbridge_test_4',        'test_4_date',         'Test 4 date',       8,  'DATE',      NULL, NULL, TRUE,  27, 'dDate',                   FALSE, FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (41, 4, 'idl', 'bbridge_test_4',        'test_4_dttm',         'Test 4 dttm',       9,  'TIMESTAMP', NULL, NULL, TRUE,  28, 'dDateTime',               FALSE, FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (42, 4, 'idl', 'bbridge_test_4',        'test_4_sum',          'Test 4 sum',        5,  'NUMERIC',   NULL, NULL, TRUE,  29, 'dSum',                    FALSE, FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (43, 4, 'idl', 'bbridge_test_4',        'test_4_cnt',          'Test 4 cnt',        16, 'BIGINT',    NULL, NULL, TRUE,  30, 'dCount',                  FALSE, FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (44, 4, 'idl', 'bbridge_test_4',        'test_4_pc',           'Test 4 pc',         5,  'NUMERIC',   NULL, NULL, TRUE,  31, 'dPercent',                FALSE, FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (45, 4, 'idl', 'bbridge_test_4',        'test_4_rate',         'Test 4 rate',       5,  'NUMERIC',   NULL, NULL, TRUE,  32, 'dRate',                   FALSE, FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (46, 4, 'idl', 'bbridge_test_4',        'test_4_score',        'Test 4 score',      5,  'NUMERIC',   NULL, NULL, TRUE,  33, 'dScore',                  FALSE, FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (47, 4, 'idl', 'bbridge_test_4',        'test_4_uid',          'Test 4 uid',        17, 'UUID',      NULL, NULL, TRUE,  34, 'dUUID',                   FALSE, FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (48, 4, 'idl', 'bbridge_test_4',        'test_4_url',          'Test 4 url',        16, 'TEXT',      NULL, NULL, TRUE,  35, 'dURL',                    FALSE, FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (49, 4, 'idl', 'bbridge_test_4',        'test_4_duration',     'Test 4 duration',   16, 'TEXT',      NULL, NULL, TRUE,  37, 'dInterval',               FALSE, FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (50, 4, 'idl', 'bbridge_test_4',        'test_4_num_duration', 'Test 4 duration',   4,  'BIGINT',    NULL, NULL, TRUE,  38, 'dIntervalSeconds',        FALSE, FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (51, 4, 'idl', 'bbridge_test_4',        'test_4_txt_duration', 'Test 4 duration',   16, 'TEXT',      NULL, NULL, TRUE,  36, 'dDuration',               FALSE, FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (52, 4, 'idl', 'bbridge_test_4',        'test_4_json',         'Test 4 json',       19, 'JSON',      NULL, NULL, TRUE,  39, 'dJSON',                   FALSE, FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (53, 4, 'idl', 'bbridge_test_4',        'test_4_b_json',       'Test 4 json',       19, 'JSON',      NULL, NULL, TRUE,  39, 'dJSON',                   FALSE, FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (54, 4, 'idl', 'bbridge_test_4',        'test_4_regexp',       'Test 4 regexp',     16, 'TEXT',      NULL, NULL, TRUE,  40, 'dRegexp',                 FALSE, FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (55, 4, 'idl', 'bbridge_test_4',        'test_4_version',      'Test 4 version',    16, 'TEXT',      NULL, NULL, TRUE,  41, 'dVersion',                FALSE, FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (56, 4, 'idl', 'bbridge_test_4',        'test_4_tbi',          'Test 4 tbi',        4,  'BIGINT',    NULL, NULL, TRUE,  42, 'dTechnicalBigint',        FALSE, FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                       
                     , (57, 5, 'idl', 'bbridge_test_5',        'src_cd',              'Source code',       16, 'TEXT',      NULL, NULL, FALSE, 17, 'dCodeIdentificator',      TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (58, 5, 'idl', 'bbridge_test_5',        'hash_diff',           'Hash',              14, 'CHAR',      32,   NULL, FALSE, 8,  'dHashIdentificator',      TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (59, 5, 'idl', 'bbridge_test_5',        'deleted_flg',         'Deleted',           1,  'BOOLEAN',   NULL, NULL, FALSE, 4,  'dDeletedFlag',            TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (60, 5, 'idl', 'bbridge_test_5',        'version_id',          'Version',           4,  'BIGINT',    NULL, NULL, FALSE, 2,  'dVersionIdentificator',   TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (61, 5, 'idl', 'bbridge_test_5',        'to_version_id',       'Version to',        4,  'BIGINT',    NULL, NULL, FALSE, 3,  'dToVersionIdentificator', TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (62, 5, 'idl', 'bbridge_test_5',        'effective_date',      'Effective',         8,  'DATE',      NULL, NULL, FALSE, 5,  'dEffectiveDate',          FALSE, FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (63, 5, 'idl', 'bbridge_test_5',        'test_5_rk',           'Test 5 key',        4,  'BIGINT',    NULL, NULL, FALSE, 13, 'dRetainedKey',            FALSE, TRUE,  FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (64, 5, 'idl', 'bbridge_test_5',        'test_5_cd',           'Test 5 code',       16, 'TEXT',      NULL, NULL, TRUE,  17, 'dCodeIdentificator',      FALSE, FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     
                     , (65, 6, 'idl', 'tbridge_test_6',        'src_cd',              'Source code',       16, 'TEXT',      NULL, NULL, FALSE, 17, 'dCodeIdentificator',      TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (66, 6, 'idl', 'tbridge_test_6',        'hash_diff',           'Hash',              14, 'CHAR',      32,   NULL, FALSE, 8,  'dHashIdentificator',      TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (67, 6, 'idl', 'tbridge_test_6',        'deleted_flg',         'Deleted',           1,  'BOOLEAN',   NULL, NULL, FALSE, 4,  'dDeletedFlag',            TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (68, 6, 'idl', 'tbridge_test_6',        'version_id',          'Version',           4,  'BIGINT',    NULL, NULL, FALSE, 2,  'dVersionIdentificator',   TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (69, 6, 'idl', 'tbridge_test_6',        'effective_dttm',      'Effective',         9,  'TIMESTAMP', NULL, NULL, FALSE, 11, 'dEffectiveDttm',          TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (70, 6, 'idl', 'tbridge_test_6',        'test_6_rk',           'Test 6 key',        4,  'BIGINT',    NULL, NULL, FALSE, 13, 'dRetainedKey',            FALSE, TRUE,  FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (71, 6, 'idl', 'tbridge_test_6',        'test_6_cd',           'Test 6 code',       16, 'TEXT',      NULL, NULL, TRUE,  17, 'dCodeIdentificator',      FALSE, FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, 2,    0,    TRUE)
                     
                     , (72, 7, 'idl', 'blink_test_4_x_test_5', 'src_cd',              'Source code',       16, 'TEXT',      NULL, NULL, FALSE, 17, 'dCodeIdentificator',      TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (73, 7, 'idl', 'blink_test_4_x_test_5', 'hash_diff',           'Hash',              14, 'CHAR',      32,   NULL, FALSE, 8,  'dHashIdentificator',      TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (74, 7, 'idl', 'blink_test_4_x_test_5', 'deleted_flg',         'Deleted',           1,  'BOOLEAN',   NULL, NULL, FALSE, 4,  'dDeletedFlag',            TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (75, 7, 'idl', 'blink_test_4_x_test_5', 'version_id',          'Version',           4,  'BIGINT',    NULL, NULL, FALSE, 2,  'dVersionIdentificator',   TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (76, 7, 'idl', 'blink_test_4_x_test_5', 'effective_from_date', 'Effective from',    8,  'DATE',      NULL, NULL, FALSE, 6,  'dEffectiveBeginDate',     TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (77, 7, 'idl', 'blink_test_4_x_test_5', 'effective_to_date',   'Effective to',      8,  'DATE',      NULL, NULL, FALSE, 7,  'dEffectiveEndDate',       TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     , (78, 7, 'idl', 'blink_test_4_x_test_5', 'test_4_rk',           'Test 4 key',        4,  'BIGINT',    NULL, NULL, FALSE, 13, 'dRetainedKey',            FALSE, TRUE,  TRUE,  'idl', 'bbridge_test_5', 'test_5_rk', 1, 0, NULL, NULL, FALSE)
                     , (79, 7, 'idl', 'blink_test_4_x_test_5', 'test_5_rk',           'Test 5 key',        4,  'BIGINT',    NULL, NULL, FALSE, 13, 'dRetainedKey',            FALSE, FALSE, TRUE,  'idl', 'bbridge_test_4', 'test_4_rk', 1, 0, NULL, NULL, FALSE)
                     , (80, 7, 'idl', 'blink_test_4_x_test_5', 'test_4_5_cd',         'Test 4 5 code',     16, 'TEXT',      NULL, NULL, TRUE,  17, 'dCodeIdentificator',      FALSE, FALSE, FALSE, NULL,  NULL,             NULL,        1, 0, NULL, NULL, FALSE)
                     
                     , (81, 8, 'idl', 'tlink_test_6_x_test_6', 'src_cd',              'Source code',       16, 'TEXT',      NULL, NULL, FALSE, 17, 'dCodeIdentificator',      TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        2, 3, NULL, NULL, FALSE)
                     , (82, 8, 'idl', 'tlink_test_6_x_test_6', 'hash_diff',           'Hash',              14, 'CHAR',      32,   NULL, FALSE, 8,  'dHashIdentificator',      TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        2, 3, NULL, NULL, FALSE)
                     , (83, 8, 'idl', 'tlink_test_6_x_test_6', 'deleted_flg',         'Deleted',           1,  'BOOLEAN',   NULL, NULL, FALSE, 4,  'dDeletedFlag',            TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        2, 3, NULL, NULL, FALSE)
                     , (84, 8, 'idl', 'tlink_test_6_x_test_6', 'version_id',          'Version',           4,  'BIGINT',    NULL, NULL, FALSE, 2,  'dVersionIdentificator',   TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        2, 3, NULL, NULL, FALSE)
                     , (85, 8, 'idl', 'tlink_test_6_x_test_6', 'to_version_id',       'Version to',        4,  'BIGINT',    NULL, NULL, FALSE, 3,  'dToVersionIdentificator', TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        2, 3, NULL, NULL, FALSE)
                     , (86, 8, 'idl', 'tlink_test_6_x_test_6', 'effective_from_dttm', 'Effective from',    8,  'DATE',      NULL, NULL, FALSE, 9,  'dEffectiveBeginDttm',     TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        2, 3, NULL, NULL, FALSE)
                     , (87, 8, 'idl', 'tlink_test_6_x_test_6', 'effective_to_dttm',   'Effective to',      8,  'DATE',      NULL, NULL, FALSE, 10, 'dEffectiveEndDttm',       TRUE,  FALSE, FALSE, NULL,  NULL,             NULL,        2, 3, NULL, NULL, FALSE)
                     , (88, 8, 'idl', 'tlink_test_6_x_test_6', 'test_6_child_rk',     'Test 6 child key',  4,  'BIGINT',    NULL, NULL, FALSE, 13, 'dRetainedKey',            FALSE, TRUE,  TRUE,  'idl', 'tbridge_test_6', 'test_6_rk', 2, 3, NULL, NULL, FALSE)
                     , (89, 8, 'idl', 'tlink_test_6_x_test_6', 'test_6_parent_rk',    'Test 6 parent key', 4,  'BIGINT',    NULL, NULL, FALSE, 13, 'dRetainedKey',            FALSE, FALSE, TRUE,  'idl', 'tbridge_test_6', 'test_6_rk', 2, 3, NULL, NULL, FALSE)
                     , (90, 8, 'idl', 'tlink_test_6_x_test_6', 'test_6_6_cd',         'Test 6 6 code',     16, 'TEXT',      NULL, NULL, TRUE,  17, 'dCodeIdentificator',      FALSE, FALSE, FALSE, NULL,  NULL,             NULL,        2, 4, 3,    1,    FALSE)
               ) a (logical_attribute_rk
                  , logical_table_rk
                  , schema_name
                  , table_name
                  , attribute_name
                  , attribute_desc
                  , data_type_rk
                  , data_type_cd
                  , data_type_size_cnt
                  , data_type_scale_cnt
                  , is_nullable_flg
                  , data_domain_rk
                  , data_domain_cd
                  , is_metadata_flg
                  , is_primary_key_flg
                  , is_foreign_key_flg
                  , foreign_schema_name
                  , foreign_table_name
                  , foreign_attribute_name
                  , model_major_version_from_num
                  , model_minor_version_from_num
                  , model_major_version_to_num
                  , model_minor_version_to_num
                  , is_deprecated_flg)
    """)
    )

    db_session.execute(text("TRUNCATE metamodel.bridge_logical_key"))
    db_session.execute(
        text("""
        INSERT INTO metamodel.bridge_logical_key (effective_from_dttm
                                                , effective_to_dttm
                                                , deleted_flg
                                                , version_rk
                                                , logical_key_rk
                                                , key_name
                                                , schema_name
                                                , logical_table_rk
                                                , table_name
                                                , is_primary_flg
                                                , is_foreign_flg
                                                , model_major_version_from_num
                                                , model_minor_version_from_num
                                                , model_major_version_to_num
                                                , model_minor_version_to_num
                                                , is_deprecated_flg
                                                , model_effective_date)
        SELECT '2025-01-01 +0' effective_from_dttm
             , '2999-12-31 +0' effective_to_dttm
             , FALSE           deleted_flg
             , 1               version_rk
             , logical_key_rk
             , key_name
             , schema_name
             , logical_table_rk
             , table_name
             , is_primary_flg
             , is_foreign_flg
             , model_major_version_from_num
             , model_minor_version_from_num
             , model_major_version_to_num
             , model_minor_version_to_num
             , is_deprecated_flg
             , '2025-01-01 +0' model_effective_date
          FROM (
                VALUES (10, 'dict_test_1_pk',                            'idl', 1, 'dict_test_1',           TRUE,  FALSE, 1, 0, NULL, NULL, FALSE)
                     , (11, 'hub_test_2_pk',                             'idl', 2, 'hub_test_2',            TRUE,  FALSE, 1, 0, NULL, NULL, FALSE)
                     , (12, 'sal_test_3_pk',                             'idl', 3, 'sal_test_3',            TRUE,  FALSE, 1, 0, NULL, NULL, FALSE)
                     , (13, 'bbridge_test_4_pk',                         'idl', 4, 'bbridge_test_4',        TRUE,  FALSE, 1, 0, NULL, NULL, FALSE)
                     , (14, 'bbridge_test_5_pk',                         'idl', 5, 'bbridge_test_5',        TRUE,  FALSE, 1, 0, NULL, NULL, FALSE)
                     , (15, 'tbridge_test_6_pk',                         'idl', 6, 'tbridge_test_6',        TRUE,  FALSE, 1, 0, 2,    0,    TRUE)
                     , (16, 'blink_test_4_x_test_5_pk',                  'idl', 7, 'blink_test_4_x_test_5', TRUE,  FALSE, 1, 0, NULL, NULL, FALSE)
                     , (17, 'tlink_test_6_x_test_6_pk',                  'idl', 8, 'tlink_test_6_x_test_6', TRUE,  FALSE, 2, 3, 3,    1,    FALSE)
                     , (18, 'blink_test_4_x_test_5_test_4_rk_fk',        'idl', 7, 'blink_test_4_x_test_5', FALSE, TRUE,  1, 0, NULL, NULL, FALSE)
                     , (19, 'blink_test_4_x_test_5_test_5_rk_fk',        'idl', 7, 'blink_test_4_x_test_5', FALSE, TRUE,  1, 0, NULL, NULL, FALSE)
                     , (20, 'tlink_test_6_x_test_6_test_6_child_rk_fk',  'idl', 8, 'tlink_test_6_x_test_6', FALSE, TRUE,  2, 3, 3,    1,    FALSE)
                     , (21, 'tlink_test_6_x_test_6_test_6_parent_rk_fk', 'idl', 8, 'tlink_test_6_x_test_6', FALSE, TRUE,  2, 3, 3,    1,    FALSE)
               ) t (logical_key_rk
                  , key_name
                  , schema_name
                  , logical_table_rk
                  , table_name
                  , is_primary_flg
                  , is_foreign_flg
                  , model_major_version_from_num
                  , model_minor_version_from_num
                  , model_major_version_to_num
                  , model_minor_version_to_num
                  , is_deprecated_flg)
    """)
    )

    db_session.execute(text("TRUNCATE metamodel.link_logical_key_attribute"))
    db_session.execute(
        text("""
        INSERT INTO metamodel.link_logical_key_attribute (effective_from_dttm
                                                        , effective_to_dttm
                                                        , deleted_flg
                                                        , version_rk
                                                        , logical_key_rk
                                                        , logical_attribute_rk
                                                        , schema_name
                                                        , key_name
                                                        , table_name
                                                        , attribute_name
                                                        , attribute_no
                                                        , foreign_logical_key_rk
                                                        , foreign_key_name
                                                        , foreign_logical_attribute_rk
                                                        , foreign_schema_name
                                                        , foreign_table_name
                                                        , foreign_attribute_name)
        SELECT '2025-01-01 +0' effective_from_dttm
             , '2999-12-31 +0' effective_to_dttm
             , FALSE           deleted_flg
             , 1               version_rk
             , logical_key_rk
             , logical_attribute_rk
             , schema_name
             , key_name
             , table_name
             , attribute_name
             , attribute_no
             , foreign_logical_key_rk
             , foreign_key_name
             , foreign_logical_attribute_rk
             , foreign_schema_name
             , foreign_table_name
             , foreign_attribute_name
          FROM (
                VALUES (10, 7,  'dict_test_1_pk',                            'idl', 'dict_test_1',           'test_1_cd',        1, NULL, NULL, NULL, NULL, NULL, NULL)
                     , (11, 14, 'hub_test_2_pk',                             'idl', 'hub_test_2',            'test_2_id',        1, NULL, NULL, NULL, NULL, NULL, NULL)
                     , (11, 15, 'hub_test_2_pk',                             'idl', 'hub_test_2',            'bk_schema_cd',     2, NULL, NULL, NULL, NULL, NULL, NULL)
                     , (12, 21, 'sal_test_3_pk',                             'idl', 'sal_test_3',            'secondary_rk',     1, NULL, NULL, NULL, NULL, NULL, NULL)
                     , (12, 22, 'sal_test_3_pk',                             'idl', 'sal_test_3',            'bk_schema_cd',     2, NULL, NULL, NULL, NULL, NULL, NULL)
                     , (13, 29, 'bbridge_test_4_pk',                         'idl', 'bbridge_test_4',        'test_4_rk',        1, NULL, NULL, NULL, NULL, NULL, NULL)
                     , (14, 63, 'bbridge_test_5_pk',                         'idl', 'bbridge_test_5',        'test_5_rk',        1, NULL, NULL, NULL, NULL, NULL, NULL)
                     , (15, 70, 'tbridge_test_6_pk',                         'idl', 'tbridge_test_6',        'test_6_rk',        1, NULL, NULL, NULL, NULL, NULL, NULL)
                     , (16, 78, 'blink_test_4_x_test_5_pk',                  'idl', 'blink_test_4_x_test_5', 'test_4_rk',        1, NULL, NULL, NULL, NULL, NULL, NULL)
                     , (16, 79, 'blink_test_4_x_test_5_pk',                  'idl', 'blink_test_4_x_test_5', 'test_5_rk',        2, NULL, NULL, NULL, NULL, NULL, NULL)
                     , (17, 88, 'tlink_test_6_x_test_6_pk',                  'idl', 'tlink_test_6_x_test_6', 'test_6_child_rk',  1, NULL, NULL, NULL, NULL, NULL, NULL)
                     , (17, 89, 'tlink_test_6_x_test_6_pk',                  'idl', 'tlink_test_6_x_test_6', 'test_6_parent_rk', 2, NULL, NULL, NULL, NULL, NULL, NULL)
                     , (18, 78, 'blink_test_4_x_test_5_test_4_rk_fk',        'idl', 'blink_test_4_x_test_5', 'test_4_rk',        1, 13, 'bbridge_test_4_pk', 29, 'idl', 'bbridge_test_4', 'test_4_rk')
                     , (19, 79, 'blink_test_4_x_test_5_test_5_rk_fk',        'idl', 'blink_test_4_x_test_5', 'test_5_rk',        1, 14, 'bbridge_test_5_pk', 63, 'idl', 'bbridge_test_5', 'test_5_rk')
                     , (20, 88, 'tlink_test_6_x_test_6_test_6_child_rk_fk',  'idl', 'tlink_test_6_x_test_6', 'test_6_child_rk',  1, 15, 'tbridge_test_6_pk', 70, 'idl', 'tbridge_test_6', 'test_6_rk')
                     , (21, 89, 'tlink_test_6_x_test_6_test_6_parent_rk_fk', 'idl', 'tlink_test_6_x_test_6', 'test_6_parent_rk', 1, 15, 'tbridge_test_6_pk', 70, 'idl', 'tbridge_test_6', 'test_6_rk')
               ) t (logical_key_rk
                  , logical_attribute_rk
                  , key_name
                  , schema_name
                  , table_name
                  , attribute_name
                  , attribute_no
                  , foreign_logical_key_rk
                  , foreign_key_name
                  , foreign_logical_attribute_rk
                  , foreign_schema_name
                  , foreign_table_name
                  , foreign_attribute_name)
    """)
    )

    db_session.commit()


@fixture(scope="session")
def version_id() -> int:
    return 0


@fixture(scope="session")
def effective_date() -> datetime:
    return datetime(2025, 1, 1, tzinfo=timezone.utc)


@fixture(scope="session")
def effective_date_incr() -> datetime:
    return datetime(2025, 3, 1, tzinfo=timezone.utc)


@fixture(scope="session")
def schemas() -> Sequence[str]:
    return ["idl", "bdm"]


@fixture(scope="session")
def table_id() -> str:
    return UUID("00000000-0000-0000-0000-000000000000").hex


@fixture(scope="function")
def tables_bridge_table_incr(ceh_cursor):
    ceh_cursor.execute("DROP SCHEMA IF EXISTS idl CASCADE")
    ceh_cursor.execute("CREATE SCHEMA idl")
    ceh_cursor.execute(f"""
        CREATE TABLE idl.full_history_table1 (
            a_boolean           BOOLEAN
          , a_int               INT
          , a_text              TEXT
          , effective_from_date DATE    NOT NULL
          , effective_to_date   DATE    NOT NULL
          , version_id          BIGINT  NOT NULL
          , deleted_flg         BOOLEAN NOT NULL
          , to_version_id       BIGINT           DEFAULT 9223372036854775807
        );

        COMMENT ON TABLE idl.full_history_table1                      IS 'Таблица с полной историчностью (версионирование и временные периоды)';
        COMMENT ON COLUMN idl.full_history_table1.a_boolean           IS 'Пользовательский атрибут: логическое значение';
        COMMENT ON COLUMN idl.full_history_table1.a_int               IS 'Пользовательский атрибут: целое число';
        COMMENT ON COLUMN idl.full_history_table1.a_text              IS 'Пользовательский атрибут: текстовое поле';
        COMMENT ON COLUMN idl.full_history_table1.effective_from_date IS 'Дата начала действия версии';
        COMMENT ON COLUMN idl.full_history_table1.effective_to_date   IS 'Дата окончания действия версии';
        COMMENT ON COLUMN idl.full_history_table1.version_id          IS 'Идентификатор версии';
        COMMENT ON COLUMN idl.full_history_table1.deleted_flg         IS 'Флаг удаления записи';
        COMMENT ON COLUMN idl.full_history_table1.to_version_id       IS 'Ссылка на следующую версию (максимальное значение по умолчанию)';
    """)
    ceh_cursor.execute(f"""
        CREATE TABLE idl.full_history_table2 (
            a_date              DATE
          , a_numeric           NUMERIC
          , a_varchar           VARCHAR(100)
          , effective_from_date DATE    NOT NULL
          , effective_to_date   DATE    NOT NULL
          , version_id          BIGINT  NOT NULL
          , deleted_flg         BOOLEAN NOT NULL
          , to_version_id       BIGINT           DEFAULT 9223372036854775807
        )
        DISTRIBUTED BY (version_id, to_version_id);

        COMMENT ON TABLE idl.full_history_table2                      IS 'Таблица с полной историчностью (второй пример)';
        COMMENT ON COLUMN idl.full_history_table2.a_date              IS 'Пользовательский атрибут: дата';
        COMMENT ON COLUMN idl.full_history_table2.a_numeric           IS 'Пользовательский атрибут: число с точностью';
        COMMENT ON COLUMN idl.full_history_table2.a_varchar           IS 'Пользовательский атрибут: строка переменной длины';
        COMMENT ON COLUMN idl.full_history_table2.effective_from_date IS 'Дата начала действия версии';
        COMMENT ON COLUMN idl.full_history_table2.effective_to_date   IS 'Дата окончания действия версии';
        COMMENT ON COLUMN idl.full_history_table2.version_id          IS 'Идентификатор версии';
        COMMENT ON COLUMN idl.full_history_table2.deleted_flg         IS 'Флаг удаления записи';
        COMMENT ON COLUMN idl.full_history_table2.to_version_id       IS 'Ссылка на следующую версию';
    """)
    ceh_cursor.execute(f"""
        CREATE TABLE idl.simple_history_table1 (
            a_bigint          BIGINT
          , a_float           DOUBLE PRECISION
          , a_json            JSON
          , effective_date    TIMESTAMP NOT NULL
          , version_id        BIGINT    NOT NULL
          , deleted_flg       BOOLEAN   NOT NULL
        )
        DISTRIBUTED BY (a_bigint);
        
        COMMENT ON TABLE idl.simple_history_table1                 IS 'Таблица с простой историчностью (основные версии)';
        COMMENT ON COLUMN idl.simple_history_table1.a_bigint       IS 'Пользовательский атрибут: большое целое число';
        COMMENT ON COLUMN idl.simple_history_table1.a_float        IS 'Пользовательский атрибут: число с плавающей точкой';
        COMMENT ON COLUMN idl.simple_history_table1.a_json         IS 'Пользовательский атрибут: JSON-данные';
        COMMENT ON COLUMN idl.simple_history_table1.effective_date IS 'Дата и время актуальности версии';
        COMMENT ON COLUMN idl.simple_history_table1.version_id     IS 'Идентификатор версии';
        COMMENT ON COLUMN idl.simple_history_table1.deleted_flg    IS 'Флаг удаления записи';
    """)
    ceh_cursor.execute(f"""
        CREATE TABLE idl.simple_history_table2 (
            a_time            TIME
          , a_boolean         BOOLEAN
          , a_double          DOUBLE PRECISION
          , effective_date    TIMESTAMP NOT NULL
          , version_id        BIGINT    NOT NULL
          , deleted_flg       BOOLEAN   NOT NULL
        )
        DISTRIBUTED REPLICATED;
        
        COMMENT ON TABLE idl.simple_history_table2                 IS 'Таблица с простой историчностью (дополнительные атрибуты)';
        COMMENT ON COLUMN idl.simple_history_table2.a_time         IS 'Пользовательский атрибут: время';
        COMMENT ON COLUMN idl.simple_history_table2.a_boolean      IS 'Пользовательский атрибут: логическое значение';
        COMMENT ON COLUMN idl.simple_history_table2.a_double       IS 'Пользовательский атрибут: число с двойной точностью';
        COMMENT ON COLUMN idl.simple_history_table2.effective_date IS 'Дата и время актуальности версии';
        COMMENT ON COLUMN idl.simple_history_table2.version_id     IS 'Идентификатор версии';
        COMMENT ON COLUMN idl.simple_history_table2.deleted_flg    IS 'Флаг удаления записи';
    """)
    ceh_cursor.connection.commit()


@fixture(scope="function")
def tables_bridge_attr_incr(ceh_cursor):
    ceh_cursor.execute("DROP SCHEMA IF EXISTS idl CASCADE")
    ceh_cursor.execute("CREATE SCHEMA idl")
    ceh_cursor.execute(f"""
        CREATE TABLE idl.attr_table1 (
            a_bigint          BIGINT,
            a_float           DOUBLE PRECISION,
            a_json            JSON,
            effective_date    TIMESTAMP NOT NULL,
            version_id        BIGINT    NOT NULL,
            deleted_flg       BOOLEAN   NOT NULL
        );

        COMMENT ON TABLE idl.attr_table1                 IS 'Таблица с простой историчностью (основные версии)';
        COMMENT ON COLUMN idl.attr_table1.a_bigint       IS 'Пользовательский атрибут: большое целое число';
        COMMENT ON COLUMN idl.attr_table1.a_float        IS 'Пользовательский атрибут: число с плавающей точкой';
        COMMENT ON COLUMN idl.attr_table1.a_json         IS 'Пользовательский атрибут: JSON-данные';
        COMMENT ON COLUMN idl.attr_table1.effective_date IS 'Дата и время актуальности версии';
        COMMENT ON COLUMN idl.attr_table1.version_id     IS 'Идентификатор версии';
        COMMENT ON COLUMN idl.attr_table1.deleted_flg    IS 'Флаг удаления записи';
    """)
    ceh_cursor.connection.commit()


@fixture(scope="session")
def page_size() -> int:
    return 3


@fixture(scope="function")
def truncate_all_model_tables(db_session):
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_physical_table"))
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_physical_attribute"))
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_physical_key"))
    db_session.execute(text("TRUNCATE TABLE metamodel.link_physical_key_attribute"))
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_logical_domain"))
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_logical_attribute"))
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_logical_table"))
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_logical_key"))
    db_session.execute(text("TRUNCATE TABLE metamodel.link_logical_key_attribute"))
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_table"))
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_attribute"))
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_key"))
    db_session.execute(text("TRUNCATE TABLE metamodel.link_key_attribute"))
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_source"))
    db_session.commit()


@fixture(scope="function")
def source(db_session):
    db_session.execute(
        text("""
            INSERT INTO metamodel.bridge_source(source_rk
                                              , source_cd
                                              , ris_src_id
                                              , ris_src_code
                                              , effective_from_dttm
                                              , effective_to_dttm
                                              , deleted_flg)
                 VALUES (1, 'DTPL', '1480', 'DTPL', '1900-01-01 +0', '2999-12-31 +0', FALSE)
        """)
    )
    db_session.commit()


def edit_incr_tables(ceh_cursor):
    ceh_cursor.execute(f"""
            COMMENT ON TABLE idl.simple_history_table1 IS 'upd. Table1 with simple history';
            
            ALTER TABLE idl.simple_history_table1 
                DROP COLUMN effective_date;
            
            ALTER TABLE idl.full_history_table1 
                DROP COLUMN to_version_id;
    """)

    ceh_cursor.execute(f"""
            DROP TABLE idl.simple_history_table2;
            DROP TABLE idl.full_history_table2;
    """)

    ceh_cursor.execute(f"""
        CREATE TABLE idl.simple_history_table2 (
            a_time            TIME
          , a_boolean         BOOLEAN
          , a_double          DOUBLE PRECISION
          , effective_date    TIMESTAMP NOT NULL
          , version_id        BIGINT    NOT NULL
          , deleted_flg       BOOLEAN   NOT NULL
        );
        
        COMMENT ON TABLE idl.simple_history_table2                 IS 'upd. Table2 with simple history';
        COMMENT ON COLUMN idl.simple_history_table2.a_time         IS 'Пользовательский атрибут: время';
        COMMENT ON COLUMN idl.simple_history_table2.a_boolean      IS 'Пользовательский атрибут: логическое значение';
        COMMENT ON COLUMN idl.simple_history_table2.a_double       IS 'Пользовательский атрибут: число с двойной точностью';
        COMMENT ON COLUMN idl.simple_history_table2.effective_date IS 'Дата и время актуальности версии';
        COMMENT ON COLUMN idl.simple_history_table2.version_id     IS 'Идентификатор версии';
        COMMENT ON COLUMN idl.simple_history_table2.deleted_flg    IS 'Флаг удаления записи';
        
        CREATE TABLE idl.full_history_table2 (
            a_date              DATE
          , a_numeric           NUMERIC
          , a_varchar           VARCHAR(100)
          , effective_from_date DATE    NOT NULL
          , effective_to_date   DATE    NOT NULL
          , version_id          BIGINT  NOT NULL
          , deleted_flg         BOOLEAN NOT NULL
          , to_version_id       BIGINT           DEFAULT 9223372036854775807
        )
        ;

        COMMENT ON TABLE idl.full_history_table2                      IS 'Таблица с полной историчностью (второй пример)';
        COMMENT ON COLUMN idl.full_history_table2.a_date              IS 'Пользовательский атрибут: дата';
        COMMENT ON COLUMN idl.full_history_table2.a_numeric           IS 'Пользовательский атрибут: число с точностью';
        COMMENT ON COLUMN idl.full_history_table2.a_varchar           IS 'Пользовательский атрибут: строка переменной длины';
        COMMENT ON COLUMN idl.full_history_table2.effective_from_date IS 'Дата начала действия версии';
        COMMENT ON COLUMN idl.full_history_table2.effective_to_date   IS 'Дата окончания действия версии';
        COMMENT ON COLUMN idl.full_history_table2.version_id          IS 'Идентификатор версии';
        COMMENT ON COLUMN idl.full_history_table2.deleted_flg         IS 'Флаг удаления записи';
        COMMENT ON COLUMN idl.full_history_table2.to_version_id       IS 'Ссылка на следующую версию';
    """)
    ceh_cursor.connection.commit()


def edit_incr_attr(ceh_cursor):
    ceh_cursor.execute(f"""
        ALTER TABLE idl.attr_table1
            DROP COLUMN a_bigint,
            DROP COLUMN version_id;
    
        COMMENT ON COLUMN idl.attr_table1.a_float IS 'upd. Is float';
        
        ALTER TABLE idl.attr_table1
            ALTER COLUMN a_json TYPE TEXT;
    """)

    ceh_cursor.execute(f"""
        ALTER TABLE idl.attr_table1
            ADD COLUMN a_bigint     BIGINT,
            ADD COLUMN version_id   BIGINT NULL,
            ADD COLUMN added_column INT DEFAULT 0;
        
        COMMENT ON COLUMN idl.attr_table1.a_bigint   IS 'Пользовательский атрибут: большое целое число';
        COMMENT ON COLUMN idl.attr_table1.version_id IS 'Идентификатор версии';
    """)
    ceh_cursor.connection.commit()


def edit_incr_key(ceh_cursor):
    ceh_cursor.execute(f"""
            DROP TABLE idl.simple_history_table1;
            DROP TABLE idl.simple_history_table2;
            DROP TABLE idl.full_history_table2;
    """)

    ceh_cursor.execute(f"""
        CREATE TABLE idl.simple_history_table1 (
            a_bigint          BIGINT
          , a_float           DOUBLE PRECISION
          , a_json            JSON
          , effective_date    TIMESTAMP NOT NULL
          , version_id        BIGINT    NOT NULL
          , deleted_flg       BOOLEAN   NOT NULL
        )
        DISTRIBUTED BY (a_bigint, version_id);
        
        CREATE TABLE idl.simple_history_table2 (
            a_time            TIME
          , a_boolean         BOOLEAN
          , a_double          DOUBLE PRECISION
          , effective_date    TIMESTAMP NOT NULL
          , version_id        BIGINT    NOT NULL
          , deleted_flg       BOOLEAN   NOT NULL
        ) DISTRIBUTED BY (version_id);

        CREATE TABLE idl.full_history_table2 (
            a_date              DATE
          , a_numeric           NUMERIC
          , a_varchar           VARCHAR(100)
          , effective_from_date DATE    NOT NULL
          , effective_to_date   DATE    NOT NULL
          , version_id          BIGINT  NOT NULL
          , deleted_flg         BOOLEAN NOT NULL
          , to_version_id       BIGINT           DEFAULT 9223372036854775807
        ) DISTRIBUTED REPLICATED;

    """)
    ceh_cursor.connection.commit()
