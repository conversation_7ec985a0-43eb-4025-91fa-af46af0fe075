from datetime import date, datetime, timezone
from typing import Any, Mapping, Sequence
from uuid import UUID

from metaloader_rest_api.data_model.logical_model import load_logical_model
from pytest import fixture
from sqlalchemy import text


def test_load_logical_model(
    db_session,
    version_id,
    effective_date,
    layer_id,
    schemas,
    data,
    as_of,
    table_id,
    page_size,
    truncate_tables,
    source,
):
    load_logical_model(
        db_session,
        version_id,
        effective_date,
        layer_id,
        schemas,
        iter(data),
        as_of,
        table_id,
        page_size,
    )
    db_session.commit()

    rows = db_session.execute(
        text("SELECT * FROM metamodel.bridge_logical_domain")
    ).fetchall()
    assert len(rows) == 2

    rows = db_session.execute(
        text("SELECT * FROM metamodel.bridge_logical_table")
    ).fetchall()
    assert len(rows) == 3

    rows = db_session.execute(
        text("SELECT * FROM metamodel.bridge_logical_attribute")
    ).fetchall()
    assert len(rows) == 22

    load_logical_model(
        db_session,
        version_id,
        effective_date,
        layer_id,
        schemas,
        iter(data),
        as_of,
        table_id,
        page_size,
    )
    db_session.commit()

    rows = db_session.execute(
        text("SELECT * FROM metamodel.bridge_logical_domain")
    ).fetchall()
    assert len(rows) == 2

    rows = db_session.execute(
        text("SELECT * FROM metamodel.bridge_logical_table")
    ).fetchall()
    assert len(rows) == 3

    rows = db_session.execute(
        text("SELECT * FROM metamodel.bridge_logical_attribute")
    ).fetchall()
    assert len(rows) == 22


@fixture(scope="session")
def data() -> Sequence[Mapping[str, Any]]:
    return [
        {
            "record_no": "1",
            "domain_name": "Test Domain 1",
            "domain_model_version_num": "1.2",
            "schema_name": "idl",
            "table_name": "bbridge_test_1",
            "table_extra_name": "test 1",
            "table_data_vault_type_cd": "IDL BBridge",
            "table_desc": "Test 1",
            "table_extra_desc": "Test 1 Extra",
            "attribute_cnt": "8",
            "is_ref_flg": False,
            "sub_domain_name": None,
            "extra_sub_domain_name": None,
            "table_axon_id": "idl_bbridge_test_1",
            "table_axon_url": "https://axon.test/idl/bbridge_test_1",
            "table_model_version_from_num": "1.2",
            "table_model_version_to_num": None,
            "attribute_name": "test_1_rk",
            "attribute_extra_name": "test 1 key",
            "data_type_cd": "BIGINT",
            "data_domain_cd": "dRetainedKey",
            "is_nullable_flg": False,
            "is_metadata_flg": False,
            "is_primary_key_flg": True,
            "is_foreign_key_flg": False,
            "foreign_schema_name": None,
            "foreign_table_name": None,
            "foreign_attribute_name": None,
            "attribute_desc": "Test 1 Key",
            "attribute_extra_desc": "Test 1 Key Extra",
            "attribute_axon_id": "idl_bbridge_test_1_test_1_rk",
            "attribute_axon_url": "https://axon.test/idl/bbridge_test_1/test_1_rk",
            "attribute_model_version_from_num": "1.2",
            "attribute_model_version_to_num": None,
        },
        {
            "record_no": "2",
            "domain_name": "Test Domain 1",
            "domain_model_version_num": "1.2",
            "schema_name": "idl",
            "table_name": "bbridge_test_1",
            "table_extra_name": "test 1",
            "table_data_vault_type_cd": "IDL BBridge",
            "table_desc": "Test 1",
            "table_extra_desc": "Test 1 Extra",
            "attribute_cnt": "8",
            "is_ref_flg": False,
            "sub_domain_name": None,
            "extra_sub_domain_name": None,
            "table_axon_id": "idl_bbridge_test_1",
            "table_axon_url": "https://axon.test/idl/bbridge_test_1",
            "table_model_version_from_num": "1.2",
            "table_model_version_to_num": None,
            "attribute_name": "test_1_cd",
            "attribute_extra_name": "test 1 code",
            "data_type_cd": "TEXT",
            "data_domain_cd": "dCodeIdentificator",
            "is_nullable_flg": True,
            "is_metadata_flg": False,
            "is_primary_key_flg": False,
            "is_foreign_key_flg": False,
            "foreign_schema_name": None,
            "foreign_table_name": None,
            "foreign_attribute_name": None,
            "attribute_desc": "Test 1 Code",
            "attribute_extra_desc": "Test 1 Code Extra",
            "attribute_axon_id": "idl_bbridge_test_1_test_1_cd",
            "attribute_axon_url": "https://axon.test/idl/bbridge_test_1/test_1_cd",
            "attribute_model_version_from_num": "1.2",
            "attribute_model_version_to_num": None,
        },
        {
            "record_no": "3",
            "domain_name": "Test Domain 1",
            "domain_model_version_num": "1.2",
            "schema_name": "idl",
            "table_name": "bbridge_test_1",
            "table_extra_name": "test 1",
            "table_data_vault_type_cd": "IDL BBridge",
            "table_desc": "Test 1",
            "table_extra_desc": "Test 1 Extra",
            "attribute_cnt": "8",
            "is_ref_flg": False,
            "sub_domain_name": None,
            "extra_sub_domain_name": None,
            "table_axon_id": "idl_bbridge_test_1",
            "table_axon_url": "https://axon.test/idl/bbridge_test_1",
            "table_model_version_from_num": "1.2",
            "table_model_version_to_num": None,
            "attribute_name": "test_1_desc",
            "attribute_extra_name": "test 1 description",
            "data_type_cd": "TEXT",
            "data_domain_cd": "dDescription",
            "is_nullable_flg": True,
            "is_metadata_flg": False,
            "is_primary_key_flg": False,
            "is_foreign_key_flg": False,
            "foreign_schema_name": None,
            "foreign_table_name": None,
            "foreign_attribute_name": None,
            "attribute_desc": "Test 1 Description",
            "attribute_extra_desc": "Test 1 Description Extra",
            "attribute_axon_id": "idl_bbridge_test_1_test_1_desc",
            "attribute_axon_url": "https://axon.test/idl/bbridge_test_1/test_1_desc",
            "attribute_model_version_from_num": "1.2",
            "attribute_model_version_to_num": "3.0",
        },
        {
            "record_no": "4",
            "domain_name": "Test Domain 1",
            "domain_model_version_num": "1.2",
            "schema_name": "idl",
            "table_name": "bbridge_test_1",
            "table_extra_name": "test 1",
            "table_data_vault_type_cd": "IDL BBridge",
            "table_desc": "Test 1",
            "table_extra_desc": "Test 1 Extra",
            "attribute_cnt": "8",
            "is_ref_flg": False,
            "sub_domain_name": None,
            "extra_sub_domain_name": None,
            "table_axon_id": "idl_bbridge_test_1",
            "table_axon_url": "https://axon.test/idl/bbridge_test_1",
            "table_model_version_from_num": "1.2",
            "table_model_version_to_num": None,
            "attribute_name": "effective_date",
            "attribute_extra_name": "effective date",
            "data_type_cd": "DATE",
            "data_domain_cd": "dEffectiveDate",
            "is_nullable_flg": False,
            "is_metadata_flg": True,
            "is_primary_key_flg": False,
            "is_foreign_key_flg": False,
            "foreign_schema_name": None,
            "foreign_table_name": None,
            "foreign_attribute_name": None,
            "attribute_desc": "Effective Date",
            "attribute_extra_desc": None,
            "attribute_axon_id": "idl_bbridge_test_1_effective_date",
            "attribute_axon_url": "https://axon.test/idl/bbridge_test_1/effective_date",
            "attribute_model_version_from_num": "1.2",
            "attribute_model_version_to_num": None,
        },
        {
            "record_no": "5",
            "domain_name": "Test Domain 1",
            "domain_model_version_num": "1.2",
            "schema_name": "idl",
            "table_name": "bbridge_test_1",
            "table_extra_name": "test 1",
            "table_data_vault_type_cd": "IDL BBridge",
            "table_desc": "Test 1",
            "table_extra_desc": "Test 1 Extra",
            "attribute_cnt": "8",
            "is_ref_flg": False,
            "sub_domain_name": None,
            "extra_sub_domain_name": None,
            "table_axon_id": "idl_bbridge_test_1",
            "table_axon_url": "https://axon.test/idl/bbridge_test_1",
            "table_model_version_from_num": "1.2",
            "table_model_version_to_num": None,
            "attribute_name": "deleted_flg",
            "attribute_extra_name": "deleted flg",
            "data_type_cd": "BOOL",
            "data_domain_cd": "dDeletedFlag",
            "is_nullable_flg": False,
            "is_metadata_flg": True,
            "is_primary_key_flg": False,
            "is_foreign_key_flg": False,
            "foreign_schema_name": None,
            "foreign_table_name": None,
            "foreign_attribute_name": None,
            "attribute_desc": "Deleted Flag",
            "attribute_extra_desc": None,
            "attribute_axon_id": "idl_bbridge_test_1_deleted_flg",
            "attribute_axon_url": "https://axon.test/idl/bbridge_test_1/deleted_flg",
            "attribute_model_version_from_num": "1.2",
            "attribute_model_version_to_num": None,
        },
        {
            "record_no": "6",
            "domain_name": "Test Domain 1",
            "domain_model_version_num": "1.2",
            "schema_name": "idl",
            "table_name": "bbridge_test_1",
            "table_extra_name": "test 1",
            "table_data_vault_type_cd": "IDL BBridge",
            "table_desc": "Test 1",
            "table_extra_desc": "Test 1 Extra",
            "attribute_cnt": "8",
            "is_ref_flg": False,
            "sub_domain_name": None,
            "extra_sub_domain_name": None,
            "table_axon_id": "idl_bbridge_test_1",
            "table_axon_url": "https://axon.test/idl/bbridge_test_1",
            "table_model_version_from_num": "1.2",
            "table_model_version_to_num": None,
            "attribute_name": "version_id",
            "attribute_extra_name": "version id",
            "data_type_cd": "BIGINT",
            "data_domain_cd": "dVersionIdentificator",
            "is_nullable_flg": False,
            "is_metadata_flg": True,
            "is_primary_key_flg": False,
            "is_foreign_key_flg": False,
            "foreign_schema_name": None,
            "foreign_table_name": None,
            "foreign_attribute_name": None,
            "attribute_desc": "Version ID",
            "attribute_extra_desc": None,
            "attribute_axon_id": "idl_bbridge_test_1_version_id",
            "attribute_axon_url": "https://axon.test/idl/bbridge_test_1/version_id",
            "attribute_model_version_from_num": "1.2",
            "attribute_model_version_to_num": None,
        },
        {
            "record_no": "7",
            "domain_name": "Test Domain 1",
            "domain_model_version_num": "1.2",
            "schema_name": "idl",
            "table_name": "bbridge_test_1",
            "table_extra_name": "test 1",
            "table_data_vault_type_cd": "IDL BBridge",
            "table_desc": "Test 1",
            "table_extra_desc": "Test 1 Extra",
            "attribute_cnt": "8",
            "is_ref_flg": False,
            "sub_domain_name": None,
            "extra_sub_domain_name": None,
            "table_axon_id": "idl_bbridge_test_1",
            "table_axon_url": "https://axon.test/idl/bbridge_test_1",
            "table_model_version_from_num": "1.2",
            "table_model_version_to_num": None,
            "attribute_name": "hash_diff",
            "attribute_extra_name": "hash diff",
            "data_type_cd": "CHAR(32)",
            "data_domain_cd": "dHashIdentificator",
            "is_nullable_flg": False,
            "is_metadata_flg": True,
            "is_primary_key_flg": False,
            "is_foreign_key_flg": False,
            "foreign_schema_name": None,
            "foreign_table_name": None,
            "foreign_attribute_name": None,
            "attribute_desc": "Hash Diff",
            "attribute_extra_desc": None,
            "attribute_axon_id": "idl_bbridge_test_1_hash_diff",
            "attribute_axon_url": "https://axon.test/idl/bbridge_test_1/hash_diff",
            "attribute_model_version_from_num": "1.2",
            "attribute_model_version_to_num": None,
        },
        {
            "record_no": "8",
            "domain_name": "Test Domain 1",
            "domain_model_version_num": "1.2",
            "schema_name": "idl",
            "table_name": "bbridge_test_1",
            "table_extra_name": "test 1",
            "table_data_vault_type_cd": "IDL BBridge",
            "table_desc": "Test 1",
            "table_extra_desc": "Test 1 Extra",
            "attribute_cnt": "8",
            "is_ref_flg": False,
            "sub_domain_name": None,
            "extra_sub_domain_name": None,
            "table_axon_id": "idl_bbridge_test_1",
            "table_axon_url": "https://axon.test/idl/bbridge_test_1",
            "table_model_version_from_num": "1.2",
            "table_model_version_to_num": None,
            "attribute_name": "src_cd",
            "attribute_extra_name": "src cd",
            "data_type_cd": "TEXT",
            "data_domain_cd": "dCodeIdentificator",
            "is_nullable_flg": False,
            "is_metadata_flg": True,
            "is_primary_key_flg": False,
            "is_foreign_key_flg": False,
            "foreign_schema_name": None,
            "foreign_table_name": None,
            "foreign_attribute_name": None,
            "attribute_desc": "Source Code",
            "attribute_extra_desc": None,
            "attribute_axon_id": "idl_bbridge_test_1_src_cd",
            "attribute_axon_url": "https://axon.test/idl/bbridge_test_1/src_cd",
            "attribute_model_version_from_num": "1.2",
            "attribute_model_version_to_num": None,
        },
        {
            "record_no": "9",
            "domain_name": "Test Domain 1",
            "domain_model_version_num": "1.2",
            "schema_name": "idl",
            "table_name": "blink_test_1_x_test_1",
            "table_extra_name": "test 1 to test 1",
            "table_data_vault_type_cd": "IDL BLink",
            "table_desc": "Test 1 to Test 1",
            "table_extra_desc": "Test 1 to Test 1 Extra",
            "attribute_cnt": "7",
            "is_ref_flg": False,
            "sub_domain_name": None,
            "extra_sub_domain_name": None,
            "table_axon_id": "blink_test_1_x_test_1",
            "table_axon_url": "https://axon.test/idl/blink_test_1_x_test_1",
            "table_model_version_from_num": "1.2",
            "table_model_version_to_num": None,
            "attribute_name": "test_1_parent_rk",
            "attribute_extra_name": "test 1 parent key",
            "data_type_cd": "BIGINT",
            "data_domain_cd": "dRetainedKey",
            "is_nullable_flg": False,
            "is_metadata_flg": False,
            "is_primary_key_flg": True,
            "is_foreign_key_flg": True,
            "foreign_schema_name": "idl",
            "foreign_table_name": "bbridge_test_1",
            "foreign_attribute_name": "test_1_rk",
            "attribute_desc": "Test 1 Parent Key",
            "attribute_extra_desc": "Test 1 Parent Key Extra",
            "attribute_axon_id": "blink_test_1_x_test_1_test_1_parent_rk",
            "attribute_axon_url": "https://axon.test/idl/blink_test_1_x_test_1/test_1_parent_rk",
            "attribute_model_version_from_num": "1.2",
            "attribute_model_version_to_num": None,
        },
        {
            "record_no": "10",
            "domain_name": "Test Domain 1",
            "domain_model_version_num": "1.2",
            "schema_name": "idl",
            "table_name": "blink_test_1_x_test_1",
            "table_extra_name": "test 1 to test 1",
            "table_data_vault_type_cd": "IDL BLink",
            "table_desc": "Test 1 to Test 1",
            "table_extra_desc": "Test 1 to Test 1 Extra",
            "attribute_cnt": "7",
            "is_ref_flg": False,
            "sub_domain_name": None,
            "extra_sub_domain_name": None,
            "table_axon_id": "blink_test_1_x_test_1",
            "table_axon_url": "https://axon.test/idl/blink_test_1_x_test_1",
            "table_model_version_from_num": "1.2",
            "table_model_version_to_num": None,
            "attribute_name": "test_1_child_rk",
            "attribute_extra_name": "test 1 child key",
            "data_type_cd": "BIGINT",
            "data_domain_cd": "dRetainedKey",
            "is_nullable_flg": False,
            "is_metadata_flg": False,
            "is_primary_key_flg": True,
            "is_foreign_key_flg": True,
            "foreign_schema_name": "idl",
            "foreign_table_name": "bbridge_test_1",
            "foreign_attribute_name": "test_1_rk",
            "attribute_desc": "Test 2 Parent Key",
            "attribute_extra_desc": "Test 2 Parent Key Extra",
            "attribute_axon_id": "blink_test_1_x_test_1_test_1_child_rk",
            "attribute_axon_url": "https://axon.test/idl/blink_test_1_x_test_1/test_1_child_rk",
            "attribute_model_version_from_num": "1.2",
            "attribute_model_version_to_num": None,
        },
        {
            "record_no": "11",
            "domain_name": "Test Domain 1",
            "domain_model_version_num": "1.2",
            "schema_name": "idl",
            "table_name": "blink_test_1_x_test_1",
            "table_extra_name": "test 1 to test 1",
            "table_data_vault_type_cd": "IDL BLink",
            "table_desc": "Test 1 to Test 1",
            "table_extra_desc": "Test 1 to Test 1 Extra",
            "attribute_cnt": "7",
            "is_ref_flg": False,
            "sub_domain_name": None,
            "extra_sub_domain_name": None,
            "table_axon_id": "idl_blink_test_1_x_test_1",
            "table_axon_url": "https://axon.test/idl/blink_test_1_x_test_1",
            "table_model_version_from_num": "1.2",
            "table_model_version_to_num": None,
            "attribute_name": "effective_date",
            "attribute_extra_name": "effective date",
            "data_type_cd": "DATE",
            "data_domain_cd": "dEffectiveDate",
            "is_nullable_flg": False,
            "is_metadata_flg": True,
            "is_primary_key_flg": False,
            "is_foreign_key_flg": False,
            "foreign_schema_name": None,
            "foreign_table_name": None,
            "foreign_attribute_name": None,
            "attribute_desc": "Effective Date",
            "attribute_extra_desc": None,
            "attribute_axon_id": "idl_blink_test_1_x_test_1_effective_date",
            "attribute_axon_url": "https://axon.test/idl/blink_test_1_x_test_1/effective_date",
            "attribute_model_version_from_num": "1.2",
            "attribute_model_version_to_num": None,
        },
        {
            "record_no": "12",
            "domain_name": "Test Domain 1",
            "domain_model_version_num": "1.2",
            "schema_name": "idl",
            "table_name": "blink_test_1_x_test_1",
            "table_extra_name": "test 1 to test 1",
            "table_data_vault_type_cd": "IDL BLink",
            "table_desc": "Test 1 to Test 1",
            "table_extra_desc": "Test 1 to Test 1 Extra",
            "attribute_cnt": "7",
            "is_ref_flg": False,
            "sub_domain_name": None,
            "extra_sub_domain_name": None,
            "table_axon_id": "blink_test_1_x_test_1",
            "table_axon_url": "https://axon.test/idl/blink_test_1_x_test_1",
            "table_model_version_from_num": "1.2",
            "table_model_version_to_num": None,
            "attribute_name": "deleted_flg",
            "attribute_extra_name": "deleted flg",
            "data_type_cd": "BOOL",
            "data_domain_cd": "dDeletedFlag",
            "is_nullable_flg": False,
            "is_metadata_flg": True,
            "is_primary_key_flg": False,
            "is_foreign_key_flg": False,
            "foreign_schema_name": None,
            "foreign_table_name": None,
            "foreign_attribute_name": None,
            "attribute_desc": "Deleted Flag",
            "attribute_extra_desc": None,
            "attribute_axon_id": "idl_blink_test_1_x_test_1_deleted_flg",
            "attribute_axon_url": "https://axon.test/idl/blink_test_1_x_test_1/deleted_flg",
            "attribute_model_version_from_num": "1.2",
            "attribute_model_version_to_num": None,
        },
        {
            "record_no": "13",
            "domain_name": "Test Domain 1",
            "domain_model_version_num": "1.2",
            "schema_name": "idl",
            "table_name": "blink_test_1_x_test_1",
            "table_extra_name": "test 1 to test 1",
            "table_data_vault_type_cd": "IDL BLink",
            "table_desc": "Test 1 to Test 1",
            "table_extra_desc": "Test 1 to Test 1 Extra",
            "attribute_cnt": "7",
            "is_ref_flg": False,
            "sub_domain_name": None,
            "extra_sub_domain_name": None,
            "table_axon_id": "blink_test_1_x_test_1",
            "table_axon_url": "https://axon.test/idl/blink_test_1_x_test_1",
            "table_model_version_from_num": "1.2",
            "table_model_version_to_num": None,
            "attribute_name": "version_id",
            "attribute_extra_name": "version id",
            "data_type_cd": "BIGINT",
            "data_domain_cd": "dVersionIdentificator",
            "is_nullable_flg": False,
            "is_metadata_flg": True,
            "is_primary_key_flg": False,
            "is_foreign_key_flg": False,
            "foreign_schema_name": None,
            "foreign_table_name": None,
            "foreign_attribute_name": None,
            "attribute_desc": "Version ID",
            "attribute_extra_desc": None,
            "attribute_axon_id": "idl_blink_test_1_x_test_1_version_id",
            "attribute_axon_url": "https://axon.test/idl/blink_test_1_x_test_1/version_id",
            "attribute_model_version_from_num": "1.2",
            "attribute_model_version_to_num": None,
        },
        {
            "record_no": "14",
            "domain_name": "Test Domain 1",
            "domain_model_version_num": "1.2",
            "schema_name": "idl",
            "table_name": "blink_test_1_x_test_1",
            "table_extra_name": "test 1 to test 1",
            "table_data_vault_type_cd": "IDL BLink",
            "table_desc": "Test 1 to Test 1",
            "table_extra_desc": "Test 1 to Test 1 Extra",
            "attribute_cnt": "7",
            "is_ref_flg": False,
            "sub_domain_name": None,
            "extra_sub_domain_name": None,
            "table_axon_id": "blink_test_1_x_test_1",
            "table_axon_url": "https://axon.test/idl/blink_test_1_x_test_1",
            "table_model_version_from_num": "1.2",
            "table_model_version_to_num": None,
            "attribute_name": "hash_diff",
            "attribute_extra_name": "hash diff",
            "data_type_cd": "CHAR(32)",
            "data_domain_cd": "dHashIdentificator",
            "is_nullable_flg": False,
            "is_metadata_flg": True,
            "is_primary_key_flg": False,
            "is_foreign_key_flg": False,
            "foreign_schema_name": None,
            "foreign_table_name": None,
            "foreign_attribute_name": None,
            "attribute_desc": "Hash Diff",
            "attribute_extra_desc": None,
            "attribute_axon_id": "idl_blink_test_1_x_test_1_hash_diff",
            "attribute_axon_url": "https://axon.test/idl/blink_test_1_x_test_1/hash_diff",
            "attribute_model_version_from_num": "1.2",
            "attribute_model_version_to_num": None,
        },
        {
            "record_no": "15",
            "domain_name": "Test Domain 1",
            "domain_model_version_num": "1.2",
            "schema_name": "idl",
            "table_name": "blink_test_1_x_test_1",
            "table_extra_name": "test 1 to test 1",
            "table_data_vault_type_cd": "IDL BLink",
            "table_desc": "Test 1 to Test 1",
            "table_extra_desc": "Test 1 to Test 1 Extra",
            "attribute_cnt": "7",
            "is_ref_flg": False,
            "sub_domain_name": None,
            "extra_sub_domain_name": None,
            "table_axon_id": "blink_test_1_x_test_1",
            "table_axon_url": "https://axon.test/idl/blink_test_1_x_test_1",
            "table_model_version_from_num": "1.2",
            "table_model_version_to_num": None,
            "attribute_name": "src_cd",
            "attribute_extra_name": "src cd",
            "data_type_cd": "TEXT",
            "data_domain_cd": "dCodeIdentificator",
            "is_nullable_flg": False,
            "is_metadata_flg": True,
            "is_primary_key_flg": False,
            "is_foreign_key_flg": False,
            "foreign_schema_name": None,
            "foreign_table_name": None,
            "foreign_attribute_name": None,
            "attribute_desc": "Source Code",
            "attribute_extra_desc": None,
            "attribute_axon_id": "idl_blink_test_1_x_test_1_src_cd",
            "attribute_axon_url": "https://axon.test/idl/blink_test_1_x_test_1/src_cd",
            "attribute_model_version_from_num": "1.2",
            "attribute_model_version_to_num": None,
        },
        {
            "record_no": "16",
            "domain_name": "Test Domain 2",
            "domain_model_version_num": "1.2",
            "schema_name": "idl",
            "table_name": "dict_test_2",
            "table_extra_name": "test 2",
            "table_data_vault_type_cd": "IDL Dictionary",
            "table_desc": "Test 2",
            "table_extra_desc": "Test 2 Extra",
            "attribute_cnt": "6",
            "is_ref_flg": False,
            "sub_domain_name": None,
            "extra_sub_domain_name": None,
            "table_axon_id": "idl_dict_test_2",
            "table_axon_url": "https://axon.test/idl/dict_test_2",
            "table_model_version_from_num": "1.2",
            "table_model_version_to_num": None,
            "attribute_name": "test_2_rk",
            "attribute_extra_name": "test 2 key",
            "data_type_cd": "SMALLINT",
            "data_domain_cd": "dSmallRetainedKey",
            "is_nullable_flg": False,
            "is_metadata_flg": False,
            "is_primary_key_flg": True,
            "is_foreign_key_flg": False,
            "foreign_schema_name": "idl",
            "foreign_table_name": None,
            "foreign_attribute_name": None,
            "attribute_desc": "Test 2 Key",
            "attribute_extra_desc": "Test 2 Key Extra",
            "attribute_axon_id": "dict_rk",
            "attribute_axon_url": "https://axon.test/idl/dict_test_2/rk",
            "attribute_model_version_from_num": "1.2",
            "attribute_model_version_to_num": None,
        },
        {
            "record_no": "17",
            "domain_name": "Test Domain 2",
            "domain_model_version_num": "1.2",
            "schema_name": "idl",
            "table_name": "dict_test_2",
            "table_extra_name": "test 2",
            "table_data_vault_type_cd": "IDL Dictionary",
            "table_desc": "Test 2",
            "table_extra_desc": "Test 2 Extra",
            "attribute_cnt": "6",
            "is_ref_flg": False,
            "sub_domain_name": None,
            "extra_sub_domain_name": None,
            "table_axon_id": "idl_dict_test_2",
            "table_axon_url": "https://axon.test/idl/dict_test_2",
            "table_model_version_from_num": "1.2",
            "table_model_version_to_num": None,
            "attribute_name": "test_2_cd",
            "attribute_extra_name": "test 2 code",
            "data_type_cd": "TEXT",
            "data_domain_cd": "dCodeIdentificator",
            "is_nullable_flg": True,
            "is_metadata_flg": False,
            "is_primary_key_flg": False,
            "is_foreign_key_flg": False,
            "foreign_schema_name": "idl",
            "foreign_table_name": None,
            "foreign_attribute_name": None,
            "attribute_desc": "Test 2 Key",
            "attribute_extra_desc": "Test 2 Code Extra",
            "attribute_axon_id": "dict_cd",
            "attribute_axon_url": "https://axon.test/idl/dict_test_2/cd",
            "attribute_model_version_from_num": "1.2",
            "attribute_model_version_to_num": None,
        },
        {
            "record_no": "18",
            "domain_name": "Test Domain 2",
            "domain_model_version_num": "1.2",
            "schema_name": "idl",
            "table_name": "dict_test_2",
            "table_extra_name": "test 2",
            "table_data_vault_type_cd": "IDL Dictionary",
            "table_desc": "Test 2",
            "table_extra_desc": "Test 2 Extra",
            "attribute_cnt": "6",
            "is_ref_flg": False,
            "sub_domain_name": None,
            "extra_sub_domain_name": None,
            "table_axon_id": "idl_dict_test_2",
            "table_axon_url": "https://axon.test/idl/dict_test_2",
            "table_model_version_from_num": "1.2",
            "table_model_version_to_num": None,
            "attribute_name": "effective_date",
            "attribute_extra_name": "effective date",
            "data_type_cd": "DATE",
            "data_domain_cd": "dEffectiveDate",
            "is_nullable_flg": False,
            "is_metadata_flg": True,
            "is_primary_key_flg": False,
            "is_foreign_key_flg": False,
            "foreign_schema_name": None,
            "foreign_table_name": None,
            "foreign_attribute_name": None,
            "attribute_desc": "Effective Date",
            "attribute_extra_desc": None,
            "attribute_axon_id": "idl_dict_effective_date",
            "attribute_axon_url": "https://axon.test/idl/dict_test_2/effective_date",
            "attribute_model_version_from_num": "1.2",
            "attribute_model_version_to_num": None,
        },
        {
            "record_no": "19",
            "domain_name": "Test Domain 2",
            "domain_model_version_num": "1.2",
            "schema_name": "idl",
            "table_name": "dict_test_2",
            "table_extra_name": "test 2",
            "table_data_vault_type_cd": "IDL Dictionary",
            "table_desc": "Test 2",
            "table_extra_desc": "Test 2 Extra",
            "attribute_cnt": "6",
            "is_ref_flg": False,
            "sub_domain_name": None,
            "extra_sub_domain_name": None,
            "table_axon_id": "idl_dict_test_2",
            "table_axon_url": "https://axon.test/idl/dict_test_2",
            "table_model_version_from_num": "1.2",
            "table_model_version_to_num": None,
            "attribute_name": "deleted_flg",
            "attribute_extra_name": "deleted flg",
            "data_type_cd": "BOOL",
            "data_domain_cd": "dDeletedFlag",
            "is_nullable_flg": False,
            "is_metadata_flg": True,
            "is_primary_key_flg": False,
            "is_foreign_key_flg": False,
            "foreign_schema_name": None,
            "foreign_table_name": None,
            "foreign_attribute_name": None,
            "attribute_desc": "Deleted Flag",
            "attribute_extra_desc": None,
            "attribute_axon_id": "idl_dict_deleted_flg",
            "attribute_axon_url": "https://axon.test/idl/dict_test_2/deleted_flg",
            "attribute_model_version_from_num": "1.2",
            "attribute_model_version_to_num": None,
        },
        {
            "record_no": "20",
            "domain_name": "Test Domain 2",
            "domain_model_version_num": "1.2",
            "schema_name": "idl",
            "table_name": "dict_test_2",
            "table_extra_name": "test 2",
            "table_data_vault_type_cd": "IDL Dictionary",
            "table_desc": "Test 2",
            "table_extra_desc": "Test 2 Extra",
            "attribute_cnt": "6",
            "is_ref_flg": False,
            "sub_domain_name": None,
            "extra_sub_domain_name": None,
            "table_axon_id": "idl_dict_test_2",
            "table_axon_url": "https://axon.test/idl/dict_test_2",
            "table_model_version_from_num": "1.2",
            "table_model_version_to_num": None,
            "attribute_name": "version_id",
            "attribute_extra_name": "version id",
            "data_type_cd": "BIGINT",
            "data_domain_cd": "dVersionIdentificator",
            "is_nullable_flg": False,
            "is_metadata_flg": True,
            "is_primary_key_flg": False,
            "is_foreign_key_flg": False,
            "foreign_schema_name": None,
            "foreign_table_name": None,
            "foreign_attribute_name": None,
            "attribute_desc": "Version ID",
            "attribute_extra_desc": None,
            "attribute_axon_id": "idl_dict_version_id",
            "attribute_axon_url": "https://axon.test/idl/dict_test_2/version_id",
            "attribute_model_version_from_num": "1.2",
            "attribute_model_version_to_num": None,
        },
        {
            "record_no": "21",
            "domain_name": "Test Domain 2",
            "domain_model_version_num": "1.2",
            "schema_name": "idl",
            "table_name": "dict_test_2",
            "table_extra_name": "test 2",
            "table_data_vault_type_cd": "IDL Dictionary",
            "table_desc": "Test 2",
            "table_extra_desc": "Test 2 Extra",
            "attribute_cnt": "6",
            "is_ref_flg": False,
            "sub_domain_name": None,
            "extra_sub_domain_name": None,
            "table_axon_id": "idl_dict_test_2",
            "table_axon_url": "https://axon.test/idl/dict_test_2",
            "table_model_version_from_num": "1.2",
            "table_model_version_to_num": None,
            "attribute_name": "hash_diff",
            "attribute_extra_name": "hash diff",
            "data_type_cd": "CHAR(32)",
            "data_domain_cd": "dHashIdentificator",
            "is_nullable_flg": False,
            "is_metadata_flg": True,
            "is_primary_key_flg": False,
            "is_foreign_key_flg": False,
            "foreign_schema_name": None,
            "foreign_table_name": None,
            "foreign_attribute_name": None,
            "attribute_desc": "Hash Diff",
            "attribute_extra_desc": None,
            "attribute_axon_id": "idl_dict_hash_diff",
            "attribute_axon_url": "https://axon.test/idl/dict_test_2/hash_diff",
            "attribute_model_version_from_num": "1.2",
            "attribute_model_version_to_num": None,
        },
        {
            "record_no": "22",
            "domain_name": "Test Domain 2",
            "domain_model_version_num": "1.2",
            "schema_name": "idl",
            "table_name": "dict_test_2",
            "table_extra_name": "test 2",
            "table_data_vault_type_cd": "IDL Dictionary",
            "table_desc": "Test 2",
            "table_extra_desc": "Test 2 Extra",
            "attribute_cnt": "6",
            "is_ref_flg": False,
            "sub_domain_name": None,
            "extra_sub_domain_name": None,
            "table_axon_id": "idl_dict_test_2",
            "table_axon_url": "https://axon.test/idl/dict_test_2",
            "table_model_version_from_num": "1.2",
            "table_model_version_to_num": None,
            "attribute_name": "src_cd",
            "attribute_extra_name": "src cd",
            "data_type_cd": "TEXT",
            "data_domain_cd": "dCodeIdentificator",
            "is_nullable_flg": False,
            "is_metadata_flg": True,
            "is_primary_key_flg": False,
            "is_foreign_key_flg": False,
            "foreign_schema_name": None,
            "foreign_table_name": None,
            "foreign_attribute_name": None,
            "attribute_desc": "Source Code",
            "attribute_extra_desc": None,
            "attribute_axon_id": "idl_dict_src_cd",
            "attribute_axon_url": "https://axon.test/idl/dict_test_2/src_cd",
            "attribute_model_version_from_num": "1.2",
            "attribute_model_version_to_num": None,
        },
    ]


@fixture(scope="session")
def version_id() -> int:
    return 0


@fixture(scope="session")
def effective_date() -> datetime:
    return datetime(2025, 1, 1, tzinfo=timezone.utc)


@fixture(scope="session")
def layer_id() -> int:
    return 3


@fixture(scope="session")
def schemas() -> Sequence[str]:
    return ["idl"]


@fixture(scope="session")
def as_of() -> date:
    return date(2025, 1, 1)


@fixture(scope="session")
def table_id() -> str:
    return UUID("00000000-0000-0000-0000-000000000000").hex


@fixture(scope="session")
def page_size() -> int:
    return 3


@fixture(scope="function")
def source(db_session):
    db_session.execute(
        text("""
            INSERT INTO metamodel.bridge_source(source_rk
                                              , source_cd
                                              , ris_src_id
                                              , ris_src_code
                                              , effective_from_dttm
                                              , effective_to_dttm
                                              , deleted_flg)
                 VALUES (1, 'DTPL', '1480', 'DTPL', '1900-01-01 +0', '2999-12-31 +0', FALSE)
        """)
    )
    db_session.commit()


@fixture(scope="function")
def truncate_tables(db_session):
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_source"))
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_logical_domain"))
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_logical_attribute"))
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_logical_table"))
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_logical_key"))
    db_session.execute(text("TRUNCATE TABLE metamodel.link_logical_key_attribute"))
    db_session.commit()
