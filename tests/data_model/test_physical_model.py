from datetime import datetime, timezone
from typing import Sequence
from uuid import UUID

from metaloader_rest_api.data_model.physical_model import load_physical_model
from pytest import fixture
from sqlalchemy import text


def test_load_physical_model(
    db_session,
    version_id,
    effective_date,
    ceh_dsn,
    schemas,
    table_id,
    page_size,
    truncate_tables,
    source,
    tables,
):
    load_physical_model(
        db_session,
        version_id,
        effective_date,
        ceh_dsn,
        schemas,
        table_id,
        page_size,
    )
    db_session.commit()

    rows = db_session.execute(
        text("SELECT * FROM metamodel.bridge_physical_table")
    ).fetchall()
    assert len(rows) == 4

    rows = db_session.execute(
        text("SELECT * FROM metamodel.bridge_physical_attribute")
    ).fetchall()
    assert len(rows) == 32

    load_physical_model(
        db_session,
        version_id,
        effective_date,
        ceh_dsn,
        schemas,
        table_id,
        page_size,
    )
    db_session.commit()

    rows = db_session.execute(
        text("SELECT * FROM metamodel.bridge_physical_table")
    ).fetchall()
    assert len(rows) == 4

    rows = db_session.execute(
        text("SELECT * FROM metamodel.bridge_physical_attribute")
    ).fetchall()
    assert len(rows) == 32


@fixture(scope="function")
def tables(ceh_cursor):
    ceh_cursor.execute("DROP SCHEMA IF EXISTS idl CASCADE")
    ceh_cursor.execute("CREATE SCHEMA idl")
    ceh_cursor.execute(f"""
        CREATE TABLE idl.test_1 (
            a_boolean     BOOLEAN
          , a_smallint    INT
          , a_int         INT
          , a_bigint      BIGINT
          , a_real        REAL
          , a_double      DOUBLE PRECISION
          , a_decimal     DECIMAL
          , a_date        DATE
          , a_time        TIME
          , a_timetz      TIMETZ
          , a_timestamp   TIMESTAMP
          , a_timestamptz TIMESTAMPTZ
          , a_interval    INTERVAL
          , a_char        CHAR
          , a_text        TEXT
          , a_uuid        UUID
          , a_json        JSON
          , a_jsonb       JSONB
        )
        DISTRIBUTED BY (a_smallint, a_bigint)
    """)
    ceh_cursor.execute(f"""
        CREATE TABLE idl.test_2 (
            a_time        TIME(3)
          , a_timetz      TIMETZ(3)
          , a_timestamp   TIMESTAMP(3)
          , a_timestamptz TIMESTAMPTZ(3)
          , a_interval    INTERVAL(3)
          , a_char        CHAR(3)
        )
        DISTRIBUTED REPLICATED
    """)
    ceh_cursor.execute(f"""
        CREATE TABLE idl.test_3 (
            a_boolean  BOOLEAN NOT NULL
          , a_smallint INT              DEFAULT 42
          , a_int      INT     NOT NULL DEFAULT -42
          , a_text     TEXT             DEFAULT ''
        )
    """)
    ceh_cursor.execute(f"""
        CREATE VIEW idl.test_4 AS
        SELECT * 
          FROM idl.test_3
    """)
    ceh_cursor.connection.commit()


@fixture(scope="session")
def version_id() -> int:
    return 0


@fixture(scope="session")
def effective_date() -> datetime:
    return datetime(2025, 1, 1, tzinfo=timezone.utc)


@fixture(scope="session")
def schemas() -> Sequence[str]:
    return ["idl"]


@fixture(scope="session")
def table_id() -> str:
    return UUID("00000000-0000-0000-0000-000000000000").hex


@fixture(scope="session")
def page_size() -> int:
    return 3


@fixture(scope="function")
def source(db_session):
    db_session.execute(
        text("""
            INSERT INTO metamodel.bridge_source(source_rk
                                              , source_cd
                                              , ris_src_id
                                              , ris_src_code
                                              , effective_from_dttm
                                              , effective_to_dttm
                                              , deleted_flg)
                 VALUES (1, 'DTPL', '1480', 'DTPL', '1900-01-01 +0', '2999-12-31 +0', FALSE)
        """)
    )
    db_session.commit()


@fixture(scope="function")
def truncate_tables(db_session):
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_source"))
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_physical_table"))
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_physical_attribute"))
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_physical_key"))
    db_session.execute(text("TRUNCATE TABLE metamodel.link_physical_key_attribute"))
    db_session.commit()
