import json
import os
from datetime import date, datetime
from pathlib import Path
from typing import Any, Iterator, Mapping, Sequence

import pytest
from metaloader_rest_api.data_model.data_model import load_data_model
from metaloader_rest_api.data_model.logical_model import load_logical_model
from metaloader_rest_api.data_model.physical_model import load_physical_model
from pytest import fixture
from sqlalchemy import text


@pytest.mark.skip("Выключен. Используется для загрузки физической модели на dev")
def test_load_physical_model_dev(
    db_session,
    version_id,
    effective_date,
    ceh_dsn_dev,
    schemas_idl_bdm,
    table_id,
):
    load_physical_model(
        session=db_session,
        version_id=version_id,
        effective_date=effective_date,
        dsn=ceh_dsn_dev,
        schemas=schemas_idl_bdm,
        table_id=table_id,
    )
    db_session.commit()


@pytest.mark.skip("Выключен. Используется для загрузки логической модели idl на dev")
def test_load_logical_model_dev_idl(
    db_session,
    version_id,
    effective_date,
    layer_id_idl,
    schema_idl,
    logical_model_data_idl,
    as_of,
    table_id,
):
    load_logical_model(
        session=db_session,
        version_id=version_id,
        effective_date=effective_date,
        layer_id=layer_id_idl,
        schemas=schema_idl,
        data=logical_model_data_idl,
        as_of=as_of,
        table_id=table_id,
    )
    db_session.commit()


@pytest.mark.skip("Выключен. Используется для загрузки логической модели bdm на dev")
def test_load_logical_model_dev_bdm(
    db_session,
    version_id,
    effective_date,
    layer_id_bdm,
    schema_bdm,
    logical_model_data_bdm,
    as_of,
    table_id,
):
    load_logical_model(
        session=db_session,
        version_id=version_id,
        effective_date=effective_date,
        layer_id=layer_id_bdm,
        schemas=schema_bdm,
        data=logical_model_data_bdm,
        as_of=as_of,
        table_id=table_id,
    )
    db_session.commit()


@pytest.mark.skip("Выключен. Используется для загрузки модели данных на dev")
def test_load_data_model_dev(
    db_session, version_id, effective_date, table_id, schemas_idl_bdm
):
    load_data_model(
        db_session,
        version_id,
        effective_date,
        schemas_idl_bdm,
        table_id,
    )
    db_session.commit()


def parse_logical_model_json(
    file_path: Path,
) -> tuple[list[str], Iterator[dict[str, Any]]]:
    with file_path.open("r", encoding="utf-8") as f:
        content = json.load(f)

    header = content.get("header")
    data = content.get("data")

    if header is None or data is None:
        raise ValueError("JSON должен содержать ключи 'header' и 'data'.")

    parsed_data: list[dict[str, Any]] = []
    for row in data:
        if len(row) != len(header):
            raise ValueError(
                "Длина строки не совпадает с количеством элементов в заголовке."
            )
        parsed_data.append(dict(zip(header, row)))

    return header, iter(parsed_data)


def get_dsn_dev() -> Mapping[str, Any]:
    return {
        "host": os.getenv("CEH_DEV_DB_HOST"),
        "port": os.getenv("CEH_DEV_DB_PORT"),
        "dbname": os.getenv("CEH_DEV_DB_NAME"),
        "user": os.getenv("CEH_DEV_DB_USER"),
        "password": os.getenv("CEH_DEV_DB_PASSWORD"),
    }


@fixture(scope="function")
def truncate_all_model_tables(db_session):
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_physical_table"))
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_physical_attribute"))
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_physical_key"))
    db_session.execute(text("TRUNCATE TABLE metamodel.link_physical_key_attribute"))
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_logical_domain"))
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_logical_attribute"))
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_logical_table"))
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_logical_key"))
    db_session.execute(text("TRUNCATE TABLE metamodel.link_logical_key_attribute"))
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_table"))
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_attribute"))
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_key"))
    db_session.execute(text("TRUNCATE TABLE metamodel.link_key_attribute"))
    db_session.commit()


@fixture(scope="function")
def truncate_physical_model_tables(db_session):
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_physical_table"))
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_physical_attribute"))
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_physical_key"))
    db_session.execute(text("TRUNCATE TABLE metamodel.link_physical_key_attribute"))
    db_session.commit()


@fixture(scope="function")
def truncate_logical_model_tables(db_session):
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_logical_domain"))
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_logical_attribute"))
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_logical_table"))
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_logical_key"))
    db_session.execute(text("TRUNCATE TABLE metamodel.link_logical_key_attribute"))
    db_session.commit()


@fixture(scope="function")
def truncate_data_model_tables(db_session):
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_table"))
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_attribute"))
    db_session.execute(text("TRUNCATE TABLE metamodel.bridge_key"))
    db_session.execute(text("TRUNCATE TABLE metamodel.link_key_attribute"))
    db_session.commit()


@fixture(scope="session")
def ceh_dsn_dev() -> Mapping[str, Any]:
    return get_dsn_dev()


@fixture(scope="session")
def as_of() -> date:
    return date(2025, 1, 1)


@fixture(scope="session")
def version_id() -> int:
    return 0


@fixture(scope="function")
def effective_date() -> datetime:
    return datetime(2024, 6, 15)


@fixture(scope="module")
def table_id() -> str:
    return 32 * "0"


@fixture(scope="session")
def schemas_idl_bdm() -> Sequence[str]:
    return ["idl", "bdm"]


@fixture(scope="session")
def schema_idl() -> Sequence[str]:
    return ["idl"]


@fixture(scope="session")
def schema_bdm() -> Sequence[str]:
    return ["bdm"]


@fixture(scope="session")
def layer_id_idl() -> int:
    return 3


@fixture(scope="session")
def layer_id_bdm() -> int:
    return 4


@pytest.fixture(scope="function")
def logical_model_data_bdm() -> Iterator[dict[str, Any]]:
    current_dir = Path(__file__).parent

    bdm_file_path = current_dir / "json" / "bdm" / "data.json"

    header_bdm, data_iter_bdm = parse_logical_model_json(bdm_file_path)

    return data_iter_bdm


@pytest.fixture(scope="function")
def logical_model_data_idl() -> Iterator[dict[str, Any]]:
    current_dir = Path(__file__).parent

    idl_file_path = current_dir / "json" / "idl" / "data.json"

    _, data_iter_idl = parse_logical_model_json(idl_file_path)

    return data_iter_idl
