import re
import time
from typing import Literal
from uuid import UUID

from metaloader_rest_api import models, schemas
from metaloader_rest_api.ceh_service.ceh_service_model import CehServiceType
from metaloader_rest_api.common_model import FIRST_DATE, LAST_DATE
from metaloader_rest_api.common_param import (
    DEPLOYMENT_MODULE_DEFAULT,
    DEPLOYMENT_SERVICE_DEFAULT,
)
from pytest import fail, fixture, mark
from sqlalchemy import text
from sqlalchemy.orm import Session
from starlette.testclient import TestClient

DUMMY_RELEASE_METADATA = {
    "release_num": "51.0.2",
    "release_desc": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut "
    "labore et dolore magna aliqua.",
    "release_date": "1970-01-01T00:00:00Z",
    "effective_from_date": "1970-01-01T00:00:00Z",
    "release_module": "core",
}


@mark.skip(reason="Включить, когда выкатим аутентификацию")
@mark.parametrize(
    "method, endpoint",
    [
        ("POST", "/v1/tx"),
        ("POST", "/v1/tx/0000/commit"),
        ("POST", "/v1/tx/0000/try"),
        ("POST", "/v1/tx/0000/rollback"),
        ("GET", "/v1/tx/0000"),
        ("POST", "/v1/sources/load_from_xlsx"),
        ("POST", "/v1/modules/load_from_xlsx"),
    ],
)
def test_requires_authentication(endpoint, method, test_client, monkeypatch):
    with monkeypatch.context() as m:
        m.delitem(test_client.headers, "Authorization")
        response = test_client.request(method, endpoint)
        assert response.status_code == 401

    with monkeypatch.context() as m:
        m.setitem(test_client.headers, "Authorization", "invalidtoken")
        response = test_client.request(method, endpoint)
        assert response.status_code == 401


@mark.parametrize(
    "endpoint_suffix",
    ["added", "modified", "deleted"],
)
def test_cant_add_actions_to_rolleback_transaction(
    test_client,
    endpoint_suffix,
    get_resource_action_payload,
    truncate_service,
    db_session: Session,
):
    create_tx = "/v1/resources/tx"
    response = test_client.post(
        create_tx, json={"release_metadata": DUMMY_RELEASE_METADATA}
    )
    assert response.status_code == 200
    tx_uid = response.json()

    assert (
        response := test_client.post(url=f"{create_tx}/{tx_uid}/rollback")
    ).status_code == 200, response.text

    response = test_client.post(
        url=f"/v1/resources/actions/{endpoint_suffix}",
        json=get_resource_action_payload(tx_uid, action=endpoint_suffix),
    )
    payload = response.json()
    assert response.status_code == 400, payload
    assert payload["detail"] == "Attempting to modify a transaction that is not open"


# noinspection DuplicatedCode
@mark.parametrize(
    "endpoint_suffix",
    ["added", "modified", "deleted"],
)
def test_cant_add_actions_to_transaction_in_progress(
    endpoint_suffix,
    get_resource_action_payload,
    test_client,
    truncate_service,
    db_session: Session,
):
    create_tx = "/v1/resources/tx"
    response = test_client.post(
        create_tx, json={"release_metadata": DUMMY_RELEASE_METADATA}
    )
    assert response.status_code == 200
    tx_uid = response.json()

    Tx = models.ResourceTransaction  # noqa
    db_session.execute(
        text(
            f"UPDATE {Tx.__table_args__['schema']}.{Tx.__tablename__}"
            f" SET {Tx.status.name} = {models.TransactionStatus.IN_PROGRESS.value}"
            f" WHERE {Tx.id.name} = '{tx_uid}';"
            f"COMMIT;"
        )
    )

    response = test_client.post(
        url=f"/v1/resources/actions/{endpoint_suffix}",
        json=get_resource_action_payload(tx_uid, action=endpoint_suffix),
    )
    payload = response.json()
    assert response.status_code == 400, payload
    assert payload["detail"] == "Attempting to modify a transaction that is not open"


@mark.parametrize(
    "endpoint_suffix",
    ["added", "modified", "deleted"],
)
def test_cant_add_actions_to_nonexistent_transaction(
    test_client,
    endpoint_suffix,
    get_resource_action_payload,
    truncate_service,
    db_session: Session,
):
    tx_uid = "00000000-0000-0000-0000-000000000000"
    response = test_client.post(
        url=f"/v1/resources/actions/{endpoint_suffix}",
        json=get_resource_action_payload(tx_uid, action=endpoint_suffix),
    )
    payload = response.json()
    assert response.status_code == 404, payload
    assert payload["detail"] == "Transaction not found"


@mark.parametrize(
    ",endpoint_suffix",
    ["added", "modified", "deleted"],
)
def test_single_action_per_entity(
    test_client,
    endpoint_suffix,
    get_resource_action_payload,
    recreate_schema_stg,
    truncate_service,
    truncate_tables,
    core_module,
    db_session: Session,
):
    response = test_client.post(
        url="/v1/resources/tx",
        json={"release_metadata": DUMMY_RELEASE_METADATA},
    )
    tx_uid = response.json()

    first_payload = get_resource_action_payload(tx_uid, action="added")
    second_payload = get_resource_action_payload(tx_uid, action=endpoint_suffix)

    assert (
        test_client.post(
            url="/v1/resources/actions/added", json=first_payload
        ).status_code
        == 200
    )

    response = test_client.post(
        url=f"/v1/resources/actions/{endpoint_suffix}", json=second_payload
    )
    assert response.status_code == 409, response.text
    response_json = response.json()
    assert (
        response_json["msg"]
        == "the transaction already has an action for this resource"
    )
    # Имена действий для разных сущностей совпадают, поэтому можно взять от любой
    assert response_json["existing_action"] == models.ResourceAction.ADD.name.lower()


def test_rollback_timeout_transactions(
    test_client,
):
    response = test_client.post(
        "/v1/resources/tx",
        json={"timeout": 0, "release_metadata": DUMMY_RELEASE_METADATA},
    )
    assert response.status_code == 200
    tx_uid = response.json()

    response = test_client.post(
        "/v1/resources/tx", json={"release_metadata": DUMMY_RELEASE_METADATA}
    )
    assert response.status_code == 200

    response = test_client.get(f"/v1/resources/tx/{tx_uid}")
    assert response.status_code == 200
    transaction_info = response.json()
    assert transaction_info["status"] == schemas.TransactionStatus.ROLLEDBACK.value


def test_resource_actions_staging_happy_path(
    test_client,
    db_session: Session,
    ceh_resource_json,
    uni_resource_json,
    truncate_service,
    recreate_schema_stg,
):
    response = test_client.post(
        "/v1/resources/tx",
        json={"release_metadata": DUMMY_RELEASE_METADATA},
    )
    assert response.status_code == 200
    tx_id = response.json()

    assert (
        test_client.post(
            "/v1/resources/actions/added",
            json={
                "tx_uid": tx_id,
                "resource_cd": "a_non_matching_resource_cd",
                "resource_type": "ceh",
                "definition": ceh_resource_json,
            },
        ).status_code
        == 200
    )

    assert (
        test_client.post(
            "/v1/resources/actions/deleted",
            json={
                "tx_uid": tx_id,
                "resource_cd": "resource2",
            },
        ).status_code
        == 200
    )

    assert (
        test_client.post(
            "/v1/resources/actions/modified",
            json={
                "tx_uid": tx_id,
                "resource_cd": uni_resource_json["resource_cd"],
                "resource_type": "uni",
                "definition": uni_resource_json,
            },
        ).status_code
        == 200
    )

    result = db_session.execute(
        text(f"""
            SELECT resource_cd, data_action_type_rk, definition
            FROM stg.bridge_resource_{UUID(tx_id).hex}
            ORDER BY data_action_type_rk
        """)
    ).fetchall()

    assert result == [
        (ceh_resource_json["resource_cd"], 1, ceh_resource_json),
        (uni_resource_json["resource_cd"], 2, uni_resource_json),
        ("resource2", 4, None),
    ]

    response = test_client.get(f"/v1/resources/tx/{tx_id}")
    assert response.status_code == 200
    tx_info = response.json()
    assert tx_info["status"] == schemas.TransactionStatus.OPENED.value
    assert tx_info["release_metadata"] == DUMMY_RELEASE_METADATA
    assert set(tx_info["resources"]) == {
        ceh_resource_json["resource_cd"],
        uni_resource_json["resource_cd"],
        "resource2",
    }


def test_resource_transaction_happy_path(
    test_client,
    session_shortcuts_wrapper,
    uni_resource_json,
    ceh_resource_json,
    truncate_tables,
    truncate_service,
    recreate_schema_stg,
    cleanup_redis,
    core_module,
):
    #
    # Создаём таблицы и источники, с которыми будут связаны добавляемые ресурсы
    #
    # TODO: надо это отрефакторить -- сделать датаклассы с дефолтами для всех таблиц метамодели,
    #  чтобы не приходилось заполнять все поля, когда для теста нужны только несколько
    session_shortcuts_wrapper.execute(
        text(
            f"""
        INSERT INTO metamodel.bridge_table (
            table_rk,
            effective_from_dttm,
            effective_to_dttm,
            deleted_flg,
            version_rk,
            domain_rk,
            data_layer_rk,
            source_rk,
            schema_name,
            table_name,
            table_type_rk,
            table_distribution_type_rk,
            is_dict_flg,
            is_map_flg,
            is_hub_flg,
            is_sal_flg,
            is_mart_flg,
            is_bridge_flg,
            is_link_flg,
            is_temporal_flg,
            is_accessor_flg,
            version_scd_type_rk,
            history_scd_type_rk,
            is_deprecated_flg
        )
        VALUES
             (1001, '2025-01-01', '{LAST_DATE}', false, 1, 1, 1, 1, 'schema1', 'table1', 1, 1, false, false, false, false, false, false, false, false, false, 1, 1, false)
            ,(1002, '2025-01-01', '{LAST_DATE}', false, 1, 1, 1, 1, 'schema2', 'table2', 1, 1, false, false, false, false, false, false, false, false, false, 1, 1, false)
        ;

        INSERT INTO metamodel.bridge_source (
            source_rk,
            source_cd,
            ris_src_id,
            ris_src_code,
            source_desc,
            effective_from_dttm,
            effective_to_dttm,
            deleted_flg
        )
        VALUES
        (2001, 'source1', 'src_id_1', 'src_code_1', 'Test Source 1', '2025-01-01', '{LAST_DATE}', false)
        """
        )
    )
    session_shortcuts_wrapper.commit()

    #
    # Загружаем ресурсы
    #
    response = test_client.post(
        "/v1/resources/tx", json={"release_metadata": DUMMY_RELEASE_METADATA}
    )
    assert response.status_code == 200
    tx_id = response.json()

    uni_resource_json["resource_cd"] = "uni_resource_added"
    uni_resource_json["datasets"] = [{"name": "table1", "schema_name": "schema1"}]
    uni_resource_json["features"] = {"source_system": "source1"}

    response = test_client.post(
        "/v1/resources/actions/added",
        json={
            "tx_uid": tx_id,
            "resource_cd": "uni_resource_added",
            "resource_type": "uni",
            "definition": uni_resource_json,
        },
    )
    assert response.status_code == 200

    response = test_client.post(
        "/v1/resources/actions/deleted",
        json={"tx_uid": tx_id, "resource_cd": "uni_resource_deleted"},
    )
    assert response.status_code == 200

    ceh_resource_json["resource_cd"] = "ceh_resource_modified"
    ceh_resource_json["datasets"] = [{"name": "table2", "schema_name": "schema2"}]
    ceh_resource_json["features"] = {"source_system": "source1"}

    response = test_client.post(
        "/v1/resources/actions/modified",
        json={
            "tx_uid": tx_id,
            "resource_cd": "ceh_resource_modified",
            "resource_type": "ceh",
            "definition": ceh_resource_json,
        },
    )
    assert response.status_code == 200

    response = test_client.post(
        f"/v1/resources/tx/{tx_id}/commit", json={"mode": "incremental"}
    )
    assert response.status_code == 202, response.json()
    assert "Location" in response.headers

    # TODO: обощить `wait_for_committed_tx` на разные типы транзакций и использовать её здесь
    for _ in range(3):
        time.sleep(1)
        response = test_client.get(f"/v1/resources/tx/{tx_id}")
        assert response.status_code == 200
        if response.json()["status"] == "committed":
            break
    else:
        fail("Transaction not committed in time")

    #
    # Проверяем, что ресурсы и связи с источниками и таблицами загружены в метамодель
    #
    bridge_resource = session_shortcuts_wrapper.execute_fetchall(
        """
        SELECT
            resource_cd
          , effective_to_dttm
          , deleted_flg
        FROM metamodel.bridge_resource
        ORDER BY 1, 2
        """
    )
    assert bridge_resource == [
        ("ceh_resource_modified", LAST_DATE, False),
        ("uni_resource_added", LAST_DATE, False),
    ]

    link_resource_source = session_shortcuts_wrapper.execute_fetchall(
        """
        SELECT
            res.resource_cd
          , src.source_cd
          , link.effective_to_dttm
          , link.deleted_flg
        FROM
            metamodel.link_resource_source AS link
            INNER JOIN metamodel.bridge_resource AS res ON
                link.resource_rk = res.resource_rk
            INNER JOIN metamodel.bridge_source AS src ON
                link.source_rk = src.source_rk
        ORDER BY 1, 2, 3
        """
    )
    assert link_resource_source == [
        ("ceh_resource_modified", "source1", LAST_DATE, False),
        ("uni_resource_added", "source1", LAST_DATE, False),
    ]

    link_resource_table = session_shortcuts_wrapper.execute_fetchall(
        """
        SELECT
            res.resource_cd
          , tbl.table_name
          , tbl.schema_name
          , link.effective_to_dttm
          , link.deleted_flg
        FROM
            metamodel.link_resource_table AS link
            INNER JOIN metamodel.bridge_resource AS res ON
                link.resource_rk = res.resource_rk
            INNER JOIN metamodel.bridge_table AS tbl ON
                link.table_rk = tbl.table_rk
        ORDER BY 1, 2, 3
        """
    )
    assert link_resource_table == [
        ("ceh_resource_modified", "table2", "schema2", LAST_DATE, False),
        ("uni_resource_added", "table1", "schema1", LAST_DATE, False),
    ]


def test_transaction_info_endpoint_scoping(
    test_client,
    db_session: Session,
    truncate_service,
):
    response = test_client.post(
        "/v1/resources/tx",
        json={"release_metadata": DUMMY_RELEASE_METADATA},
    )
    assert response.status_code == 200
    resource_tx_id = response.json()

    response = test_client.get(f"/v1/tx/{resource_tx_id}")
    assert response.status_code == 404
    assert response.json()["detail"] == "Transaction not found"

    response = test_client.get(f"/v1/resources/tx/{resource_tx_id}")
    assert response.status_code == 200


def test_load_all_flows(
    test_client,
    db_session,
    truncate_service,
    truncate_flows,
    service_id,
    flow_paths,
    flow_resources,
):
    for _ in range(2):
        tx_ix = (
            test_client.post("/v1/flows/load/all", json={}).raise_for_status().json()
        )
        tx_status = wait_for_complete_tx(test_client, tx_ix)

        assert tx_status == schemas.TransactionStatus.COMMITTED

        assert db_session.execute(
            text("""
                  SELECT "table", "count"
                    FROM (SELECT 1  "no", 'bridge_flow'              "table", COUNT(*) "count" FROM metamodel.bridge_flow
                           UNION ALL
                          SELECT 2  "no", 'link_flow'                "table", COUNT(*) "count" FROM metamodel.link_flow
                           UNION ALL
                          SELECT 3  "no", 'link_flow_service'        "table", COUNT(*) "count" FROM metamodel.link_flow_service
                           UNION ALL
                          SELECT 4  "no", 'bridge_master_flow'       "table", COUNT(*) "count" FROM metamodel.bridge_master_flow
                           UNION ALL
                          SELECT 5  "no", 'bridge_master_flow_item'  "table", COUNT(*) "count" FROM metamodel.bridge_master_flow_item
                           UNION ALL
                          SELECT 6 "no", 'link_master_flow_item'     "table", COUNT(*) "count" FROM metamodel.link_master_flow_item
                           UNION ALL
                          SELECT 7  "no", 'link_master_flow_x_item'  "table", COUNT(*) "count" FROM metamodel.link_master_flow_x_item
                           UNION ALL
                          SELECT 8  "no", 'bridge_flow_parameter'    "table", COUNT(*) "count" FROM metamodel.bridge_flow_parameter
                           UNION ALL
                          SELECT 9  "no", 'link_flow_parameter'      "table", COUNT(*) "count" FROM metamodel.link_flow_parameter
                           UNION ALL
                          SELECT 10 "no", 'link_flow_parameter_pass' "table", COUNT(*) "count" FROM metamodel.link_flow_parameter_pass
                           UNION ALL
                          SELECT 11 "no", 'link_flow_resource'       "table", COUNT(*) "count" FROM metamodel.link_flow_resource
                           UNION ALL
                          SELECT 12 "no", 'bridge_etl_algorithm'     "table", COUNT(*) "count" FROM metamodel.bridge_etl_algorithm) q
                ORDER BY "no"
            """)
        ).fetchall() == list(
            {
                "bridge_flow": 25,
                "link_flow": 28,
                "link_flow_service": 22,
                "bridge_master_flow": 2,
                "bridge_master_flow_item": 21,
                "link_master_flow_item": 19,
                "link_master_flow_x_item": 2,
                "bridge_flow_parameter": 1,
                "link_flow_parameter": 1,
                "link_flow_parameter_pass": 1,
                "link_flow_resource": 7,
                "bridge_etl_algorithm": 2,
            }.items()
        )


def test_load_master_flows(
    test_client,
    db_session,
    truncate_service,
    truncate_flows,
    service_id,
):
    tx_ix = test_client.post("/v1/flows/load", json={}).raise_for_status().json()
    tx_status = wait_for_complete_tx(test_client, tx_ix)

    assert tx_status == schemas.TransactionStatus.COMMITTED

    tx_ix = (
        test_client.post("/v1/flows/masters/load", json={}).raise_for_status().json()
    )
    tx_status = wait_for_complete_tx(test_client, tx_ix)

    assert tx_status == schemas.TransactionStatus.COMMITTED

    for _ in range(2):
        tx_ix = (
            test_client.post("/v1/flows/masters/items/load", json={})
            .raise_for_status()
            .json()
        )
        tx_status = wait_for_complete_tx(test_client, tx_ix)

        assert tx_status == schemas.TransactionStatus.COMMITTED

        assert db_session.execute(
            text("""
                  SELECT "table", "count"
                    FROM (SELECT 1 "no", 'bridge_flow'             "table", COUNT(*) "count" FROM metamodel.bridge_flow
                           UNION ALL
                          SELECT 2 "no", 'link_flow'               "table", COUNT(*) "count" FROM metamodel.link_flow
                           UNION ALL
                          SELECT 3 "no", 'link_flow_service'       "table", COUNT(*) "count" FROM metamodel.link_flow_service
                           UNION ALL
                          SELECT 4 "no", 'bridge_master_flow'      "table", COUNT(*) "count" FROM metamodel.bridge_master_flow
                           UNION ALL
                          SELECT 5 "no", 'bridge_master_flow_item' "table", COUNT(*) "count" FROM metamodel.bridge_master_flow_item
                           UNION ALL
                          SELECT 6 "no", 'link_master_flow_item'   "table", COUNT(*) "count" FROM metamodel.link_master_flow_item
                           UNION ALL
                          SELECT 7 "no", 'link_master_flow_x_item' "table", COUNT(*) "count" FROM metamodel.link_master_flow_x_item) q
                ORDER BY "no"
            """)
        ).fetchall() == list(
            {
                "bridge_flow": 25,
                "link_flow": 28,
                "link_flow_service": 22,
                "bridge_master_flow": 2,
                "bridge_master_flow_item": 21,
                "link_master_flow_item": 19,
                "link_master_flow_x_item": 2,
            }.items()
        )


def test_load_service(
    test_client,
    db_session,
    truncate_service,
    truncate_ceh_service,
):
    response = test_client.post(
        url="/v1/service/load",
        json=[
            {
                "service_type_cd": "service_type_cd_1",
                "environment_cd": "environment_cd_1",
                "service_cd": "service_cd_1",
                "service_name": "service_name_1",
                "service_desc": "service_desc_1",
                "service_host_name": "service_host_name_1",
                "service_url": "service_url_1",
                "service_alt_url": "service_alt_url_1",
                "code_delivery_cd": "code_delivery_cd_1",
                "ris_src_id": "ris_src_id_1",
                "ris_src_code": "ris_src_code_1",
            }
        ],
    )
    assert response.status_code == 200

    tx_ix = response.json()
    tx_state = wait_for_complete_tx(test_client, tx_ix)
    assert tx_state == schemas.TransactionStatus.COMMITTED

    services = db_session.execute(
        text("SELECT * FROM metamodel.bridge_service")
    ).fetchall()

    assert len(services) == 1


def test_load_resources_from_providers(
    test_client,
    db_session,
    ceh_provider_url,
    uni_provider_url,
    truncate_service,
    truncate_tables,
    core_module,
):
    response = test_client.post(
        url="/v1/resources/load_from_providers",
        json={
            "deployment": {
                "module": "core",
                "release": "1.2.3",
            },
            "uni_provider_base_url": uni_provider_url.docker,
            "ceh_provider_base_url": ceh_provider_url.docker,
            "limit": 10,
        },
    )
    assert response.status_code == 200

    tx_id = response.json()
    assert (
        wait_for_complete_tx(
            test_client,
            tx_id,
            attempts=3,
            timeout=3,
        )
        == schemas.TransactionStatus.COMMITTED
    )

    resources = db_session.execute(
        text("SELECT * FROM metamodel.bridge_resource")
    ).fetchall()

    # TO-DO: стоит отрефакторить, чтобы было по коду теста понятно, почему именно такое число
    # ресурсов загружается, подобно test_resource_providers_resource_loader.py
    assert len(resources) == 4


def test_metrics_heartbeat_when_worker_unavailable(
    test_client,
    celery_par_worker_container,
    celery_seq_worker_container,
):
    heartbeat_metric_pattern = re.compile(
        r"ceh_metamodel__heartbit\{[^}]+}\s+(\d+\.\d)"
    )

    # Проверяем, что heartbeat=1, когда запущены оба контейнера
    assert (
        celery_par_worker_container.status
        == celery_seq_worker_container.status
        == "running"
    )
    response = test_client.get("/metrics")
    assert response.status_code == 200
    metrics_content = response.content.decode("utf-8")
    match = heartbeat_metric_pattern.search(metrics_content)
    assert match
    healthy_heartbeat_value = float(match.group(1))
    assert healthy_heartbeat_value == 1.0

    try:
        celery_seq_worker_container.stop()  # noqa
        time.sleep(2)

        # Проверяем, что heartbeat стал 0
        response = test_client.get("/metrics")
        assert response.status_code == 200
        metrics_content = response.content.decode("utf-8")
        match = heartbeat_metric_pattern.search(metrics_content)
        assert match
        healthy_heartbeat_value = float(match.group(1))
        assert healthy_heartbeat_value == 0.0
    finally:
        celery_seq_worker_container.start()


def wait_for_complete_tx(
    test_client: TestClient,
    tx_id: UUID,
    attempts: int = 10,
    timeout: float = 5.0,
) -> schemas.TransactionStatus:
    status = None
    for attempt in range(attempts):
        time.sleep(timeout)
        response = test_client.get(f"/v1/tx/{tx_id}").raise_for_status().json()

        status = schemas.TransactionStatus(response.get("status"))
        if status.complete:
            return status

    raise TimeoutError(f"Transaction id={tx_id}, {status=} timed out")


@fixture(scope="function")
def core_module(db_session) -> str:
    # TODO: прохождение тестов зависит от порядка применения фикстур: этой и truncate_flow.
    # Стоит сделать единую фикстуру, которая будет ресетитть состояние тестовой базы.
    db_session.execute(
        text("""
        INSERT INTO dict.dict_code_delivery(code_delivery_rk
                                          , code_delivery_cd
                                          , code_delivery_name
                                          , is_ddl_flg
                                          , is_flow_flg
                                          , is_config_flg
                                          , deleted_flg)
        VALUES (155
              , 'core'
              , 'core'
              , FALSE
              , TRUE
              , FALSE
              , FALSE)
    """)
    )
    db_session.commit()

    return "core"


@fixture
def get_resource_action_payload(ceh_resource_json, uni_resource_json):
    def get_payload(
        tx_uid: str,
        action: Literal["added", "modified", "deleted"],
        resource_type: Literal["ceh", "uni"] = "ceh",
    ) -> dict:
        payload = dict(
            tx_uid=tx_uid,
            resource_cd=ceh_resource_json["resource_cd"],
            resource_type=schemas.ResourceType.CEH.value
            if resource_type == "ceh"
            else schemas.ResourceType.UNI.value,
            definition=ceh_resource_json
            if resource_type == "ceh"
            else uni_resource_json,
        )
        if action == "deleted":
            del payload["resource_type"]
            del payload["definition"]
        return payload

    return get_payload


@fixture(scope="function")
def service_id(
    db_session: Session,
    module_id: int,
    docker_airflow_url: str,
    docker_file_server_url: str,
) -> int:
    db_session.execute(
        statement=text(f"""
            TRUNCATE metamodel.bridge_service;
            INSERT INTO metamodel.bridge_service (service_rk
                                                , service_cd
                                                , service_host_name
                                                , service_url
                                                , service_alt_url
                                                , service_type_rk
                                                , environment_rk
                                                , effective_from_dttm
                                                , effective_to_dttm
                                                , deleted_flg)
            VALUES (3
                  , '{DEPLOYMENT_SERVICE_DEFAULT}'
                  , ''
                  , '{docker_airflow_url}'
                  , '{docker_file_server_url}/etl-scale'
                  , {CehServiceType.AIRFLOW}
                  , -1
                  , :effective_from_dttm
                  , :effective_to_dttm
                  , FALSE);

            TRUNCATE metamodel.link_service_delivery;
            INSERT INTO metamodel.link_service_delivery (service_rk
                                                       , code_delivery_rk)
            VALUES (3
                  , :code_delivery_rk)
        """),
        params={
            "code_delivery_rk": module_id,
            "effective_from_dttm": FIRST_DATE,
            "effective_to_dttm": LAST_DATE,
        },
    )
    db_session.commit()

    return 3


@fixture(scope="function")
def module_id(db_session: Session) -> int:
    db_session.execute(
        statement=text(f"""
            TRUNCATE dict.dict_code_delivery;
            INSERT INTO dict.dict_code_delivery(code_delivery_rk
                                              , code_delivery_cd
                                              , code_delivery_name
                                              , is_ddl_flg
                                              , is_flow_flg
                                              , is_config_flg
                                              , deleted_flg)
                 VALUES (1
                      , '{DEPLOYMENT_MODULE_DEFAULT}'
                      , '{DEPLOYMENT_MODULE_DEFAULT}'
                      , FALSE
                      , TRUE
                      , FALSE
                      , FALSE);
        """)
    )
    db_session.commit()

    return 1


@fixture(scope="function")
def truncate_ceh_service(db_session: Session):
    db_session.execute(
        text("""
            TRUNCATE metamodel.bridge_service
                   , metamodel.link_service_delivery
        """)
    )
    db_session.commit()


@fixture(scope="function")
def truncate_tables(db_session: Session):
    db_session.execute(
        text("""
            TRUNCATE dict.dict_code_delivery
                   , metamodel.bridge_version
                   , metamodel.bridge_table
                   , metamodel.bridge_source
                   , metamodel.bridge_resource
                   , metamodel.link_resource_source
                   , metamodel.link_resource_table
        """)
    )
    db_session.commit()


@fixture(scope="function")
def flow_resources(
    db_session: Session,
    module_id: int,
) -> None:
    db_session.execute(
        statement=text("""
            TRUNCATE metamodel.bridge_table;
            INSERT INTO metamodel.bridge_table(table_rk
                                             , domain_rk
                                             , data_layer_rk
                                             , source_rk
                                             , schema_name
                                             , table_name
                                             , table_type_rk
                                             , table_distribution_type_rk
                                             , is_dict_flg
                                             , is_map_flg
                                             , is_hub_flg
                                             , is_sal_flg
                                             , is_mart_flg
                                             , is_bridge_flg
                                             , is_link_flg
                                             , is_temporal_flg
                                             , is_accessor_flg
                                             , version_scd_type_rk
                                             , history_scd_type_rk
                                             , is_deprecated_flg
                                             , version_rk
                                             , effective_from_dttm
                                             , effective_to_dttm
                                             , deleted_flg)
                 VALUES (20, -1, -1, -1, 'rdv', 'mart_test_1', -1, -1, FALSE, FALSE, FALSE, FALSE, TRUE, FALSE, FALSE, FALSE, FALSE, -1, -1, FALSE, -1, :effective_from_dttm, :effective_to_dttm, FALSE)
                      , (21, -1, -1, -1, 'idl', 'bbridge_test_1', -1, -1, FALSE, FALSE, FALSE, FALSE, FALSE, TRUE, FALSE, FALSE, FALSE, -1, -1, FALSE, -1, :effective_from_dttm, :effective_to_dttm, FALSE);

            TRUNCATE metamodel.bridge_resource;
            INSERT INTO metamodel.bridge_resource(resource_rk
                                                , resource_cd
                                                , is_readonly_flg
                                                , is_maintenance_flg
                                                , tag_list
                                                , resource_json
                                                , version_rk
                                                , effective_from_dttm
                                                , effective_to_dttm
                                                , deleted_flg)
                 VALUES (10, 'source_uni_resource_test_1', FALSE, FALSE, NULL, 'null'::JSONB, -1, :effective_from_dttm, :effective_to_dttm, FALSE)
                      , (11, 'target_ceh_resource_test_1', FALSE, FALSE, NULL, 'null'::JSONB, -1, :effective_from_dttm, :effective_to_dttm, FALSE)
                      , (12, 'source_resource_test_1', FALSE, FALSE, NULL, 'null'::JSONB, -1, :effective_from_dttm, :effective_to_dttm, FALSE)
                      , (13, 'target_resource_test_1', FALSE, FALSE, NULL, 'null'::JSONB, -1, :effective_from_dttm, :effective_to_dttm, FALSE);
        """),
        params={
            "code_delivery_rk": module_id,
            "effective_from_dttm": FIRST_DATE,
            "effective_to_dttm": LAST_DATE,
        },
    )
    db_session.commit()


@fixture(scope="function")
def flow_paths(
    db_session: Session,
    module_id: int,
) -> None:
    db_session.execute(
        statement=text("""
            TRUNCATE dict.dict_code_folder;
            INSERT INTO dict.dict_code_folder(code_folder_rk
                                            , code_folder_cd  
                                            , code_folder_path) 
                 VALUES (4
                       , 'core_src_rdv_flow_dumps'
                       , 'general_ledger/src_rdv/flow_dumps')
                      , (5
                       , 'core_src_rdv_schema_work_flows'
                       , 'general_ledger/src_rdv/schema/work_flows')
                      , (6
                       , 'core_rdv_idl_flow_dumps'
                       , 'general_ledger/rdv_idl/flow_dumps')
                      , (7
                       , 'core_master_cf_conf'
                       , 'general_ledger/master_cf/conf');
                
            TRUNCATE dict.link_code_delivery_folder;
            INSERT INTO dict.link_code_delivery_folder(code_delivery_rk
                                                     , code_folder_rk
                                                     , is_required_flg
                                                     , is_flow_flg
                                                     , is_master_flow_flg
                                                     , is_ddl_flg
                                                     , is_resource_flg)
                 VALUES (:code_delivery_rk
                       , 4
                       , TRUE
                       , TRUE
                       , FALSE
                       , FALSE
                       , FALSE)
                      , (:code_delivery_rk
                       , 5
                       , TRUE
                       , TRUE
                       , FALSE
                       , FALSE
                       , FALSE)
                      , (:code_delivery_rk
                       , 6
                       , TRUE
                       , TRUE
                       , FALSE
                       , FALSE
                       , FALSE)
                      , (:code_delivery_rk
                       , 7
                       , TRUE
                       , FALSE
                       , TRUE
                       , FALSE
                       , FALSE);
        """),
        params={
            "code_delivery_rk": module_id,
        },
    )
    db_session.commit()


@fixture(scope="function")
def truncate_flows(db_session: Session):
    db_session.execute(
        text("""
            TRUNCATE metamodel.bridge_flow
                   , metamodel.link_flow
                   , metamodel.link_flow_service
                   , metamodel.bridge_master_flow
                   , metamodel.bridge_master_flow_item
                   , metamodel.link_master_flow_item
                   , metamodel.link_master_flow_x_item
                   , metamodel.bridge_flow_parameter
                   , metamodel.link_flow_parameter
                   , metamodel.link_flow_parameter_pass
                   , metamodel.link_flow_resource
                   , metamodel.bridge_etl_algorithm
        """)
    )
    db_session.commit()


# noinspection SqlWithoutWhere
@fixture(scope="function")
def truncate_service(db_session: Session):
    db_session.execute(
        text("""
            TRUNCATE metamodel.service_transaction_data
                   , metamodel.service_transaction_status
        """)
    )
    db_session.commit()


@fixture(scope="function")
def cleanup_redis(redis_client):
    redis_client.flushdb()


@fixture(scope="function")
def recreate_schema_stg(db_session: Session):
    db_session.execute(text("DROP SCHEMA IF EXISTS stg CASCADE"))
    db_session.execute(text("CREATE SCHEMA stg"))
    db_session.commit()
