from typing import Any, Dict, List, Optional, Sequence, Tuple

from sqlalchemy import BindParameter
from sqlalchemy.engine import Result
from sqlalchemy.orm import Session
from sqlalchemy.sql import text


class TableViewConfig:
    """Конфигурация отображения таблицы `TableView`"""

    DEFAULT_STRING_QUOTE = '"'
    DEFAULT_NULL_PLACEHOLDER = "<NULL>"
    DEFAULT_COLUMN_SEPARATOR = "\t"
    DEFAULT_ALIGN_RIGHT = False
    DEFAULT_QUOTE_STRINGS = True
    DEFAULT_DISPLAY_TABLE_NAME = True

    def __init__(
        self,
        *,
        align_right: bool = DEFAULT_ALIGN_RIGHT,
        quote_strings: bool = DEFAULT_QUOTE_STRINGS,
        display_table_name: bool = DEFAULT_DISPLAY_TABLE_NAME,
        string_quote: str = DEFAULT_STRING_QUOTE,
        null_placeholder: str = DEFAULT_NULL_PLACEHOLDER,
        column_separator: str = DEFAULT_COLUMN_SEPARATOR,
    ):
        self._align_right = align_right
        self._quote_strings = quote_strings
        self._display_table_name = display_table_name
        self._string_quote = string_quote
        self._null_placeholder = null_placeholder
        self._column_separator = column_separator

    @property
    def align_right(self) -> bool:
        return self._align_right

    @property
    def quote_strings(self) -> bool:
        return self._quote_strings

    @property
    def display_table_name(self) -> bool:
        return self._display_table_name

    @property
    def string_quote(self) -> str:
        return self._string_quote

    @property
    def null_placeholder(self) -> str:
        return self._null_placeholder

    @property
    def column_separator(self) -> str:
        return self._column_separator


class TableViewMeta:
    """Метаданные таблицы для `TableView`"""

    def __init__(
        self,
        *,
        table_name: Optional[str],
        columns: Optional[Tuple[str, ...]],
        config: Optional[TableViewConfig],
    ):
        self._table_name = table_name
        self._columns = columns
        self._config = config or TableViewConfig()

    @property
    def table_name(self) -> str:
        return self._table_name

    @property
    def columns(self) -> Tuple[str, ...]:
        return self._columns

    @property
    def config(self) -> TableViewConfig:
        return self._config


class TableView:
    """Представление табличных данных с поддержкой форматирования и сравнения"""

    def __init__(
        self,
        meta: TableViewMeta,
        data: Tuple[Tuple[Any, ...], ...] = (),
    ):
        self._meta = meta
        self._data = self._normalize_data(data)

    @property
    def meta(self) -> TableViewMeta:
        return self._meta

    @property
    def data(self) -> Tuple[Tuple[Any, ...], ...]:
        return self._data

    def __eq__(self, other):
        if not isinstance(other, TableView):
            return NotImplemented
        return self._meta.columns == other._meta.columns and self._data == other._data

    def __repr__(self):
        str_columns = list(map(str, self._meta.columns))
        num_cols = len(str_columns)

        str_data = []
        for row_data in self._data:
            str_data.append([self._format_value(item) for item in row_data])

        all_rows = [str_columns] + str_data
        col_widths = [
            max(len(r[col_idx]) for r in all_rows) for col_idx in range(num_cols)
        ]

        header = f"TableView{f' (table: {self._meta.table_name})' if self._meta.table_name and self._meta.config.display_table_name else ''}:\n"

        if not str_data:
            return header + self._format_row(str_columns, col_widths)

        lines = [self._format_row(str_columns, col_widths)] + [
            self._format_row(row, col_widths) for row in str_data
        ]
        return header + "\n".join(lines)

    @staticmethod
    def from_result(
        result: Result,
        table_name: Optional[str] = None,
        *,
        config: Optional[TableViewConfig] = None,
    ) -> "TableView":
        return TableView(
            TableViewMeta(
                table_name=table_name, columns=tuple(result.keys()), config=config
            ),
            tuple(tuple(row) for row in result.fetchall()),
        )

    @staticmethod
    def for_query(
        session: Session,
        query: str,
        table_name: Optional[str] = None,
        *,
        config: Optional[TableViewConfig] = None,
    ) -> "TableView":
        result = session.execute(text(query))
        return TableView.from_result(result, table_name, config=config)

    def _normalize_data(
        self,
        data: Tuple[Tuple[Any, ...], ...],
    ) -> Tuple[Tuple[Any, ...], ...]:
        num_cols = len(self._meta.columns)
        normalized_data = []

        for idx, row in enumerate(data):
            if len(row) > num_cols:
                raise ValueError(
                    f"Row {idx} has more values ({len(row)}) than columns ({num_cols}): {row}"
                )
            padded_row = row + (None,) * (num_cols - len(row))
            normalized_data.append(padded_row)

        return tuple(normalized_data)

    def _format_value(self, val: Any) -> str:
        if val is None:
            return self._meta.config.null_placeholder
        if isinstance(val, str):
            return (
                f"{self._meta.config.string_quote}{val}{self._meta.config.string_quote}"
                if self._meta.config.quote_strings
                else val
            )
        return str(val)

    def _format_row(self, row: Sequence[str], col_widths: Sequence[int]) -> str:
        return self._meta.config.column_separator.join(
            val.rjust(col_widths[i])
            if self._meta.config.align_right
            else val.ljust(col_widths[i])
            for i, val in enumerate(row)
        )


class TableViewDataInjector:
    """Вспомогательный класс для подготовки и работы с данными"""

    def __init__(
        self,
        session: Session,
        meta: TableViewMeta,
        rows: Tuple[Tuple[Any, ...], ...],
        *,
        bind_params: Optional[Sequence[BindParameter]] = None,
    ):
        self._session = session
        self._meta = meta
        self._rows = rows
        self._bind_params = bind_params or []

    def bind_params(
        self, bind_params: Sequence[BindParameter]
    ) -> "TableViewDataInjector":
        self._bind_params = bind_params or []
        return self

    def insert(self) -> None:
        if self._rows:
            table_view = TableView(self._meta, self._rows)
            self._session.execute(
                statement=text(f"""
                    INSERT INTO {table_view.meta.table_name} ({", ".join(table_view.meta.columns)})
                         VALUES ({", ".join(f":{col}" for col in table_view.meta.columns)})
                """).bindparams(*self._bind_params),
                params=self._to_dicts(table_view.meta.columns, table_view.data),
            )
            self._session.commit()

    def from_data(self) -> TableView:
        return TableView(meta=self._meta, data=self._rows)

    @staticmethod
    def _to_dicts(
        columns: Tuple[str, ...],
        data: Tuple[Tuple[Any, ...], ...],
    ) -> Sequence[Dict[str, Any]]:
        return [dict(zip(columns, row)) for row in data]


class TableViewBuilder:
    """Билдер для работы с `TableView` и данными"""

    def __init__(
        self,
        session: Session,
        *,
        table_name: Optional[str] = None,
        columns: Optional[List[str]] = None,
        config: Optional[TableViewConfig] = None,
    ):
        self._session = session
        self._table_name = table_name
        self._columns = columns or []
        self._config = config

    def table(self, name: str) -> "TableViewBuilder":
        self._table_name = name
        return self

    def columns(self, *column_names: str) -> "TableViewBuilder":
        self._columns = list(column_names)
        return self

    def configure(self, config: TableViewConfig) -> "TableViewBuilder":
        self._config = config
        return self

    def truncate(self) -> None:
        if not self._table_name:
            raise ValueError("Table name must be specified to perform cleanup.")
        self._session.execute(text(f"TRUNCATE TABLE {self._table_name}"))
        self._session.commit()

    def from_table(self, *sort: str) -> TableView:
        return TableView.for_query(
            self._session,
            self._build_query(sort=tuple(sort) if sort else None),
            self._table_name,
            config=self._config,
        )

    def from_query(self, query: str) -> TableView:
        return TableView.for_query(
            self._session, query, self._table_name, config=self._config
        )

    def data(
        self, rows: Optional[Tuple[Tuple[Any, ...], ...]] = ()
    ) -> TableViewDataInjector:
        if not self._table_name:
            raise ValueError("Columns must be defined before the data is set")
        return TableViewDataInjector(
            session=self._session,
            meta=TableViewMeta(
                table_name=self._table_name,
                columns=tuple(self._columns),
                config=self._config,
            ),
            rows=rows,
        )

    def _build_query(
        self,
        sort: Optional[Tuple[str, ...]] = None,
    ) -> str:
        if self._table_name is None:
            raise ValueError("Table name must be defined before generating the query.")
        if self._columns is None:
            raise ValueError("Columns must be defined before generating the query.")
        return f"""
                SELECT {", ".join(f'{col}' for col in self._columns)}
                  FROM {self._table_name}
            {"ORDER BY " + ", ".join(f'{col}' for col in sort) if sort else ""}
            """
