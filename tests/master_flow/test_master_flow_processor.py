from textwrap import dedent

from metaloader_rest_api.master_flow.master_flow_processor import (
    process_master_flow,
)


def test_process_master_flow():
    flow_source, flow_properties = process_master_flow(
        path="cf_master_test_1",
        source=_dedent("""
            from pathlib import Path

            from airflow.decorators import dag
            from airflow.utils.dates import days_ago
            from ceh_core_management.master_cf.cf_builder import (
                ControlFlowLoader,
                ControlFLowBuilder,
            )
            
            
            @dag(
                dag_id=Path(__file__).stem,
                start_date=days_ago(2),
                catchup=False,
                orientation="TB",
                schedule_interval=None,
            )
            def master_control_flow():
                control_flow = ControlFlowLoader(
                    cf_filename="cf_master_test_1.yaml",
                ).load_from_yaml()
                ControlFLowBuilder(
                    control_flow=control_flow,
                ).build()


            dag = master_control_flow()
        """),
    )

    assert flow_source == "cf_master_test_1.yaml"
    assert flow_properties == {}

    flow_source, flow_properties = process_master_flow(
        path="cf_master_test_2",
        source=_dedent("""
            from pathlib import Path

            from airflow.decorators import dag
            from airflow.utils.dates import days_ago
            from ceh_core_management.master_cf.cf_builder import (
                ControlFlowLoader,
                ControlFLowBuilder,
            )


            @dag(
                dag_id=Path(__file__).stem,
                start_date=days_ago(2),
                catchup=False,
                orientation="TB",
                schedule_interval=None,
            )
            def master_control_flow():
                control_flow = ControlFlowLoader(
                    cf_filename=f"{Path(__file__).stem}.yaml",
                ).load_from_yaml()
                ControlFLowBuilder(
                    control_flow=control_flow,
                    load_eod=True,
                    load_reglament=True,
                    receiver_names=[
                        "test_receiver_name_1",
                        "test_receiver_name_2",
                    ],
                    source_resource_name={
                        "test_source_resource_name_1": "test_another_source_resource_name_1",
                        "test_source_resource_name_2": "test_another_source_resource_name_2",
                    },
                    target_resource_name="test_target_resource_name",
                    src_cd="test_src_cd",
                    journal_table_name="test_journal_table_name",
                    delta_schema_name="test_delta_schema_name",
                ).build()


            dag = master_control_flow()
        """),
    )

    assert flow_source == "cf_master_test_2.yaml"
    assert flow_properties == {
        "load_eod": True,
        "load_reglament": True,
        "receiver_names": [
            "test_receiver_name_1",
            "test_receiver_name_2",
        ],
        "source_resource_name": {
            "test_source_resource_name_1": "test_another_source_resource_name_1",
            "test_source_resource_name_2": "test_another_source_resource_name_2",
        },
        "target_resource_name": "test_target_resource_name",
        "src_cd": "test_src_cd",
        "journal_table_name": "test_journal_table_name",
        "delta_schema_name": "test_delta_schema_name",
    }


def _dedent(sting: str) -> str:
    return dedent(sting).strip()
