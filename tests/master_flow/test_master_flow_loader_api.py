from datetime import datetime
from uuid import UUID

from metaloader_rest_api.ceh_service.ceh_service_model import CehServiceType
from metaloader_rest_api.common_model import FIRST_DATE, LAST_DATE
from metaloader_rest_api.common_param import (
    DEPLOYMENT_MODULE_DEFAULT,
    DEPLOYMENT_SERVICE_DEFAULT,
    FailsafeOptions,
)
from metaloader_rest_api.flow.flow_loader_api import LoadFlowsParams, load_flows
from metaloader_rest_api.master_flow.master_flow_loader_api import (
    LoadMasterFlowsParams,
    load_master_flows,
)
from pytest import fixture
from sqlalchemy import text
from sqlalchemy.orm import Session


def test_master_flow_loader_api(
    db_session: Session,
    load_id: UUID,
    effective_date: datetime,
    module_id: int,
    version_id: int,
    service_id: int,
    master_flow_path_id: int,
    flows,
):
    load_flows(
        session=db_session,
        load_id=load_id,
        effective_date=effective_date,
        params=LoadFlowsParams(
            failsafe_options=FailsafeOptions(
                threshold=1,
            ),
        ),
    )

    for _ in range(2):
        load_master_flows(
            session=db_session,
            load_id=load_id,
            effective_date=effective_date,
            params=LoadMasterFlowsParams(
                failsafe_options=FailsafeOptions(
                    threshold=1,
                ),
            ),
        )

        assert (
            db_session.scalar(text("SELECT COUNT(*) FROM metamodel.bridge_master_flow"))
            == 2
        )


@fixture(scope="function")
def flows(db_session: Session) -> None:
    db_session.execute(
        statement=text(f"""
            TRUNCATE metamodel.bridge_flow;
            TRUNCATE metamodel.link_flow;
            TRUNCATE metamodel.link_flow_service;
            TRUNCATE metamodel.bridge_master_flow;
        """),
    )
    db_session.commit()


@fixture(scope="function")
def master_flow_path_id(
    db_session: Session,
    module_id: int,
) -> int:
    db_session.execute(
        statement=text(f"""
            TRUNCATE dict.dict_code_folder;
            INSERT INTO dict.dict_code_folder(code_folder_rk
                                            , code_folder_cd  
                                            , code_folder_path) 
                 VALUES (4
                       , 'core_master_cf_conf'
                       , 'general_ledger/master_cf/conf');
                
            TRUNCATE dict.link_code_delivery_folder;
            INSERT INTO dict.link_code_delivery_folder(code_delivery_rk
                                                     , code_folder_rk
                                                     , is_required_flg
                                                     , is_flow_flg
                                                     , is_master_flow_flg
                                                     , is_ddl_flg
                                                     , is_resource_flg)
                 VALUES (:code_delivery_rk
                       , 4
                       , TRUE
                       , FALSE
                       , TRUE
                       , FALSE
                       , FALSE);
        """),
        params={
            "code_delivery_rk": module_id,
        },
    )
    db_session.commit()

    return 4


@fixture(scope="function")
def service_id(
    db_session: Session,
    module_id: int,
    airflow_url: str,
) -> int:
    db_session.execute(
        statement=text(f"""
            TRUNCATE metamodel.bridge_service;
            INSERT INTO metamodel.bridge_service (service_rk
                                                , service_cd
                                                , service_host_name
                                                , service_url
                                                , service_type_rk
                                                , environment_rk
                                                , effective_from_dttm
                                                , effective_to_dttm
                                                , deleted_flg)
            VALUES (3
                  , '{DEPLOYMENT_SERVICE_DEFAULT}'
                  , ''
                  , '{airflow_url}'
                  , {CehServiceType.AIRFLOW}
                  , -1
                  , :effective_from_dttm
                  , :effective_to_dttm
                  , FALSE);

            TRUNCATE metamodel.link_service_delivery;
            INSERT INTO metamodel.link_service_delivery (service_rk
                                                       , code_delivery_rk)
            VALUES (3
                  , :code_delivery_rk)
        """),
        params={
            "effective_from_dttm": FIRST_DATE,
            "effective_to_dttm": LAST_DATE,
            "code_delivery_rk": module_id,
        },
    )
    db_session.commit()

    return 3


@fixture(scope="function")
def version_id(
    db_session: Session,
    module_id: int,
) -> int:
    db_session.execute(
        statement=text("""
            TRUNCATE metamodel.bridge_version;
            INSERT INTO metamodel.bridge_version (version_rk
                                                , code_delivery_rk
                                                , release_major_num
                                                , release_minor_num
                                                , release_fix_num
                                                , release_delivery_dttm) 
                 VALUES (2
                       , :code_delivery_rk
                       , 999
                       , 999
                       , 999
                       , '2000-01-01 +0');
        """),
        params={
            "code_delivery_rk": module_id,
        },
    )
    db_session.commit()

    return 2


@fixture(scope="function")
def module_id(db_session: Session) -> int:
    db_session.execute(
        statement=text(f"""
            TRUNCATE dict.dict_code_delivery;
            INSERT INTO dict.dict_code_delivery(code_delivery_rk
                                              , code_delivery_cd
                                              , code_delivery_name
                                              , is_ddl_flg
                                              , is_flow_flg
                                              , is_config_flg
                                              , deleted_flg)
                 VALUES (1
                      , '{DEPLOYMENT_MODULE_DEFAULT}'
                      , '{DEPLOYMENT_MODULE_DEFAULT}'
                      , FALSE
                      , TRUE
                      , FALSE
                      , FALSE);
        """)
    )
    db_session.commit()

    return 1


@fixture(scope="module")
def effective_date() -> datetime:
    return datetime(2000, 1, 1)


@fixture(scope="module")
def load_id() -> UUID:
    return UUID("12345678123456781234567812345678")
