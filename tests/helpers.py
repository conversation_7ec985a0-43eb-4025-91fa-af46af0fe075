from collections import OrderedDict
from datetime import date, datetime, timezone
from itertools import chain
from typing import (
    Any,
    Iterable,
    Literal,
    Mapping,
    NamedTuple,
    Sequence,
    Tuple,
    Union,
    overload,
)

from metaloader_rest_api.helpers import all_equal
from sqlalchemy.engine import Result


class TableData:
    """Примитивный аналог pandas.Dataframe для облегчения работы с содержимым БД-таблицы в тестах"""

    class FromRows(NamedTuple):
        rows: Iterable[tuple]
        colnames: Sequence[str]

    class FromColumns(NamedTuple):
        columns: Mapping[str, Sequence]

    rows: Sequence[Mapping[str, Any]]
    columns: Mapping[str, Sequence]

    def __init__(self, content: Union[FromRows, FromColumns]):
        if isinstance(content, self.FromRows):
            _rows = tuple(content.rows)
            if not all_equal(len(seq) for seq in chain((content.colnames,), _rows)):
                raise ValueError(
                    "colnames and rows must all have the same number of elements"
                )

            self.rows = tuple(OrderedDict(zip(content.colnames, row)) for row in _rows)
            self.columns = OrderedDict(zip(content.colnames, zip(*_rows)))
        else:
            _colnames = content.columns.keys()
            _colvalues = content.columns.values()
            if not all_equal(map(len, _colvalues)):
                raise ValueError("all columns must have the same number of elements")

            self.rows = tuple(
                OrderedDict(zip(_colnames, row)) for row in zip(*_colvalues)
            )
            self.columns = content.columns

    @overload
    def __getitem__(self, colname: str) -> Sequence: ...

    @overload
    def __getitem__(self, colname: list[str]) -> Sequence[Tuple]: ...

    @overload
    def __getitem__(self, colname: tuple[str, ...]) -> Sequence[Tuple]: ...

    def __getitem__(self, colname):
        if isinstance(colname, str):
            return self.columns[colname]
        return list(zip(*map(self.columns.__getitem__, colname)))

    def query(self, expr: str) -> "TableData":
        matching_rows = filter(lambda row: bool(eval(expr, globals(), row)), self.rows)
        return TableData(
            self.FromRows(
                rows=(row.values() for row in matching_rows),
                colnames=self.columns.keys(),
            )
        )

    @classmethod
    def from_sqlalchemy_result(
        cls,
        result: Result,
        stringify_datetime_columns: Literal[True, False, "truncate_to_date"] = False,
    ) -> "TableData":
        colnames = result.keys()
        if not colnames:
            raise RuntimeError("result has no columns")

        rows_iter = result.fetchall()

        if not stringify_datetime_columns:
            return cls(cls.FromRows(rows=rows_iter, colnames=colnames))

        columns = OrderedDict(zip(colnames, zip(*rows_iter)))

        for colname, colvalues in columns.items():
            if not any(isinstance(val, (date, datetime)) for val in colvalues):
                continue
            truncate_to_date = (
                lambda datetime_str: datetime_str[:10]
                if stringify_datetime_columns == "truncate_to_date"
                else lambda x: x
            )
            columns[colname] = tuple(
                val and truncate_to_date(str(val)) for val in colvalues
            )

        return cls(cls.FromColumns(columns=columns))


def date_time(year, month, day, hour=0, minute=0, second=0, tz=timezone.utc):
    return datetime(year, month, day, hour, minute, second, tzinfo=tz)
