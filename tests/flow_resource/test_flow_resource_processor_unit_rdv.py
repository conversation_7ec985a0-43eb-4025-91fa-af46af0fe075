from metaloader_rest_api.file_provider.file_provider import FileProviderError
from metaloader_rest_api.flow_resource.flow_resource_model import (
    FlowResource,
    flow_resource_table,
    source_flow_resource,
    target_flow_resource,
)
from metaloader_rest_api.flow_resource.flow_resource_processor_unit_rdv import (
    RdvFlowResourceProcessorUnit,
)
from pytest import fixture, mark, param

from .conftest import load_flow_data

test_process_cases = [
    param(
        1,
        "wf_test_basic_case",
        [
            # NOTE: пайплайн парсинга-загрузки связей поток-ресурс сделан так, что дубли удаляются
            #  позже, уже внутри БД, на этапе загрузки в стейджинг. Поэтому в тестах намеренно
            #  оставляем дублирующиеся записи.
            dup_link_1 := target_flow_resource(
                algorithm="ALGORITHM_1",
                flow_id=1,
                resource_name="test.resource.tgt.1",
                resource_table=flow_resource_table(
                    schema_name="rdv",
                    table_name="table_1",
                ),
            ),
            source_flow_resource(
                algorithm="ALGORITHM_1",
                flow_id=1,
                resource_name="test.resource.src.1",
            ),
            target_flow_resource(
                algorithm="ALGORITHM_2",
                flow_id=1,
                resource_name="test.resource.tgt.2",
                resource_table=flow_resource_table(
                    schema_name="rdv", table_name="table_2"
                ),
            ),
            dup_link_2 := source_flow_resource(
                algorithm="ALGORITHM_2",
                flow_id=1,
                resource_name="test.resource.src.2",
            ),
            dup_link_1,
            source_flow_resource(
                algorithm="ALGORITHM_1",
                flow_id=1,
                resource_name="test.resource.src.2",
            ),
            target_flow_resource(
                algorithm="ALGORITHM_2",
                flow_id=1,
                resource_name="test.resource.tgt.1",
                resource_table=flow_resource_table(
                    schema_name="rdv",
                    table_name="table_1",
                ),
            ),
            dup_link_2,
        ],
        id="simple case: source and target in mappings",
    ),
    param(
        3,
        "wf_test_computed_table",
        [
            # Ресурсы из COMPUTED_TABLE маппингов
            target_flow_resource(
                algorithm="ALGORITHM_3",
                flow_id=3,
                resource_name="test.resource.tgt.1",
                resource_table=flow_resource_table(
                    schema_name="rdv",
                    table_name="table_1",
                ),
            ),
            source_flow_resource(
                algorithm="ALGORITHM_3",
                flow_id=3,
                resource_name="test.resource.src.2",
            ),
            target_flow_resource(
                algorithm="ALGORITHM_1",
                flow_id=3,
                resource_name="test.resource.tgt.1",
                resource_table=flow_resource_table(
                    schema_name="rdv",
                    table_name="table_1",
                ),
            ),
            source_flow_resource(
                algorithm="ALGORITHM_1",
                flow_id=3,
                resource_name="test.resource.src.1",
            ),
            source_flow_resource(
                algorithm="ALGORITHM_1",
                flow_id=3,
                resource_name="test.resource.src.2",
            ),
            # Ресурсы из простого маппинга
            target_flow_resource(
                algorithm="ALGORITHM_2",
                flow_id=3,
                resource_name="test.resource.tgt.1",
                resource_table=flow_resource_table(
                    schema_name="rdv",
                    table_name="table_1",
                ),
            ),
            source_flow_resource(
                algorithm="ALGORITHM_2",
                flow_id=3,
                resource_name="test.resource.src.1",
            ),
        ],
        id="case: COMPUTED_TABLE",
    ),
    param(
        4,
        "wf_test_business_key_schema",
        [
            # Ресурсы из основного маппинга
            target_flow_resource(
                algorithm="ALGORITHM_BK_TEST",
                flow_id=4,
                resource_name="test.resource.tgt.mart",
                resource_table=flow_resource_table(
                    schema_name="rdv",
                    table_name="table_mart",
                ),
            ),
            source_flow_resource(
                algorithm="ALGORITHM_BK_TEST",
                flow_id=4,
                resource_name="test.resource.src.data",
            ),
            # Ресурсы из hub_map с business_key_schema
            target_flow_resource(
                algorithm="ALGORITHM_BK_TEST",
                flow_id=4,
                resource_name="test.resource.hub.entity1.BK-key-schema-entity-1-TEST",
                resource_table=flow_resource_table(
                    schema_name="rdv",
                    table_name="hub_entity1",
                ),
            ),
            target_flow_resource(
                algorithm="ALGORITHM_BK_TEST",
                flow_id=4,
                resource_name="test.resource.hub.entity2.BK-key-schema-entity-2-TEST",
                resource_table=flow_resource_table(
                    schema_name="rdv",
                    table_name="hub_entity2",
                ),
            ),
            target_flow_resource(
                algorithm="ALGORITHM_BK_TEST",
                flow_id=4,
                resource_name="test.resource.hub.entity3.BK-key-schema-entity-3-TEST",
                resource_table=flow_resource_table(
                    schema_name="rdv",
                    table_name="hub_entity3",
                ),
            ),
        ],
        id="case: business_key_schema",
    ),
    param(
        5,
        "wf_test_dynamic_business_key_schema",
        [
            # Ресурсы из основного маппинга
            target_flow_resource(
                algorithm="ALGORITHM_DYNAMIC_BK_TEST",
                flow_id=5,
                resource_name="test.resource.tgt.mart",
                resource_table=flow_resource_table(
                    schema_name="rdv",
                    table_name="table_mart",
                ),
            ),
            source_flow_resource(
                algorithm="ALGORITHM_DYNAMIC_BK_TEST",
                flow_id=5,
                resource_name="test.resource.src.data",
            ),
            # Ресурсы из hub_map, где use_default_bk=false
            target_flow_resource(
                algorithm="ALGORITHM_DYNAMIC_BK_TEST",
                flow_id=5,
                resource_name="test.resource.hub.entity_dynamic.BK-rdv-entity-typeA-TEST",
                resource_table=flow_resource_table(
                    schema_name="rdv",
                    table_name="hub_entity_dynamic",
                ),
            ),
            target_flow_resource(
                algorithm="ALGORITHM_DYNAMIC_BK_TEST",
                flow_id=5,
                resource_name="test.resource.hub.entity_dynamic.BK-rdv-entity-typeB-TEST",
                resource_table=flow_resource_table(
                    schema_name="rdv",
                    table_name="hub_entity_dynamic",
                ),
            ),
            target_flow_resource(
                algorithm="ALGORITHM_DYNAMIC_BK_TEST",
                flow_id=5,
                resource_name="test.resource.hub.entity_dynamic.BK-rdv-entity-typeC-TEST",
                resource_table=flow_resource_table(
                    schema_name="rdv",
                    table_name="hub_entity_dynamic",
                ),
            ),
            # Ресурсы из hub_map, где use_default_bk=true
            target_flow_resource(
                algorithm="ALGORITHM_DYNAMIC_BK_TEST",
                flow_id=5,
                resource_name="test.resource.hub.entity_dynamic_default.BK-rdv-entity-default-TEST",
                resource_table=flow_resource_table(
                    schema_name="rdv",
                    table_name="hub_entity_dynamic_default",
                ),
            ),
        ],
        id="case: dynamic_business_key_schema",
    ),
    param(
        6,
        "wf_test_satellite_rk_map",
        [
            # Ресурсы из основного маппинга hub_satellites
            target_flow_resource(
                algorithm="ALGORITHM_HUB_SAT",
                flow_id=6,
                resource_name="test.resource.tgt.1",
                resource_table=flow_resource_table(
                    schema_name="rdv",
                    table_name="target_1",
                ),
            ),
            source_flow_resource(
                algorithm="ALGORITHM_HUB_SAT",
                flow_id=6,
                resource_name="test.resource.src.1",
            ),
            # Ресурсы из основного маппинга link_satellites
            target_flow_resource(
                algorithm="ALGORITHM_LINK_SAT",
                flow_id=6,
                resource_name="test.resource.tgt.3",
                resource_table=flow_resource_table(
                    schema_name="rdv",
                    table_name="target_3",
                ),
            ),
            source_flow_resource(
                algorithm="ALGORITHM_LINK_SAT",
                flow_id=6,
                resource_name="test.resource.src.1",
            ),
            # Ресурсы из hub_satellites.rk_map
            target_flow_resource(
                algorithm="ALGORITHM_HUB_SAT",
                flow_id=6,
                resource_name="test.resource.tgt.2.bk-schema-hub-sat-1-TEST",
                resource_table=flow_resource_table(
                    schema_name="rdv",
                    table_name="target_2",
                ),
            ),
            # Ресурсы из link_satellites.rk_map
            target_flow_resource(
                algorithm="ALGORITHM_LINK_SAT",
                flow_id=6,
                resource_name="test.resource.tgt.5.bk-schema-link-sat-1-TEST",
                resource_table=flow_resource_table(
                    schema_name="rdv",
                    table_name="target_5",
                ),
            ),
            target_flow_resource(
                algorithm="ALGORITHM_LINK_SAT",
                flow_id=6,
                resource_name="test.resource.tgt.5.bk-schema-link-sat-2-TEST",
                resource_table=flow_resource_table(
                    schema_name="rdv",
                    table_name="target_5",
                ),
            ),
        ],
        id="case: satellites rk_map",
    ),
    param(
        7,
        "wf_test_uni_map",
        [
            # Ресурсы из основного маппинга
            dup_link := target_flow_resource(
                algorithm="ALGORITHM_UNI_MAP_TEST",
                flow_id=7,
                resource_name="test.resource.tgt.1",
                resource_table=flow_resource_table(
                    schema_name="rdv",
                    table_name="table_1",
                ),
            ),
            source_flow_resource(
                algorithm="ALGORITHM_UNI_MAP_TEST",
                flow_id=7,
                resource_name="test.resource.src.1",
            ),
            dup_link,
            # Ресурсы из unified loader S2T для stg_uni_map
            source_flow_resource(
                algorithm="ALGORITHM_UNI_MAP_TEST",
                flow_id=7,
                resource_name="ACPD.ORS_CEH.C_TEST_UNI_MAP_ENTITY_1",
            ),
            target_flow_resource(
                algorithm="ALGORITHM_UNI_MAP_TEST",
                flow_id=7,
                resource_name="ceh.rdv.test_uni_map_entity_1",
                resource_table=flow_resource_table(
                    schema_name="ORS_CEH",
                    table_name="C_TEST_UNI_MAP_ENTITY_1",
                ),
            ),
            source_flow_resource(
                algorithm="ALGORITHM_UNI_MAP_TEST",
                flow_id=7,
                resource_name="ACPD.ORS_CEH.C_TEST_UNI_MAP_ENTITY_2",
            ),
            target_flow_resource(
                algorithm="ALGORITHM_UNI_MAP_TEST",
                flow_id=7,
                resource_name="ceh.rdv.test_uni_map_entity_2",
                resource_table=flow_resource_table(
                    schema_name="ORS_CEH",
                    table_name="C_TEST_UNI_MAP_ENTITY_2",
                ),
            ),
        ],
        id="case: unified loader stg_uni_map",
    ),
    param(
        8,
        "wf_test_uni_ref",
        [
            # Ресурсы из основного маппинга
            dup_link := target_flow_resource(
                algorithm="ALGORITHM_UNI_REF_TEST",
                flow_id=8,
                resource_name="test.resource.tgt.1",
                resource_table=flow_resource_table(
                    schema_name="rdv",
                    table_name="table_1",
                ),
            ),
            source_flow_resource(
                algorithm="ALGORITHM_UNI_REF_TEST",
                flow_id=8,
                resource_name="test.resource.src.1",
            ),
            dup_link,
            # Ресурсы из unified loader S2T для stg_uni_ref
            source_flow_resource(
                algorithm="ALGORITHM_UNI_REF_TEST",
                flow_id=8,
                resource_name="ACPD.ORS_CEH.C_TEST_UNI_REF_ENTITY_1",
            ),
            target_flow_resource(
                algorithm="ALGORITHM_UNI_REF_TEST",
                flow_id=8,
                resource_name="ceh.rdv.test_uni_ref_entity_1",
                resource_table=flow_resource_table(
                    schema_name="ORS_CEH",
                    table_name="C_TEST_UNI_REF_ENTITY_1",
                ),
            ),
            source_flow_resource(
                algorithm="ALGORITHM_UNI_REF_TEST",
                flow_id=8,
                resource_name="ACPD.ORS_CEH.C_TEST_UNI_REF_ENTITY_2",
            ),
            target_flow_resource(
                algorithm="ALGORITHM_UNI_REF_TEST",
                flow_id=8,
                resource_name="ceh.rdv.test_uni_ref_entity_2",
                resource_table=flow_resource_table(
                    schema_name="ORS_CEH",
                    table_name="C_TEST_UNI_REF_ENTITY_2",
                ),
            ),
        ],
        id="case: unified loader stg_uni_ref",
    ),
    param(
        9,
        "wf_test_uni_missing_s2t",
        [
            # Ресурсы из основного маппинга (должны работать нормально)
            target_flow_resource(
                algorithm="ALGORITHM_UNI_MISSING_TEST",
                flow_id=9,
                resource_name="test.resource.tgt.1",
                resource_table=flow_resource_table(
                    schema_name="rdv",
                    table_name="table_1",
                ),
            ),
            # Для missing S2T файла не должно быть дополнительных ресурсов
        ],
        id="case: missing S2T file error handling",
    ),
]


@mark.parametrize("flow_id, flow_name, expected", test_process_cases)
def test_process(
    flow_id,
    flow_name,
    expected,
    rdv_flow_resource_processor,
    yaml_src_rdv_path,
):
    _, _, flow_content = load_flow_data(yaml_src_rdv_path, flow_id, flow_name)
    actual = list(rdv_flow_resource_processor.process(flow_id, flow_name, flow_content))
    sort_flow_resources_inplace(actual, expected)
    assert actual == expected, f"{len(actual)=}, {len(expected)=}"


def test_process_with_s2t_file_provider_error(
    yaml_src_rdv_path,
    rdv_flow_resource_processor,
    monkeypatch,
):
    """
    Проверяет, что парсинг потоков, не требующих файлов S2T, не падает при ошибке в list_dir_items
    """
    flow_id, flow_name, expected = test_process_cases[0].values  # noqa
    mock_was_called = False

    def mock_list_dir_items(*_, **__):
        nonlocal mock_was_called
        mock_was_called = True
        raise FileProviderError("Mocked error for testing")

    monkeypatch.setattr(
        rdv_flow_resource_processor._http_loader,
        "list_dir_items",
        mock_list_dir_items,
    )
    _, _, flow_content = load_flow_data(yaml_src_rdv_path, flow_id, flow_name)
    actual = list(rdv_flow_resource_processor.process(flow_id, flow_name, flow_content))
    sort_flow_resources_inplace(actual, expected)

    assert mock_was_called
    assert actual == expected, f"{len(actual)=}, {len(expected)=}"


@fixture(scope="function")
def rdv_flow_resource_processor(http_loader):
    return RdvFlowResourceProcessorUnit(http_loader)


def sort_flow_resources_inplace(*args: list[FlowResource]) -> None:
    for flow_resources in args:
        flow_resources.sort(key=lambda r: (r["resource_name"], r["algorithm"]))
