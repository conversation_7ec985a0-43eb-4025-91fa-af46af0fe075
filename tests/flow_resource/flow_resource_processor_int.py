import json
import logging
import os
from datetime import datetime
from functools import partial
from pathlib import Path
from uuid import UUID

import structlog
from dotenv import load_dotenv
from metaloader_rest_api.common_model import LAST_DATE
from metaloader_rest_api.common_repository import SessionResource
from metaloader_rest_api.flow_model import FlowType
from metaloader_rest_api.flow_param.flow_param_processor import FlowParamProcessor
from metaloader_rest_api.flow_processor.flow_db_provider import FlowDBProvider
from metaloader_rest_api.flow_processor.flow_path_factory import get_flow_paths_factory
from metaloader_rest_api.flow_processor.flow_processor_int import _process_flows
from metaloader_rest_api.flow_processor.flow_processor_registry import (
    FlowProcessorRegistry,
)
from metaloader_rest_api.flow_resource.flow_resource_processor import (
    FlowResourceProcessor,
)
from metaloader_rest_api.local_file_profider.local_file_provider import (
    LocalFileProvider,
)
from metaloader_rest_api.models import ResourceAction
from metaloader_rest_api.resource_repository import (
    ResourceRepository,
    ResourceStageSequentialRepository,
)
from metaloader_rest_api.yaml.yaml_loader import get_yaml_loader
from orjson import orjson
from sqlalchemy import create_engine, text
from sqlalchemy.orm import Session, scoped_session, sessionmaker
from structlog.stdlib import get_logger

load_dotenv()

structlog.configure(
    cache_logger_on_first_use=True,
    wrapper_class=structlog.make_filtering_bound_logger(logging.INFO),
    processors=[
        structlog.contextvars.merge_contextvars,
        structlog.processors.add_log_level,
        structlog.processors.format_exc_info,
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.JSONRenderer(serializer=orjson.dumps),
    ],
    logger_factory=structlog.BytesLoggerFactory(),
)

log = get_logger(__name__)

db_engine = create_engine(
    url=os.getenv("DATABASE_URL"),
    json_serializer=partial(json.dumps, default=str),
)

session_maker = sessionmaker(bind=db_engine, autoflush=False)

session_factory = scoped_session(session_maker)

module_id = 1

version_id = 2

load_id = UUID("12345678123456781234567812345678")

effective_date = datetime(2000, 1, 1)

ceh_elt_path = Path("../../ceh-etl")

resources_path = ceh_elt_path / "_resources"

flows_path = ceh_elt_path / "general_ledger"

flows_paths = (
    "rdv_idl/flow_dumps",
    "src_rdv/flow_dumps",
)

flow_paths_factory = get_flow_paths_factory(flows_paths)

file_provider = LocalFileProvider(flows_path)

yaml_loader = get_yaml_loader()


def clean_flows_resources(db_session: Session) -> None:
    db_session.execute(text("TRUNCATE metamodel.link_flow_resource"))
    db_session.execute(text("TRUNCATE metamodel.bridge_etl_algorithm"))
    db_session.commit()


def load_flows_resources(db_session: Session):
    FlowProcessorRegistry.register(
        name="idl_resources",
        processor=FlowResourceProcessor,
    )
    flow_processor_registry = FlowProcessorRegistry(
        session=db_session,
        file_provider=file_provider,
        load_id=load_id,
        module_id=module_id,
        version_id=version_id,
        effective_date=effective_date,
        processors=[
            "idl_resources",
        ],
    )

    session_resource = SessionResource(db_session)
    flow_provider = FlowDBProvider(
        session=db_session,
        module_id=module_id,
    )

    _process_flows(
        session_resource=session_resource,
        flow_provider=flow_provider,
        flows_paths=flows_paths,
        file_provider=file_provider,
        yaml_loader=yaml_loader,
        flow_processor=flow_processor_registry,
        fail_threshold=5,
    )


def clean_flows_params(db_session: Session) -> None:
    db_session.execute(text("TRUNCATE metamodel.bridge_flow_parameter"))
    db_session.execute(text("TRUNCATE metamodel.link_flow_parameter"))
    db_session.execute(text("TRUNCATE metamodel.link_flow_parameter_pass"))
    db_session.commit()


def load_flows_params(db_session: Session):
    FlowProcessorRegistry.register(
        name="idl_parameters",
        processor=FlowParamProcessor,
    )
    flow_processor_registry = FlowProcessorRegistry(
        session=db_session,
        file_provider=file_provider,
        load_id=load_id,
        module_id=module_id,
        version_id=version_id,
        effective_date=effective_date,
        processors=[
            "idl_parameters",
        ],
    )

    session_resource = SessionResource(db_session)
    flow_provider = FlowDBProvider(
        session=db_session,
        module_id=module_id,
    )

    _process_flows(
        session_resource=session_resource,
        flow_provider=flow_provider,
        flows_paths=flows_paths,
        file_provider=file_provider,
        yaml_loader=yaml_loader,
        flow_processor=flow_processor_registry,
    )


def clean_flows(db_session: Session) -> None:
    db_session.execute(text("TRUNCATE metamodel.bridge_flow"))
    db_session.commit()


def load_flows(db_session: Session) -> None:
    statement = text("""
        INSERT INTO metamodel.bridge_flow(flow_name
                                        , flow_type_rk
                                        , flow_release_status_rk
                                        , code_delivery_rk
                                        , version_rk
                                        , effective_from_dttm
                                        , effective_to_dttm
                                        , deleted_flg)
            VALUES (:flow_name
                  , :flow_type_rk
                  , 1
                  , :code_delivery_rk
                  , :version_rk
                  , :effective_from_dttm
                  , :effective_to_dttm
                  , FALSE)
    """)
    for _flows_path in flows_paths:
        _flows_path = flows_path / _flows_path
        for flow_path in _flows_path.glob("*.yaml"):
            log.info("processing flow", path=flow_path)
            flow_name = flow_path.stem
            if flow_name.startswith("wrk_") or flow_name.startswith("wf_"):
                flow_type = FlowType.WORK
            elif flow_name.startswith("cf_"):
                flow_type = FlowType.CONTROL
            elif flow_name.startswith("sf_"):
                flow_type = FlowType.SERVICE
            else:
                flow_type = FlowType.UNDEFINED
            db_session.execute(
                statement=statement,
                params={
                    "flow_name": flow_name,
                    "flow_type_rk": flow_type,
                    "code_delivery_rk": module_id,
                    "version_rk": version_id,
                    "effective_from_dttm": effective_date,
                    "effective_to_dttm": LAST_DATE,
                },
            )
    db_session.commit()


def clean_tables(db_session: Session) -> None:
    db_session.execute(text("TRUNCATE metamodel.bridge_table"))
    db_session.commit()


def load_tables(db_session: Session) -> None:
    db_session.execute(
        statement=text("""
            INSERT INTO metamodel.bridge_table (domain_rk
                                              , data_layer_rk
                                              , source_rk
                                              , schema_name
                                              , table_name
                                              , table_type_rk
                                              , table_distribution_type_rk
                                              , is_dict_flg
                                              , is_map_flg
                                              , is_hub_flg
                                              , is_sal_flg
                                              , is_mart_flg
                                              , is_bridge_flg
                                              , is_link_flg
                                              , is_temporal_flg
                                              , is_accessor_flg
                                              , version_scd_type_rk
                                              , history_scd_type_rk
                                              , is_deprecated_flg
                                              , version_rk
                                              , effective_from_dttm
                                              , effective_to_dttm
                                              , deleted_flg) 
                 SELECT -1                   domain_rk
                      , -1                   data_layer_rk
                      , -1                   source_rk
                      , schema_name          schema_name
                      , table_name           table_name
                      , -1                   table_type_rk
                      , -1                   table_distribution_type_rk
                      , FALSE                is_dict_flg
                      , FALSE                is_map_flg
                      , FALSE                is_hub_flg
                      , FALSE                is_sal_flg
                      , FALSE                is_mart_flg
                      , FALSE                is_bridge_flg
                      , FALSE                is_link_flg
                      , FALSE                is_temporal_flg
                      , FALSE                is_accessor_flg
                      , -1                   version_scd_type_rk
                      , -1                   history_scd_type_rk
                      , FALSE                is_deprecated_flg
                      , :version_rk          version_rk
                      , :effective_from_dttm effective_from_dttm
                      , :effective_to_dttm   effective_to_dttm
                      , FALSE
                   FROM (SELECT DISTINCT *
                           FROM (SELECT JSONB_PATH_QUERY(resource_json, '$.datasets.schema_name') ->> 0 schema_name
                                      , JSONB_PATH_QUERY(resource_json, '$.datasets.name')        ->> 0 table_name
                                 FROM metamodel.bridge_resource) t
                          WHERE schema_name IN ('rdv', 'rdv_dict', 'idl', 'bdm')
                            AND table_name IS NOT NULL) t
        """),
        params={
            "version_rk": version_id,
            "effective_from_dttm": effective_date,
            "effective_to_dttm": LAST_DATE,
        },
    )
    db_session.commit()


def clean_resources(db_session: Session) -> None:
    db_session.execute(text("TRUNCATE metamodel.bridge_resource"))
    db_session.commit()


def load_resources(db_session: Session) -> None:
    resource_stage_sequential_repository = ResourceStageSequentialRepository(
        session=db_session,
        load_id=load_id,
    )
    resource_stage_sequential_repository.drop_table()
    resource_stage_sequential_repository.create_table()
    for path in resources_path.glob("**/*.json"):
        log.info("loading resource", path=path)
        try:
            definition = json.loads(path.read_bytes())
            resource_stage_sequential_repository.add_resource_action(
                resource_cd=path.stem,
                action=ResourceAction.ADD,
                definition=definition,
            )
        except Exception:
            log.exception("failed load resource", path=path)

    resource_repository = ResourceRepository(
        session=db_session,
    )
    stage_table = ResourceStageSequentialRepository.get_staging_table_name(
        load_id=load_id,
    )
    resource_repository.load_resources(
        version_id=version_id,
        effective_date=effective_date,
        stage_table=stage_table,
    )
    db_session.commit()


def load_version(db_session: Session) -> None:
    db_session.execute(text("TRUNCATE metamodel.bridge_version"))
    db_session.execute(
        statement=text("""
            INSERT INTO metamodel.bridge_version (version_rk
                                                , code_delivery_rk
                                                , release_major_num
                                                , release_minor_num
                                                , release_fix_num
                                                , release_delivery_dttm) 
                 VALUES (:version_rk
                       , :code_delivery_rk
                       , 999
                       , 999
                       , 999
                       , '2000-01-01 +0')
        """),
        params={
            "version_rk": version_id,
            "code_delivery_rk": module_id,
        },
    )
    db_session.commit()


def load_module(db_session: Session) -> None:
    db_session.execute(text("TRUNCATE dict.dict_code_delivery"))
    db_session.execute(
        statement=text("""
            INSERT INTO dict.dict_code_delivery(code_delivery_rk
                                              , code_delivery_cd
                                              , code_delivery_name
                                              , is_ddl_flg
                                              , is_flow_flg
                                              , is_config_flg
                                              , deleted_flg)
                 VALUES (:code_delivery_rk
                       , 'ETL-core'
                       , 'ETL-core'
                       , FALSE
                       , TRUE
                       , FALSE
                       , FALSE)
        """),
        params={
            "code_delivery_rk": module_id,
        },
    )
    db_session.commit()


if __name__ == "__main__":
    db_session = session_factory()
    with db_session:
        load_module(db_session)
        load_version(db_session)

        clean_resources(db_session)
        load_resources(db_session)

        clean_tables(db_session)
        load_tables(db_session)

        clean_flows(db_session)
        load_flows(db_session)

        clean_flows_params(db_session)
        load_flows_params(db_session)

        clean_flows_resources(db_session)
        load_flows_resources(db_session)
