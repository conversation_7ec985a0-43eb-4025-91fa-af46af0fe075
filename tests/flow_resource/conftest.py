import os

from metaloader_rest_api.http_file_provider.http_file_provider import HttpFileProvider
from metaloader_rest_api.yaml.yaml_loader import load_yaml
from pytest import fixture


@fixture(scope="function")
def http_loader(ceh_etl_src_url):
    with HttpFileProvider(url=ceh_etl_src_url) as http_loader:
        yield http_loader


def load_flow_data(path, flow_id, file_name):
    yaml_file_path = os.path.join(path, f"{file_name}.yaml")

    with open(yaml_file_path, "r", encoding="utf-8") as file:
        return flow_id, file_name, load_yaml(file.read())
