from metaloader_rest_api.http_file_provider.http_file_provider import (
    HttpFileProvider,
    _parse_html_directory_items,
)


def test_list_dir_items(local_file_server_url):
    with HttpFileProvider(url=local_file_server_url) as provider:
        items = set(provider.list_dir_items(path="test_http_file_provider/"))
    assert items == {
        "d/",
        "f",
        "index.json",
    }


def test_html_dir_parser():
    """Текущий Nginx из тестового сетапа не поддерживает отображение файлов, имя которых начинается
    с точки, а такие файлы в банковских средах есть
    """
    html = """
<h1>Содержимое каталога /app/airflow/etl-scale/files:</h1>
<ul>
  <li>
    <a href="/etl-scale/files">.. (на уровень выше)</a>
  </li>
  <li>
    <a foo="bar" href="/etl-scale/files/cf_master_ZFNT.xlsx">📄 cf_master_ZFNT.xlsx</a>
  </li>
  <li>
    <a href="/etl-scale/files/.yaml">.yaml</a>
  </li>
  <li>
    <a href="/etl-scale/files/mart_client_cl_bank_cftb">📁 mart_client_cl_bank_cftb/</a>
  </li>
</ul>
    """
    assert _parse_html_directory_items(html) == [
        "cf_master_ZFNT.xlsx",
        ".yaml",
        "mart_client_cl_bank_cftb",
    ]
