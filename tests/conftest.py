import json
import os
from contextlib import closing
from dataclasses import dataclass
from datetime import datetime, timedelta
from functools import partial
from typing import (
    Any,
    Collection,
    Mapping,
    NamedTuple,
    Sequence,
    Union,
)
from urllib.parse import urljoin

import docker
from celery import Celery
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa
from docker.models.containers import Container
from jose import jwt
from metaloader_rest_api import ceh_db
from metaloader_rest_api.common_repository import SessionResource
from metaloader_rest_api.helpers import JsonDict
from metaloader_rest_api.resource_schemas._ceh_provider import (
    Resource as CehResourceModel,
)
from metaloader_rest_api.resource_schemas._uni_provider import (
    ResourceUniRequest as UniResourceModel,
)
from metaloader_rest_api.settings import Settings, read_settings
from polyfactory.factories.pydantic_factory import ModelFactory
from psycopg2 import connect
from pytest import FixtureRequest, fixture
from redis import StrictRedis
from sqlalchemy import Engine, Row, create_engine, text
from sqlalchemy.orm import Session, scoped_session, sessionmaker
from starlette.testclient import TestClient
from table_view import TableViewBuilder


class CehResourceFactory(ModelFactory[CehResourceModel]):
    __model__ = CehResourceModel


class UniResourceFactory(ModelFactory[UniResourceModel]):
    __model__ = UniResourceModel


@dataclass
class SessionShortcutsWrapper:
    session: Session

    def execute_fetchall(self, query: str) -> Sequence[Row]:
        return self.session.execute(text(query)).fetchall()

    def __getattr__(self, name):
        return getattr(self.session, name)


class TestServiceUrl(NamedTuple):
    local: str
    docker: str


@fixture(scope="session")
def settings() -> Settings:
    return read_settings()


@fixture(scope="session")
def db_engine(settings) -> Engine:  # type: ignore
    engine = create_engine(
        settings.database_url.unicode_string(),
        json_serializer=partial(json.dumps, default=str),
        echo=False,
    )
    yield engine
    engine.dispose()


@fixture(scope="session")
def db_session_factory(db_engine: Engine):
    session_maker = sessionmaker(bind=db_engine, autoflush=False)
    return scoped_session(session_maker)


@fixture(scope="function")
def db_session(db_session_factory) -> Session:  # type: ignore
    session = db_session_factory()
    yield session
    session.rollback()
    session.close()


@fixture(scope="function")
def session_shortcuts_wrapper(db_session_factory) -> SessionShortcutsWrapper:  # type: ignore
    session = db_session_factory()
    yield SessionShortcutsWrapper(session)
    session.rollback()
    session.close()


@fixture(scope="function")
def table_view_builder(db_session: Session):
    def _make() -> TableViewBuilder:
        return TableViewBuilder(session=db_session)

    return _make


@fixture(scope="function")
def enable_log_statement_all(db_engine: Engine):
    db_alter_system(db_engine, "SET log_statement = 'all'")
    yield
    db_alter_system(db_engine, "RESET log_statement")


def db_alter_system(
    db_engine: Engine,
    expression: str,
) -> None:
    raw_connection = db_engine.raw_connection()
    try:
        dbapi_connection = raw_connection.dbapi_connection
        dbapi_connection.autocommit = True
        dbapi_cursor = dbapi_connection.cursor()
        try:
            dbapi_cursor.execute(f"ALTER SYSTEM {expression}")
            dbapi_cursor.callproc("pg_reload_conf")
        finally:
            dbapi_cursor.close()
    finally:
        raw_connection.close()


@fixture(scope="function")
def session_resource(
    db_session: Session,
) -> SessionResource:
    return SessionResource(db_session)


@fixture(scope="session")
def ceh_dsn() -> Mapping[str, Any]:
    return ceh_db.get_dsn()


@fixture(scope="session")
def ceh_connection(ceh_dsn):
    with connect(**ceh_dsn) as connection:
        yield connection


@fixture(scope="function")
def ceh_cursor(ceh_connection):
    with ceh_connection.cursor() as cursor:
        yield cursor


@fixture(scope="session")
def redis_client(settings) -> StrictRedis:  # type: ignore
    url = settings.celery_broker_url
    db = int(url.path[1:]) if url.path else "0"
    with StrictRedis(
        host=url.host,
        port=url.port,
        db=db,
    ) as client:
        yield client


@fixture(scope="session")
def celery_client(settings) -> Celery:
    with Celery("tasks", broker=settings.celery_broker_url.unicode_string()) as client:
        yield client


@fixture(scope="module")
def app_url() -> str:
    return os.getenv("APP_URL")


@fixture(scope="module")
def airflow_url(settings) -> str:
    return os.getenv("AF_URL")


@fixture(scope="module")
def docker_airflow_url(settings) -> str:
    return os.getenv("DOCKER_AF_URL")


@fixture(scope="session")
def test_client(request: FixtureRequest):
    private_key, public_key = generate_rsa_key_pair()
    auth_token = generate_auth_token(private_key)

    os.environ["RS256_PUBLIC_KEY"] = public_key

    from metaloader_rest_api.app import app

    client = TestClient(app)
    client.headers.update(
        {
            "Authorization": f"Bearer {auth_token}",
        }
    )

    return client


@fixture(scope="module")
def local_file_server_url() -> str:
    return os.getenv("LOCAL_FILE_SERVER_URL")


@fixture(scope="module")
def ceh_etl_src_url(local_file_server_url) -> str:
    return urljoin(local_file_server_url, "etl-scale/core/")


@fixture(scope="module")
def docker_file_server_url() -> str:
    return os.getenv("DOCKER_FILE_SERVER_URL")


@fixture(scope="module")
def uni_provider_url() -> TestServiceUrl:
    return TestServiceUrl(
        local=os.getenv("LOCAL_UNI_PROVIDER_URL"),
        docker=os.getenv("DOCKER_UNI_PROVIDER_URL"),
    )


@fixture(scope="module")
def ceh_provider_url() -> TestServiceUrl:
    return TestServiceUrl(
        local=os.getenv("LOCAL_CEH_PROVIDER_URL"),
        docker=os.getenv("DOCKER_CEH_PROVIDER_URL"),
    )


@fixture
def ceh_resource_json() -> JsonDict:
    resource = CehResourceFactory.build()
    return resource.model_dump(mode="json")


@fixture
def uni_resource_json() -> JsonDict:
    resource = UniResourceFactory.build()
    return resource.model_dump(mode="json")


@fixture(scope="module")
def yaml_directory_path():
    return os.path.join(
        os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
        "tests",
        "docker_",
        "nginx-file-server",
        "files",
        "etl-scale",
        "core",
        "general_ledger",
    )


@fixture(scope="module")
def yaml_rdv_idl_path(yaml_directory_path):
    return os.path.join(
        yaml_directory_path,
        "rdv_idl",
        "flow_dumps",
    )


@fixture(scope="module")
def yaml_src_rdv_path(yaml_directory_path):
    return os.path.join(
        yaml_directory_path,
        "src_rdv",
        "flow_dumps",
    )


@fixture
def docker_client():
    with closing(docker.from_env()) as client:
        yield client


@fixture
def celery_seq_worker_container(docker_client) -> Container:
    return docker_client.containers.get(os.environ["CELERY_WORKER_SEQ_CONTAINER_NAME"])


@fixture
def celery_par_worker_container(docker_client) -> Container:
    return docker_client.containers.get(os.environ["CELERY_WORKER_PAR_CONTAINER_NAME"])


def generate_rsa_key_pair() -> tuple[str, str]:
    private_key = rsa.generate_private_key(
        public_exponent=65537,
        key_size=2048,
        backend=default_backend(),
    )
    public_key = private_key.public_key()

    private_key_pem = private_key.private_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PrivateFormat.TraditionalOpenSSL,
        encryption_algorithm=serialization.NoEncryption(),
    )
    public_key_pem = public_key.public_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PublicFormat.SubjectPublicKeyInfo,
    )

    return private_key_pem.decode("utf-8"), public_key_pem.decode("utf-8")


def generate_auth_token(
    private_key: str,
    roles: Collection[str] = ("read_role", "modify_role"),
    expires: Union[int, datetime] = 30 * 60,
) -> str:
    payload = {
        "name": "john_doe",
        "realm_access": {"roles": roles},
        "exp": datetime.utcnow() + timedelta(seconds=expires)
        if isinstance(expires, int)
        else expires,
    }
    dummy_token = jwt.encode(
        claims=payload,
        key=private_key,
        algorithm="RS256",
    )

    return dummy_token
