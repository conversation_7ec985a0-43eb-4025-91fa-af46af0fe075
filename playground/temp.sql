select
    resource_rk as id,
    resource_cd as name,
    is_deprecated_flg as deprecated,
    deleted_flg as deleted,
    resource_desc as description,
    effective_from_dttm as updated_at,
    first_record.created_at,
    resource_json->>'type' as type
from metamodel.bridge_resource
    inner join (
        select
            resource_rk,
            min(effective_from_dttm) as created_at
        from metamodel.bridge_resource
        group by 1
    ) as first_record
        using (resource_rk)
where effective_to_dttm = :last_date
