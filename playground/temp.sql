select
    bridge_resource.resource_rk as id,
    bridge_resource.resource_cd as name,
    bridge_resource.is_deprecated_flg as deprecated,
    bridge_resource.deleted_flg as deleted,
    bridge_resource.resource_desc as description,
    bridge_resource.effective_from_dttm as updated_at,
    bridge_resource.tag_list as tags,
    first_record.created_at,
    dict_resource_type.resource_type_cd as type
from metamodel.bridge_resource_new as bridge_resource
    inner join (
        select
            resource_rk,
            min(effective_from_dttm) as created_at
        from metamodel.bridge_resource_new
        group by 1
    ) as first_record
        using (resource_rk)
    inner join dict.dict_resource_type
        using (resource_type_rk)
where effective_to_dttm = :last_date
