import html.parser
import re
from typing import Optional


class DirectoryHTMLParser(html.parser.HTMLParser):

    def __init__(self):
        super().__init__()
        self.directory_items: list[str] = []

        self._current_href: Optional[str] = None
        self._current_text: Optional[str] = None

    def handle_starttag(self, tag: str, attrs: list[tuple]) -> None:
        if tag != 'a':
            return

        self._current_href = self._current_text = None

        for attr_name, attr_value in attrs:
            if attr_name == 'href':
                self._current_href = attr_value
                break

    def handle_endtag(self, tag: str) -> None:
        if tag != 'a':
            return

        name_from_href = self._get_last_path_entry(self._current_href)
        if name_from_href and self._current_text.rstrip(" /").endswith(name_from_href):
            self.directory_items.append(name_from_href)

        self._current_href = self._current_text = None

    def handle_data(self, data: str) -> None:
        self._current_text = data

    @staticmethod
    def _get_last_path_entry(text: str) -> Optional[str]:
        if not text:
            return None
        match = re.search(r'[^/]+$', text)
        return match.group(0) if match else None


def parse_directory_html(html_content: str) -> list[str]:
    parser = DirectoryHTMLParser()
    parser.feed(html_content)
    return parser.directory_items


# Example usage and test
if __name__ == "__main__":
    sample_html = """<html>
<head><title>Index of /etl-scale/core/general_ledger/src_rdv/flow_dumps/</title></head>
<body>
<h1>Index of /etl-scale/core/general_ledger/src_rdv/flow_dumps/</h1><hr><pre><a href="../">../</a>
<a href="flow_test_2.yaml">flow_test_2.yaml</a>                                   21-Mar-2025 17:19      91
<a href="wf_test_basic_case.yaml">wf_test_basic_case.yaml</a>                            19-May-2025 12:34    1054
<a href="wf_test_business_key_schema.yaml">wf_test_business_key_schema.yaml</a>                   19-May-2025 12:34     971
<a href="wf_test_computed_table.yaml">wf_test_computed_table.yaml</a>                        24-May-2025 01:06     855
<a href="wf_test_dynamic_business_key_schema.yaml">wf_test_dynamic_business_key_schema.yaml</a>           19-May-2025 12:34     991
<a href="wf_test_satellite_rk_map.yaml">wf_test_satellite_rk_map.yaml</a>                      19-May-2025 12:34    1192
<a href="wf_test_uni_map.yaml">wf_test_uni_map.yaml</a>                               20-Jun-2025 23:08     552
<a href="wf_test_uni_missing_s2t.yaml">wf_test_uni_missing_s2t.yaml</a>                       20-Jun-2025 23:08     615
<a href="wf_test_uni_ref.yaml">wf_test_uni_ref.yaml</a>                               20-Jun-2025 23:08     552
</pre><hr></body>
</html>"""
    sample_html = """
<h1>Содержимое каталога /app/airflow/etl-scale/files:</h1><ul><li><a href="/etl-scale/files">.. (на уровень выше)</a></li><li><a foo=bar href="/etl-scale/files/cf_master_ZFNT.xlsx">📄 cf_master_ZFNT.xlsx</a></li><li><a href="/etl-scale/files/cf_master_ZFNT.yaml">📄 cf_master_ZFNT.yaml</a></li><li><a href="/etl-scale/files/cf_master_zfnt_idl_.png">📄 cf_master_zfnt_idl_.png</a></li><li><a href="/etl-scale/files/cf_master_zfnt_rdv_.png">📄 cf_master_zfnt_rdv_.png</a></li><li><a href="/etl-scale/files/checkpoint_vpn_clear.bat">📄 checkpoint_vpn_clear.bat</a></li><li><a href="/etl-scale/files/comments_old_flows.sh">📄 comments_old_flows.sh</a></li><li><a href="/etl-scale/files/excel_for_import_2023-02-27_786392433.xlsx">📄 excel_for_import_2023-02-27_786392433.xlsx</a></li><li><a href="/etl-scale/files/mart_client_cl_bank_cftb">📁 mart_client_cl_bank_cftb/</a></li></ul>"""

    # Parse the HTML
    files = parse_directory_html(sample_html)

    # Print results
    print("Parsed files from d listing:")
    print("-" * 50)

    tuple(map(print, files))

    print(f"\nTotal files found: {len(files)}")
