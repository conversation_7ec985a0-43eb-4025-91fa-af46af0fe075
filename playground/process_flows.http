POST https://build-metamodel.dtpl.corp.dev.vtb/v1/flows/process
Content-Type: application/json

{
  "ceh_etl_src_url": "https://develop-af.dtpl.corp.dev.vtb/etl-scale/",
  "deployment": {
    "service": "prod1",
    "module": "ETL-SCALE-core",
    "release": "999.999.999",
    "effective_date": "2025-05-20"
  }
}

###

POST http://localhost:8000/v1/resources/load_from_providers
#POST https://build-metamodel.dtpl.corp.dev.vtb/v1/resources/load_from_providers
Content-Type: application/json

{
  "module": "INVALID----ETL-SCALE-core",
  "release": "999.999.999",
  "uni_provider_base_url": "https://develop-uni.dtpl.corp.dev.vtb/api/1.0/",
  "ceh_provider_base_url": "https://develop-ceh.dtpl.corp.dev.vtb/api/0.6/"
}

###
@tx_uid =0743b6db-601d-4fd2-9c3a-87519bf4b917

#GET https://build-metamodel.dtpl.corp.dev.vtb/v1/tx/{{ tx_uid }}
GET http://localhost:8000/v1/tx/{{ tx_uid }}

###

GET https://develop-ceh.dtpl.corp.dev.vtb/api/0.6/resources?limit=100&offset=12

###

GET https://develop-af.dtpl.corp.dev.vtb/etl-scale/general_ledger/src_rdv/uni_load/cf_uni_map_S2T

###

GET https://develop-af.dtpl.corp.dev.vtb/etl-scale/general_ledger/src_rdv/uni_load/cf_uni_map_S2T.yaml

###

HEAD http://localhost:5090/etl-scale/core/general_ledger/src_rdv/flow_dumps/

###

GET localhost:8000/metrics
