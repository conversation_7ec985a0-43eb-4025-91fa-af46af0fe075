DO NOT CHANGE ANYTHING IN THE CODEBASE UNLESS EXPLICITLY REQUESTED.






INSERT INTO dict.dict_code_delivery VALUES (155, 'ETL-SCALE-core', 'core', '1480_1', false, true, false);





class TableReader(ExecuteInSessionMixin):
    def __init__(self, session: Session):
        self._session = session

    def bridge_resource(self) -> list[ResourceRecord]:
        result = self._execute(f"SELECT * FROM {TEST_SCHEMA}.bridge_resource")
        return [
            ResourceRecord(
                resource_rk=row.resource_rk,
                resource_cd=row.resource_cd,
                resource_desc=row.resource_desc,
                is_readonly_flg=row.is_readonly_flg,
                is_maintenance_flg=row.is_maintenance_flg,
                tag_list=row.tag_list or [],
                resource_json=row.resource_json or {},
                version_rk=row.version_rk,
                effective_from_dttm=row.effective_from_dttm.strftime("%Y-%m-%d"),
                effective_to_dttm=row.effective_to_dttm.strftime("%Y-%m-%d"),
                deleted_flg=row.deleted_flg,
            )
            for row in result
        ]

    def bridge_table(self) -> list[TableRecord]:
        result = self._execute(f"SELECT * FROM {TEST_SCHEMA}.bridge_table")
        return [
            TableRecord(
                table_rk=row.table_rk,
                table_name=row.table_name,
                schema_name=row.schema_name,
                effective_from_dttm=row.effective_from_dttm.strftime("%Y-%m-%d"),
                effective_to_dttm=row.effective_to_dttm.strftime("%Y-%m-%d"),
                deleted_flg=row.deleted_flg,
            )
            for row in result
        ]

    def link_resource_table(self) -> list[TableLinkRecord]:
        result = self._execute(f"SELECT * FROM {TEST_SCHEMA}.link_resource_table")
        return [
            TableLinkRecord(
                resource_rk=row.resource_rk,
                table_rk=row.table_rk,
                effective_from_dttm=row.effective_from_dttm.strftime("%Y-%m-%d"),
                effective_to_dttm=row.effective_to_dttm.strftime("%Y-%m-%d"),
                deleted_flg=row.deleted_flg,
            )
            for row in result
        ]

    def bridge_source(self) -> list[SourceRecord]:
        result = self._execute(f"SELECT * FROM {TEST_SCHEMA}.bridge_source")
        return [
            SourceRecord(
                source_rk=row.source_rk,
                source_cd=row.source_cd,
                effective_from_dttm=row.effective_from_dttm.strftime("%Y-%m-%d"),
                effective_to_dttm=row.effective_to_dttm.strftime("%Y-%m-%d"),
                deleted_flg=row.deleted_flg,
            )
            for row in result
        ]

    def link_resource_source(self) -> list[SourceLinkRecord]:
        result = self._execute(f"SELECT * FROM {TEST_SCHEMA}.link_resource_source")
        return [
            SourceLinkRecord(
                resource_rk=row.resource_rk,
                source_rk=row.source_rk,
                effective_from_dttm=row.effective_from_dttm.strftime("%Y-%m-%d"),
                effective_to_dttm=row.effective_to_dttm.strftime("%Y-%m-%d"),
                deleted_flg=row.deleted_flg,
            )
            for row in result
        ]
