I want to implement a new prometheus metric "heartbit" that reports the health of our service. It takes binary value -- 0 or 1.

Since our service consists of not just a webapp but also Celery (workers and broker), the metric should reflect the aggregated health of all components. I.e. simply implementing a new endpoint in the webapp that returns 200 is not enough. 

Let's brainstorm on the system design of this metric.
