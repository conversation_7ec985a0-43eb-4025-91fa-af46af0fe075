name: test-ceh-metamodel-app

services:
  app:
    build: .
    command: uvicorn metaloader_rest_api.app:app --host 0.0.0.0 --port 8000
    environment:
      DATABASE_URL: ********************************************************/d0_mon
      CELERY_BROKER_URL: redis://redis:6379/0
      AF_USERNAME: admin
      AF_PASSWORD: admin
      AUTH_DISABLED: true
      LOG_FORMAT: pretty
      LOG_LEVEL: INFO
      CEH_DB_HOST: ceh_db
      CEH_DB_PORT: 5432
      CEH_DB_NAME: testdb
      CEH_DB_USER: tester
      CEH_DB_PASSWORD: guest
    volumes:
      - ./src:/app
    ports:
      - "8000:8000"
    depends_on:
      db:
        condition: service_healthy
      airflow:
        condition: service_healthy
      worker_seq:
        condition: service_started
      worker_par:
        condition: service_started

  db:
    image: postgres:15.6-bullseye
    environment:
      POSTGRES_DB: d0_mon
      POSTGRES_USER: d0_mon_loader_tec
      POSTGRES_PASSWORD: d0_mon_loader_tec
    volumes:
      - ./tests/docker_/initdb:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -d d0_mon -U d0_mon_loader_tec" ]
      interval: 10s
      retries: 5
      start_period: 30s
      timeout: 10s
    restart: always

  airflow:
    image: ceh-metamodel/airflow-efficient:2.7.3
    environment:
      PYTHONUNBUFFERED: True
      PYTHONOPTIMIZE: True
      AIRFLOW__WEBSERVER__WORKERS: 1
      AIRFLOW__API__AUTH_BACKENDS: airflow.api.auth.backend.basic_auth
    volumes:
      - "./tests/docker_/airflow/dags:/opt/airflow/dags"
    command: standalone
    ports:
      - "8080:8080"
    healthcheck:
      test: ["CMD", "curl", "--fail", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    restart: always

  worker_seq:
    build: .
    command: >
      celery
      --app=metaloader_rest_api.celery_tasks.app worker
      --queues=sequential
      --pool=prefork
      --concurrency=1
    environment:
      # TO-DO: убрать дублирование значение переменных окружения
      # Для этого будет достаточно заменить здесь конкретные значения на ссылку на переменную и
      # просто перенести файл ".env" из папки "tests" в корень проекта -- пайтест из коробки умеет
      # находить дотэнв там. Правда ".env" в корне -- это гитигнорный паттеррн, поэтому нужно будет
      # ещё сделать ".env.template", где и указать какими в принципе переменными конфигурится приложение.
      DATABASE_URL: ********************************************************/d0_mon
      CELERY_BROKER_URL: redis://redis:6379/0
      LOG_FORMAT: pretty
      CEH_DB_HOST: ceh_db
      CEH_DB_PORT: 5432
      CEH_DB_NAME: testdb
      CEH_DB_USER: tester
      CEH_DB_PASSWORD: guest
      AF_USERNAME: admin
      AF_PASSWORD: admin
    depends_on:
      - redis
    volumes:
      - ./src:/app

  worker_par:
    build: .
    command: >
      celery
      --app=metaloader_rest_api.celery_tasks.app worker
      --queues=parallel
      --pool=prefork
      --concurrency=4
    environment:
      # TO-DO: убрать дублирование значение переменных окружения
      # Для этого будет достаточно заменить здесь конкретные значения на ссылку на переменную и
      # просто перенести файл ".env" из папки "tests" в корень проекта -- пайтест из коробки умеет
      # находить дотэнв там. Правда ".env" в корне -- это гитигнорный паттеррн, поэтому нужно будет
      # ещё сделать ".env.template", где и указать какими в принципе переменными конфигурится приложение.
      DATABASE_URL: ********************************************************/d0_mon
      CELERY_BROKER_URL: redis://redis:6379/0
      CEH_DB_HOST: ceh_db
      CEH_DB_PORT: 5432
      CEH_DB_NAME: testdb
      CEH_DB_USER: tester
      CEH_DB_PASSWORD: guest
      AF_USERNAME: admin
      AF_PASSWORD: admin
    depends_on:
      - redis
    volumes:
      - ./src:/app

  redis:
    image: redis:7.4.1
    ports:
      - "6379:6379"

  celery_prometheus_exporter:
    image: danihodovic/celery-exporter
    environment:
      CE_BROKER_URL: redis://redis:6379/0
    ports:
      - "9808:9808"

  nginx:
    image: nginx:latest
    container_name: nginx-file-server
    ports:
      - "5090:80"
    volumes:
      - ./tests/docker_/nginx-file-server/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./tests/docker_/nginx-file-server/files:/usr/share/nginx/html/files:ro
    restart: unless-stopped

  ceh_db:
    image: datagrip/greenplum:6.8
#    environment:
#      GP_DB: dwh
#      GP_USER: dwh_loader_tec
#      GP_PASSWORD: dwh_loader_tec
    security_opt:
      - seccomp:unconfined
    ports:
      - "54321:5432"
    healthcheck:
      test: grep -r -i --include \*.log "Database successfully started" || exit 1

  resource_provider_ceh:
    build: ./tests/docker_/resource_provider
    environment:
      PROVIDER_TYPE: ceh
    ports:
      - "8001:8000"
    volumes:
      - ./tests/docker_/resource_provider:/app
    healthcheck:
      test: ["CMD", "curl", "--fail", "http://localhost:8000/health"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s
    restart: unless-stopped

  resource_provider_uni:
    build: ./tests/docker_/resource_provider
    environment:
      PROVIDER_TYPE: uni
    ports:
      - "8002:8000"
    volumes:
      - ./tests/docker_/resource_provider:/app
    healthcheck:
      test: ["CMD", "curl", "--fail", "http://localhost:8000/health"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s
    restart: unless-stopped
