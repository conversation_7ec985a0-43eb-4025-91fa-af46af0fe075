# Основной образ
FROM docker.repo-ci.sfera.inno.local/dtpl-docker-lib/astra17-zero:1.7.6.11

ADD ./_certs/ /usr/local/share/ca-certificates/

ENV PATH="$PATH:/opt/helm"
ENV GOMEMLIMIT="5MiB"

RUN mkdir -p /app \
    && update-ca-certificates \
    && useradd -s /bin/bash -u 2000 -U -c "Application" app

COPY --from=docker.repo-ci.sfera.inno.local/dtpl-docker-snapshot/deploy/prometheus-scraper:f48555f74f9 \
    /prometheus-scraper /app/prometheus-scraper

RUN chown -R app:app /app \
    && find /app -type d -exec chmod 777 {} \; \
    && find /app -type f -exec chmod 666 {} \; \
    && chmod a+x /app/prometheus-scraper

USER app
WORKDIR /app
ENTRYPOINT /app/prometheus-scraper --op=tcp