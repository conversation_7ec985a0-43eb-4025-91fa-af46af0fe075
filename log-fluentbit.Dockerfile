FROM docker.repo-ci.sfera.inno.local/dtpl-docker-lib/astra17-zero:1.7.4.11 as certs-for-log
ADD ./_certs/ /usr/local/share/ca-certificates/
RUN update-ca-certificates

FROM docker.repo-ci.sfera.inno.local/dtpl-docker/fluent/fluent-bit:3.0.7
# Копируем оболочку и другие инструменты из промежуточного образа
COPY --from=docker.repo-ci.sfera.inno.local/dtpl-docker/library/busybox:1.32.0-glibc /bin/ /bin/
COPY --from=certs-for-log /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/ca-certificates.crt

# Устанавливаем рабочую директорию
WORKDIR /fluent-bit