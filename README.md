# Metamodel Loader HTTP API

## Настройка локального окружения

*Необходимо иметь установленную [Poetry](https://python-poetry.org/docs/#installation).*

- Создать контейнер с постгресом
- Сконфигурировать БД скриптом [Metamodel.sql](db/ddl/Metamodel.sql). Структура БД в банковских средах будет управляться внешним инструментом + разрабатывают её люди далёкие от питон-разработки, потому мы не можем использовать стандартный процесс с миграциями, а должны использовать вручную поддерживаемые скрипты, меняющие всю БД.
- Создать виртуалэнв с питоном 3.8, затем `poetry install`
- Настроить автоматический линтинг и фоматирование при коммите: `pre-commit install`

## Важно! Про обновление зависимостей

Локально мы используем Poetry. Её разработчики не рекомендуют устанавливать тул в тот же виртуалэнв, что и зависимости проекта. При этом в банковском контуре установка поэтри через curl в контейнер будет, скорее всего, недоступна из-за политик безопасности -- т.е. пришлось бы ставить с помощью pip. Чтобы избежать конфликтов зависимостей, в контейнере мы не ставим поэтри, а используем старый-добрый requirements.txt. Поэтому, при обновлении зависимостей в проекте необходимо обновить requirements.txt, см. Makefile. 

# ...
