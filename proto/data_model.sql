create table stg.bridge_logical_model (
    record_no                           bigint      not null,   -- index
    domain_name	                        text        not null,   -- subject_area
    domain_model_version_num	        text        not null,   -- model_version
    schema_name	                        text        not null,   -- table_owner
    table_name	                        text        not null,   -- table_code
    table_extra_name	                text,                   -- table_name
    table_data_vault_type_cd	        text,                   -- table_stereotype
    table_desc	                        text,                   -- table_comment
    table_extra_desc	                text,                   -- table_annotation
    attribute_cnt  	                    smallint,               -- columns_count
    is_ref_flg	                        bool        not null,   -- table_isShortcut
    sub_domain_name	                    text,                   -- sub_subject_area
    extra_sub_domain_name               text,                   -- sub_subject_area_additional
    table_axon_id     	                text,                   -- Axon_ID_table
    table_axon_url    	                text,                   -- Axon_URL_table
    table_model_version_from_num	    text        not null,   -- table_version
    table_model_version_to_num          text,                   -- table_deprecated_version
    attribute_name	                    text        not null,   -- column_code
    attribute_extra_name	            text,                   -- column_name
    data_type_cd	                    text,                   -- data_type
    data_domain_cd	                    text,                   -- domain
    is_nullable_flg	                    boolean     not null,   -- mandatory
    is_metadata_flg	                    boolean     not null,   -- technical_column
    is_primary_key_flg	                boolean     not null,   -- primary_key
    is_foreign_key_flg	                boolean     not null,   -- foreign_key
    foreign_schema_name                 text,                   -- parent_table_owner
    foreign_table_name                  text,                   -- parent_table_code
    foreign_attribute_name              text,                   -- parent_column_code
    attribute_desc	                    text,                   -- column_comment
    attribute_extra_desc	            text,                   -- column_annotation
    attribute_axon_id	                text,                   -- Axon_ID
    attribute_axon_url	                text,                   -- Axon_URL
    attribute_model_version_from_num    text        not null,   -- column_version
    attribute_model_version_to_num      text                    -- column_deprecated_version
);


create table stg.bridge_logical_key_attribute (
    key_name                        text        not null,
    schema_name	                    text        not null,
    logical_table_rk                bigint      not null,
    table_name                      text        not null,
    logical_attribute_rk            bigint      not null,
    attribute_name                  text        not null,
    attribute_no                    smallint    not null,
    foreign_logical_table_rk        bigint,
    foreign_logical_attribute_rk    bigint,
    foreign_schema_name             text,
    foreign_table_name              text,
    foreign_attribute_name          text,
    is_primary_flg                  bool        not null,
    is_foreign_flg                  bool        not null,
    model_major_version_from_num    smallint,
    model_minor_version_from_num    smallint,
    model_major_version_to_num	    smallint,
    model_minor_version_to_num	    smallint,
    is_deprecated_flg               bool        not null,
    model_effective_date            date        not null
);


create table stg.bridge_physical_key_attribute (
    key_name                        text        not null,
    schema_name	                    text        not null,
    physical_table_rk               bigint      not null,
    table_name                      text        not null,
    physical_attribute_rk           bigint      not null,
    attribute_name                  text        not null,
    attribute_no                    smallint    not null
);


-- metamodel.bridge_logical_domain
     SELECT
DISTINCT ON (m.domain_name
           , dl.data_layer_rk)
            -1                                                                                logical_domain_rk -- sequence
          , m.domain_name                                                                     domain_name
          , COALESCE(dl.data_layer_rk, -1)                                                    data_layer_rk
          , SPLIT_PART(m.domain_model_version_num, '.', 1)::SMALLINT                          model_major_version_num
          , COALESCE(NULLIF(SPLIT_PART(m.domain_model_version_num, '.', 2), '')::SMALLINT, 0) model_minor_version_num
       FROM stg.bridge_logical_model                 m
  LEFT JOIN metamodel.link_table_schema_x_data_layer dl
         ON dl.schema_name = m.schema_name
   ORDER BY m.domain_name
          , dl.data_layer_rk
          , model_major_version_num DESC
          , model_minor_version_num DESC
;


-- metamodel.bridge_logical_table
SELECT -1                                                             logical_table_rk    -- sequence
     , 'DTPL'                                                         source_cd
     , t.schema_name                                                  schema_name
     , t.table_name                                                   table_name
     , t.table_extra_name                                             table_extra_name
     , t.table_data_vault_type_cd                                     table_data_vault_type_cd
     , t.table_desc                                                   table_desc
     , t.table_extra_desc                                             table_extra_desc
     , t.attribute_cnt                                                attribute_cnt
     , t.is_ref_flg                                                   is_ref_flg
     , d.logical_domain_rk                                            logical_domain_rk
     , t.sub_domain_name                                              sub_domain_name
     , t.extra_sub_domain_name                                        extra_sub_domain_name
     , t.axon_id                                                      axon_id
     , t.axon_url                                                     axon_url
     , t.model_major_version_from_num                                 model_major_version_from_num
     , t.model_minor_version_from_num                                 model_minor_version_from_num
     , t.model_major_version_to_num                                   model_major_version_to_num
     , t.model_minor_version_to_num                                   model_minor_version_to_num
     , t.model_major_version_to_num IS NOT NULL
   AND CASE
       WHEN t.model_major_version_to_num  = d.model_major_version_num
       THEN t.model_minor_version_to_num <= d.model_minor_version_num
       ELSE t.model_major_version_to_num <  d.model_major_version_num
        END                                                           is_deprecated_flg
 FROM (SELECT
  DISTINCT ON (table_name
             , schema_name)
              schema_name
            , table_name
            , table_extra_name
            , table_data_vault_type_cd
            , table_desc
            , table_extra_desc
            , attribute_cnt
            , is_ref_flg
            , domain_name
            , sub_domain_name
            , extra_sub_domain_name
            , table_axon_id                                                                          axon_id
            , table_axon_url                                                                         axon_url
            , SPLIT_PART(table_model_version_from_num, '.', 1)::SMALLINT                             model_major_version_from_num
            , COALESCE(NULLIF(SPLIT_PART(table_model_version_from_num, '.', 2), '')::SMALLINT, 0)    model_minor_version_from_num
            , SPLIT_PART(table_model_version_to_num, '.', 1)::SMALLINT                               model_major_version_to_num
            , CASE WHEN table_model_version_to_num IS NOT NULL
              THEN COALESCE(NULLIF(SPLIT_PART(table_model_version_to_num, '.', 2), '')::SMALLINT, 0)
               END                                                                                   model_minor_version_to_num
         FROM stg.bridge_logical_model
     ORDER BY table_name
            , schema_name
            , model_major_version_to_num DESC NULLS FIRST
            , model_minor_version_to_num DESC NULLS FIRST) t
 JOIN metamodel.link_table_schema_x_data_layer             dl
   ON dl.schema_name = t.schema_name
 JOIN metamodel.bridge_logical_domain                      d
   ON d.domain_name    = t.domain_name
  AND dl.data_layer_rk = d.data_layer_rk
;


-- metamodel.bridge_logical_attribute
SELECT -1                                                             logical_attribute_rk    -- sequence
     , t.logical_table_rk                                             logical_table_rk
     , a.record_no                                                    record_no
     , a.schema_name                                                  schema_name
     , a.table_name                                                   table_name
     , a.attribute_name                                               attribute_name
     , a.attribute_extra_name                                         attribute_extra_name
     , a.data_type_cd                                                 data_type_cd
     , a.data_domain_cd                                               data_domain_cd
     , a.is_nullable_flg                                              is_nullable_flg
     , a.is_metadata_flg                                              is_metadata_flg
     , a.is_primary_key_flg                                           is_primary_key_flg
     , a.is_foreign_key_flg                                           is_foreign_key_flg
     , a.foreign_schema_name                                          foreign_schema_name
     , a.foreign_table_name                                           foreign_table_name
     , a.foreign_attribute_name                                       foreign_attribute_name
     , a.attribute_desc                                               attribute_desc
     , a.attribute_extra_desc                                         attribute_extra_desc
     , a.axon_id                                                      axon_id
     , a.axon_url                                                     axon_url
     , a.model_major_version_from_num                                 model_major_version_from_num
     , a.model_minor_version_from_num                                 model_minor_version_from_num
     , a.model_major_version_to_num                                   model_major_version_to_num
     , a.model_minor_version_to_num                                   model_minor_version_to_num
     , a.model_major_version_to_num IS NOT NULL
   AND CASE
       WHEN a.model_major_version_to_num  = d.model_major_version_num
       THEN a.model_minor_version_to_num <= d.model_minor_version_num
       ELSE a.model_major_version_to_num <  d.model_major_version_num
        END                                                           is_deprecated_flg
  FROM (SELECT
   DISTINCT ON (table_name
              , attribute_name
              , schema_name)
               record_no
             , domain_name
             , schema_name
             , table_name
             , attribute_name
             , attribute_extra_name
             , data_type_cd
             , data_domain_cd
             , is_nullable_flg
             , is_metadata_flg
             , is_primary_key_flg
             , is_foreign_key_flg
             , foreign_schema_name
             , foreign_table_name
             , foreign_attribute_name
             , attribute_desc
             , attribute_extra_desc
             , attribute_axon_id                                                                          axon_id
             , attribute_axon_url                                                                         axon_url
             , SPLIT_PART(attribute_model_version_from_num, '.', 1)::SMALLINT                             model_major_version_from_num
             , COALESCE(NULLIF(SPLIT_PART(attribute_model_version_from_num, '.', 2), '')::SMALLINT, 0)    model_minor_version_from_num
             , SPLIT_PART(attribute_model_version_to_num, '.', 1)::SMALLINT                               model_major_version_to_num
             , CASE WHEN attribute_model_version_to_num IS NOT NULL
               THEN COALESCE(NULLIF(SPLIT_PART(attribute_model_version_to_num, '.', 2), '')::SMALLINT, 0)
                END                                                                                       model_minor_version_to_num
          FROM stg.bridge_logical_model
      ORDER BY table_name
             , attribute_name
             , schema_name
             , model_major_version_to_num DESC NULLS FIRST
             , model_minor_version_to_num DESC NULLS FIRST) a
  JOIN metamodel.bridge_logical_table                       t
    ON t.table_name  = a.table_name
   AND t.schema_name = a.schema_name
  JOIN metamodel.link_table_schema_x_data_layer             dl
    ON dl.schema_name = t.schema_name
  JOIN metamodel.bridge_logical_domain                      d
    ON d.domain_name    = a.domain_name
   AND d.data_layer_rk  = dl.data_layer_rk
;


-- stg.bridge_logical_key_attribute
   SELECT table_name || '_pk'                              key_name
        , schema_name                                      schema_name
        , logical_table_rk                                 logical_table_rk
        , table_name
        , logical_attribute_rk                             logical_attribute_rk
        , attribute_name
        , ROW_NUMBER() OVER (PARTITION BY logical_table_rk
                                 ORDER BY record_no)       attribute_no
        , NULL                                             foreign_logical_table_rk
        , NULL                                             foreign_logical_attribute_rk
        , NULL                                             foreign_schema_name
        , NULL                                             foreign_table_name
        , NULL                                             foreign_attribute_name
        , TRUE                                             is_primary_flg
        , FALSE                                            is_foreign_flg
        , model_major_version_from_num                     model_major_version_from_num
        , model_minor_version_from_num                     model_minor_version_from_num
        , model_major_version_to_num                       model_major_version_to_num
        , model_minor_version_to_num                       model_minor_version_to_num
        , is_deprecated_flg                                is_deprecated_flg
        , model_effective_date                             model_effective_date
     FROM metamodel.bridge_logical_attribute
    WHERE is_foreign_key_flg IS FALSE
    UNION ALL
   SELECT a.table_name || '_' || a.attribute_name || '_fk' key_name
        , a.schema_name                                    schema_name
        , a.logical_table_rk                               logical_table_rk
        , a.table_name                                     table_name
        , a.logical_attribute_rk                           logical_attribute_rk
        , a.attribute_name                                 attribute_name
        , 1                                                attribute_no
        , af.logical_table_rk                              foreign_logical_table_rk
        , af.logical_attribute_rk                          foreign_logical_attribute_rk
        , a.foreign_schema_name                            foreign_schema_name
        , a.foreign_table_name                             foreign_table_name
        , a.foreign_attribute_name                         foreign_attribute_name
        , FALSE                                            is_primary_flg
        , TRUE                                             is_foreign_flg
        , a.model_major_version_from_num                   model_major_version_from_num
        , a.model_minor_version_from_num                   model_minor_version_from_num
        , a.model_major_version_to_num                     model_major_version_to_num
        , a.model_minor_version_to_num                     model_minor_version_to_num
        , a.is_deprecated_flg                              is_deprecated_flg
        , a.model_effective_date                           model_effective_date
     FROM (SELECT *
             FROM metamodel.bridge_logical_attribute
            WHERE is_foreign_key_flg IS TRUE)          a
LEFT JOIN metamodel.bridge_logical_attribute           af
       ON af.table_name     = a.foreign_table_name
      AND af.attribute_name = a.foreign_attribute_name
      AND af.schema_name    = a.foreign_schema_name
;


-- metamodel.bridge_logical_key
     SELECT
DISTINCT ON (key_name
           , schema_name)
            -1                           logical_key_rk -- sequence
          , key_name                     key_name
          , schema_name                  schema_name
          , logical_table_rk             logical_table_rk
          , table_name                   table_name
          , is_primary_flg               is_primary_flg
          , is_foreign_flg               is_foreign_flg
          , model_major_version_from_num model_major_version_from_num
          , model_minor_version_from_num model_minor_version_from_num
          , model_major_version_to_num   model_major_version_to_num
          , model_minor_version_to_num   model_minor_version_to_num
          , is_deprecated_flg            is_deprecated_flg
          , model_effective_date         model_effective_date
       FROM stg.bridge_logical_key_attribute
   ORDER BY key_name
          , schema_name
          , model_major_version_to_num DESC NULLS FIRST
          , model_minor_version_to_num DESC NULLS FIRST
          , model_effective_date       DESC
;


-- metamodel.link_logical_key_attribute
   SELECT k.logical_key_rk                logical_key_rk
        , ka.logical_attribute_rk         logical_attribute_rk
        , ka.schema_name                  schema_name
        , ka.key_name                     key_name
        , ka.table_name                   table_name
        , ka.attribute_name               attribute_name
        , ka.attribute_no                 attribute_no
        , kf.logical_key_rk               foreign_logical_key_rk
        , kf.key_name                     foreign_key_name
        , ka.foreign_logical_attribute_rk foreign_logical_attribute_rk
        , ka.foreign_schema_name          foreign_schema_name
        , ka.foreign_table_name           foreign_table_name
        , ka.foreign_attribute_name       foreign_attribute_name
     FROM stg.bridge_logical_key_attribute                  ka
     JOIN metamodel.bridge_logical_key                      k
       ON k.key_name    = ka.key_name
      AND k.schema_name = ka.schema_name
LEFT JOIN metamodel.bridge_logical_key                      kf
       ON kf.logical_table_rk = ka.foreign_logical_table_rk
      AND kf.is_primary_flg IS TRUE
      AND ka.is_foreign_flg IS TRUE
;


-- metamodel.bridge_physical_table
   SELECT ns.nspname    table_schema
        , c.relname     table_name
        , c.relkind     table_type_cd
        , d.description table_desc
        , dp.policytype distribution_type_cd
     FROM pg_catalog.pg_class c
     JOIN pg_catalog.pg_namespace ns
       ON ns.oid = c.relnamespace
      AND ns.nspname IN ('idl')
      AND c.relkind IN ('r', 'v')
LEFT JOIN pg_catalog.pg_description d
       ON d.objoid = c.oid
      AND d.objsubid = 0
LEFT JOIN pg_catalog.gp_distribution_policy dp
       ON dp.localoid = c.oid
    WHERE NOT EXISTS (SELECT NULL
                        FROM pg_catalog.pg_inherits i
                       WHERE i.inhrelid = c.oid)
;


-- metamodel.bridge_physical_attribute
   SELECT ns.nspname                                                         table_schema
        , c.relname                                                          table_name
        , a.attname                                                          attribute_name
        , a.attnum                                                           attribute_no
        , a.atttypid                                                         data_type_cd
        , CASE
          WHEN a.atttypid = 1042
          THEN CASE
               WHEN a.atttypmod != -1
               THEN a.atttypmod - 4
                END
          WHEN a.atttypid = 1700
          THEN CASE
               WHEN a.atttypmod != -1
               THEN ((a.atttypmod - 4) >> 16) & 65535
                END
          WHEN a.atttypid IN (1083, 1114, 1184, 1266)
          THEN CASE
               WHEN a.atttypmod < 0
               THEN 6
               ELSE a.atttypmod
                END
          WHEN a.atttypid = 1186
           THEN CASE
                WHEN a.atttypmod < 0
                  OR a.atttypmod & 65535 = 65535
                THEN 6
                ELSE a.atttypmod & 65535
                 END
           END                                                               data_type_size_cnt
        , CASE a.atttypid
          WHEN 1700
          THEN CASE WHEN a.atttypmod != -1
                    THEN (a.atttypmod - 4) & 65535
                END
           END                                                               data_type_scale_cnt
        , NOT attnotnull                                                     is_nullable_flg
        , pg_catalog.pg_get_expr(ad.adbin, ad.adrelid)                       default_value_txt
        , d.description                                                      attribute_desc
        , (SELECT distribution_no + 1
             FROM (SELECT GENERATE_SUBSCRIPTS(dp.distkey, 1) distribution_no
                        , UNNEST(dp.distkey)                 distkey) dk
            WHERE dk.distkey = a.attnum)                                     distribution_no
     FROM pg_catalog.pg_class               c
     JOIN pg_catalog.pg_namespace           ns
       ON ns.oid = c.relnamespace
      AND ns.nspname IN ('idl')
      AND c.relkind IN ('r', 'v')
     JOIN pg_catalog.pg_attribute           a
       ON a.attrelid = c.oid
      AND a.attnum > 0
      AND a.attisdropped IS FALSE
     JOIN pg_catalog.pg_type                t
       ON t.oid = a.atttypid
LEFT JOIN pg_catalog.pg_attrdef             ad
       ON ad.adrelid = c.oid
      AND ad.adnum = a.attnum
LEFT JOIN pg_catalog.pg_description         d
       ON d.objoid = c.oid
      AND d.objsubid = a.attnum
LEFT JOIN pg_catalog.gp_distribution_policy dp
       ON dp.localoid = c.oid
      AND a.attnum = ANY(dp.distkey)
    WHERE NOT EXISTS (SELECT NULL
                        FROM pg_catalog.pg_inherits i
                       WHERE i.inhrelid = c.oid)
;


-- stg.bridge_physical_key_attribute
SELECT table_name || '_dk'   key_name
     , schema_name           schema_name
     , physical_table_rk     physical_table_rk
     , physical_attribute_rk physical_attribute_rk
     , table_name            table_name
     , attribute_name        attribute_name
     , distribution_no       attribute_no
  FROM metamodel.bridge_physical_attribute
 WHERE distribution_no IS NOT NULL
;


-- metamodel.bridge_physical_key
     SELECT
DISTINCT ON (key_name
           , schema_name)
            -1                physical_key_rk -- sequence
          , key_name          key_name
          , schema_name       schema_name
          , physical_table_rk physical_table_rk
          , table_name        table_name
       FROM stg.bridge_physical_key_attribute
   ORDER BY key_name
          , schema_name
;


-- metamodel.link_physical_key_attribute
SELECT k.physical_key_rk        physical_key_rk
     , ka.physical_attribute_rk physical_attribute_rk
     , ka.schema_name           schema_name
     , ka.key_name              key_name
     , ka.table_name            table_name
     , ka.attribute_name        attribute_name
     , ka.attribute_no          attribute_no
  FROM stg.bridge_physical_key_attribute ka
  JOIN metamodel.bridge_physical_key     k
    ON k.key_name    = ka.key_name
   AND k.schema_name = ka.schema_name
;


-- metamodel.bridge_table
           SELECT -1                                                  table_rk    -- sequence
                , COALESCE(d.domain_rk, -1)                           domain_rk
                , COALESCE(dl.data_layer_rk, -1)                      data_layer_rk
                , COALESCE(s.source_rk, -1)                           source_rk
                , pt.schema_name                                      schema_name
                , pt.table_name                                       table_name
                , COALESCE(lt.table_desc, pt.table_desc)              table_desc
                , COALESCE(tt.table_type_rk, -1)                      table_type_rk
                , COALESCE(tdt.table_distribution_type_rk, -1)        table_distribution_type_rk
                , COALESCE(lf.is_dict_flg,   nf.is_dict_flg,   FALSE) is_dict_flg
                , COALESCE(lf.is_map_flg,    nf.is_map_flg,    FALSE) is_map_flg
                , COALESCE(lf.is_hub_flg,    nf.is_hub_flg,    FALSE) is_hub_flg
                , COALESCE(lf.is_sal_flg,    nf.is_sal_flg,    FALSE) is_sal_flg
                , COALESCE(lf.is_mart_flg,   nf.is_mart_flg,   FALSE) is_mart_flg
                , COALESCE(lf.is_bridge_flg, nf.is_bridge_flg, FALSE) is_bridge_flg
                , COALESCE(lf.is_link_flg,   nf.is_link_flg,   FALSE) is_link_flg
                , FALSE                                               is_temporal_flg
                , COALESCE(nf.is_accessor_flg, FALSE)                 is_accessor_flg
                , COALESCE(scdt.version_scd_type_rk, 1)               version_scd_type_rk
                , COALESCE(scdt.history_scd_type_rk, 1)               history_scd_type_rk
                , lt.model_major_version_from_num                     model_major_version_from_num
                , lt.model_minor_version_from_num                     model_minor_version_from_num
                , lt.model_major_version_to_num                       model_major_version_to_num
                , lt.model_minor_version_to_num                       model_minor_version_to_num
                , COALESCE(lt.is_deprecated_flg, FALSE)               is_deprecated_flg
             FROM metamodel.bridge_physical_table                                   pt
        LEFT JOIN metamodel.link_table_schema_x_data_layer                          dl
               ON dl.schema_name = pt.schema_name
        LEFT JOIN metamodel.link_physical_table_type                                tt
               ON tt.physical_table_type_cd = pt.table_type_cd
        LEFT JOIN metamodel.link_physical_table_distribution_type                   tdt
               ON tdt.physical_table_distribution_type_cd = pt.distribution_type_cd
LEFT JOIN LATERAL (SELECT priority_no
                        , is_dict_flg
                        , is_map_flg
                        , is_hub_flg
                        , is_sal_flg
                        , is_mart_flg
                        , is_bridge_flg
                        , is_link_flg
                        , is_accessor_flg
                     FROM metamodel.link_table_name_x_flg
                    WHERE is_table_prefix_flg IS TRUE
                      AND pt.table_name ^@ table_pattern_cd
                      AND data_layer_rk = dl.data_layer_rk
                    UNION ALL
                   SELECT priority_no
                        , is_dict_flg
                        , is_map_flg
                        , is_hub_flg
                        , is_sal_flg
                        , is_mart_flg
                        , is_bridge_flg
                        , is_link_flg
                        , is_accessor_flg
                     FROM metamodel.link_table_name_x_flg
                    WHERE is_table_prefix_flg IS FALSE
                      AND pt.table_name ~~ table_pattern_cd
                      AND data_layer_rk =  dl.data_layer_rk
                 ORDER BY priority_no
                    LIMIT 1)                                                        nf
               ON TRUE
        LEFT JOIN metamodel.bridge_logical_table                                    lt
               ON lt.table_name  = pt.table_name
              AND lt.schema_name = pt.schema_name
        LEFT JOIN metamodel.bridge_logical_domain                                   ld
               ON ld.logical_domain_rk = lt.logical_domain_rk
        LEFT JOIN dict.dict_domain                                                  d
               ON d.domain_name = ld.domain_name
        LEFT JOIN metamodel.bridge_source                                           s
               ON s.source_cd = COALESCE(dl.source_cd, lt.source_cd)
        LEFT JOIN metamodel.link_table_data_vault_type_x_flg                        lf
               ON lf.table_data_vault_type_cd = lt.table_data_vault_type_cd
              AND lf.data_layer_rk            = dl.data_layer_rk
LEFT JOIN LATERAL (SELECT MAX(version_scd_type_rk) version_scd_type_rk
                        , MAX(history_scd_type_rk) history_scd_type_rk
                     FROM metamodel.bridge_physical_attribute      pa
                     JOIN metamodel.link_attribute_name_x_scd_type scdt
                       ON scdt.attribute_name  = pa.attribute_name
                      AND pa.physical_table_rk = pt.physical_table_rk)              scdt
               ON TRUE
;


-- metamodel.bridge_attribute
   SELECT -1                                             attribute_rk    -- sequence
        , t.table_rk                                     table_rk
        , pa.attribute_name                              attribute_name
        , pa.attribute_no                                attribute_no
        , COALESCE(la.attribute_desc, pa.attribute_desc) attribute_desc
        , COALESCE(dt.data_type_rk, -1)                  data_type_rk
        , pa.data_type_size_cnt                          data_type_size_cnt
        , pa.data_type_scale_cnt                         data_type_scale_cnt
        , pa.is_nullable_flg                             is_nullable_flg
        , pa.default_value_txt                           default_value_txt
        , COALESCE(ldd.data_domain_rk
                 , mdt.data_domain_rk
                 , pdd.data_domain_rk
                 , -1)                                   data_domain_rk
        , COALESCE(mdt.metadata_type_rk, -1)             metadata_type_rk
        , la.model_major_version_from_num                model_major_version_from_num
        , la.model_minor_version_from_num                model_minor_version_from_num
        , la.model_major_version_to_num                  model_major_version_to_num
        , la.model_minor_version_to_num                  model_minor_version_to_num
        , COALESCE(la.is_deprecated_flg, FALSE)          is_deprecated_flg
     FROM metamodel.bridge_physical_attribute            pa
     JOIN metamodel.bridge_table                         t
       ON t.table_name = pa.table_name
      AND t.schema_name = pa.schema_name
LEFT JOIN metamodel.link_physical_data_type              dt
       ON dt.physical_data_type_cd = pa.data_type_cd
LEFT JOIN metamodel.link_attribute_postfix_x_data_domain pdd
       ON pa.attribute_name ~~ pdd.attribute_postfix_cd
      AND dt.data_type_rk = pdd.data_type_rk
      AND dt.physical_data_type_cd IS NULL
LEFT JOIN metamodel.link_attribute_name_x_metadata_type  mdt
       ON mdt.attribute_name = pa.attribute_name
LEFT JOIN metamodel.bridge_logical_attribute             la
       ON la.table_name = pa.table_name
      AND la.attribute_name = pa.attribute_name
      AND la.schema_name = pa.schema_name
LEFT JOIN dict.dict_data_domain                          ldd
       ON ldd.data_domain_cd = la.data_domain_cd
;


-- metamodel.bridge_key
SELECT lk.logical_key_rk               key_rk
     , lk.key_name                     key_name
     , t.table_rk                      table_rk
     , lk.is_primary_flg               is_primary_flg
     , lk.is_foreign_flg               is_foreign_flg
     , FALSE                           is_unique_flg
     , FALSE                           is_hash_flg
     , FALSE                           is_distribution_flg
     , lk.model_major_version_from_num model_major_version_from_num
     , lk.model_minor_version_from_num model_minor_version_from_num
     , lk.model_major_version_to_num   model_major_version_to_num
     , lk.model_minor_version_to_num   model_minor_version_to_num
     , lk.is_deprecated_flg            is_deprecated_flg
     , lk.model_effective_date         model_effective_date
  FROM metamodel.bridge_logical_key   lk
  JOIN metamodel.bridge_table         t
    ON t.table_name  = lk.table_name
   AND t.schema_name = lk.schema_name
 UNION ALL
SELECT physical_key_rk              key_rk
     , key_name                     key_name
     , table_rk                     table_rk
     , FALSE                        is_primary_flg
     , FALSE                        is_foreign_flg
     , FALSE                        is_unique_flg
     , FALSE                        is_hash_flg
     , TRUE                         is_distribution_flg
     , model_major_version_from_num model_major_version_from_num
     , model_minor_version_from_num model_minor_version_from_num
     , model_major_version_to_num   model_major_version_to_num
     , model_minor_version_to_num   model_minor_version_to_num
     , is_deprecated_flg            is_deprecated_flg
     , model_effective_date         model_effective_date
   FROM (SELECT
    DISTINCT ON (pka.physical_key_rk)
                pka.physical_key_rk
              , pka.key_name
              , t.table_rk
              , a.model_major_version_from_num
              , a.model_minor_version_from_num
              , a.model_major_version_to_num
              , a.model_minor_version_to_num
              , a.is_deprecated_flg
              , a.model_effective_date
           FROM metamodel.link_physical_key_attribute  pka
           JOIN metamodel.bridge_table                 t
             ON t.table_name  = pka.table_name
            AND t.schema_name = pka.schema_name
           JOIN metamodel.bridge_attribute             a
             ON a.table_rk        = t.table_rk
            AND a.attribute_name  = pka.attribute_name
       ORDER BY pka.physical_key_rk
              , a.model_major_version_to_num DESC NULLS FIRST
              , a.model_minor_version_to_num DESC NULLS FIRST
              , a.model_effective_date       DESC) k
;


-- metamodel.link_key_attribute
   SELECT lka.logical_key_rk         key_rk
        , a.attribute_rk             attribute_rk
        , lka.attribute_no           attribute_no
        , lka.foreign_logical_key_rk foreign_key_rk
        , af.attribute_rk            foreign_attribute_rk
     FROM metamodel.link_logical_key_attribute          lka
     JOIN metamodel.bridge_table                        t
       ON t.table_name  = lka.table_name
      AND t.schema_name = lka.schema_name
     JOIN metamodel.bridge_attribute                    a
       ON a.table_rk       = t.table_rk
      AND a.attribute_name = lka.attribute_name
LEFT JOIN metamodel.bridge_table                        tf
       ON tf.table_name  = lka.foreign_table_name
      AND tf.schema_name = lka.foreign_schema_name
LEFT JOIN metamodel.bridge_attribute                    af
       ON af.table_rk       = tf.table_rk
      AND af.attribute_name = lka.foreign_attribute_name
    UNION ALL
   SELECT pka.physical_key_rk key_rk
        , a.attribute_rk      attribute_rk
        , pka.attribute_no    attribute_no
        , NULL                foreign_key_rk
        , NULL                foreign_attribute_rk
     FROM metamodel.link_physical_key_attribute          pka
     JOIN metamodel.bridge_table                         t
       ON t.table_name  = pka.table_name
      AND t.schema_name = pka.schema_name
     JOIN metamodel.bridge_attribute                     a
       ON a.table_rk       = t.table_rk
      AND a.attribute_name = pka.attribute_name
;
