import argparse
import json
import multiprocessing
import os
import time
from enum import Enum
from pathlib import Path
from typing import Any, Dict, Iterable, Literal, Optional, Set, TypedDict
from uuid import UUID

import requests
import structlog
import urllib3

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
structlog.configure(
    cache_logger_on_first_use=True,
)
logger = structlog.stdlib.get_logger()

ResourceType = Literal["ceh", "uni"]

WORK_DIR = Path(__file__).parent
OUTPUT_DIR = WORK_DIR / ".load_resources"
STATE_FILE = OUTPUT_DIR / "state.json"
ERRORS_FILE = OUTPUT_DIR / "errors.json"


class LoadState(TypedDict):
    tx_id: Optional[str]
    loaded_files: Set[str]


LoadErrors = dict[str, str]


class LoadErrorJson(TypedDict):
    file: str
    error: str


class ProcessingStatus(Enum):
    SUCCESS = "success"
    SKIPPED = "skipped"
    ERROR = "error"
    SERVER_ERROR = "server_error"


STATUS_TO_LOG_MESSAGE = {
    ProcessingStatus.SUCCESS: "file_loaded",
    ProcessingStatus.SKIPPED: "file_skipped",
    ProcessingStatus.ERROR: "file_skipped",
    ProcessingStatus.SERVER_ERROR: "server_error",
}


def process_json_file(
    session: requests.Session,
    api_base_url: str,
    tx_id: UUID,
    resource_type: ResourceType,
    file_path: Path,
    logger: structlog.BoundLogger = logger,
) -> tuple[ProcessingStatus, Optional[str]]:
    file_path_str = str(file_path)
    loop_log = logger.bind(
        file=file_path_str,
        resource_type=resource_type,
    )

    status = error_message = None
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            data = json.load(f)

        if "resource_cd" not in data:
            raise ValueError("resource_cd not found in the JSON data")

        resource_cd = data["resource_cd"]
        loop_log = loop_log.bind(resource_cd=resource_cd)

        create_resource_action_added(
            session,
            api_base_url,
            tx_id,
            resource_type,
            resource_cd,
            data,
        )

        status = ProcessingStatus.SUCCESS

    except requests.exceptions.HTTPError as http_err:
        error_message = (
            f"HTTP {http_err.response.status_code}: {http_err.response.text}"
        )

        if http_err.response.status_code == 409:
            status = ProcessingStatus.SKIPPED
            loop_log = loop_log.bind(reason="already_exists_in_transaction")
            return status, error_message

        if http_err.response.status_code >= 500:
            status = ProcessingStatus.SERVER_ERROR
            loop_log = loop_log.bind(
                http_code=http_err.response.status_code, error=str(http_err)
            )
            return status, error_message

        status = ProcessingStatus.ERROR
        loop_log = loop_log.bind(
            http_code=http_err.response.status_code,
            error=http_err.response.text,
        )

    except Exception as err:
        error_message = f"{err.__class__.__name__}: {str(err)}"
        status = ProcessingStatus.ERROR
        loop_log = loop_log.bind(
            error_type=err.__class__.__name__,
            error=str(err),
        )

    finally:
        call_logger = (
            loop_log.critical
            if status is ProcessingStatus.SERVER_ERROR
            else loop_log.error
            if status is ProcessingStatus.ERROR
            else loop_log.info
        )
        call_logger(event=STATUS_TO_LOG_MESSAGE.get(status, "KeyboardInterrupt"))

    return status, error_message


def process_json_file_worker(args):
    resource_type, file_path, api_base_url, tx_id = args

    session = requests.Session()
    session.verify = False

    worker_log = structlog.stdlib.get_logger().bind(tx_id=str(tx_id))

    status, error_message = process_json_file(
        session=session,
        api_base_url=api_base_url,
        tx_id=tx_id,
        resource_type=resource_type,
        file_path=file_path,
        logger=worker_log,
    )
    return status, error_message, file_path


def run_loading(
    ceh_dir: Path,
    uni_dir: Path,
    api_base_url: str,
    state: LoadState,
    errors: LoadErrors,
    parallel: bool = False,
) -> None:
    start_time = time.time()
    logger.info("Starting resource loading process")  # noqa

    session = requests.Session()
    session.verify = False

    tx_id = get_or_create_transaction(session, api_base_url, state)
    log = logger.bind(tx_id=str(tx_id))

    resource_files = skip_processed_files(
        file_paths=list_resource_json_files(ceh_dir, uni_dir),
        state=state,
    )
    if parallel:
        files_processed, files_loaded = _run_loading__multiprocessing(
            resource_files, api_base_url, tx_id, state, errors
        )
    else:
        files_processed, files_loaded = _run_loading__singlethreaded(
            resource_files, session, api_base_url, tx_id, state, errors, log
        )

    commit_transaction(session, api_base_url, tx_id)
    processing_time = time.time() - start_time
    minutes, seconds = divmod(int(processing_time), 60)
    log.info(
        "All files submitted for processing",
        files_processed=files_processed,
        files_loaded=files_loaded,
        processing_time=f"{minutes}m{seconds}s",
    )

    tx_status = get_transaction_status(session, api_base_url, tx_id)
    log.info(f"Current transaction status: {tx_status}")
    logger.info(f"Check status at {api_base_url}/resources/tx/{tx_id}")


def _run_loading__multiprocessing(
    resource_files: Iterable[tuple[ResourceType, Path]],
    api_base_url: str,
    tx_id: UUID,
    state: LoadState,
    errors: LoadErrors,
) -> tuple[int, int]:
    """
    FIXME: в текущей версии Ctrl+C не работает, приходится извне убивать родительский процесс
    """
    with multiprocessing.Pool() as pool:
        worker_args_iter = (
            (resource_type, file_path, api_base_url, tx_id)
            for resource_type, file_path in resource_files
        )
        files_processed = files_loaded = 0
        for status, error_message, file_path in pool.imap_unordered(
            process_json_file_worker, worker_args_iter, chunksize=100
        ):
            files_processed += 1
            if status is ProcessingStatus.SUCCESS:
                files_loaded += 1
                state["loaded_files"].add(str(file_path))
            elif status is ProcessingStatus.ERROR:
                errors[str(file_path)] = error_message
            elif status is ProcessingStatus.SKIPPED:
                continue
            elif status is ProcessingStatus.SERVER_ERROR:
                continue

    return files_processed, files_loaded


def _run_loading__singlethreaded(
    resource_files: Iterable[tuple[ResourceType, Path]],
    session: requests.Session,
    api_base_url: str,
    tx_id: UUID,
    state: LoadState,
    errors: LoadErrors,
    logger: structlog.BoundLogger,
) -> tuple[int, int]:
    files_processed = files_loaded = 0
    for resource_type, file_path in resource_files:
        status, error_message = process_json_file(
            session,
            api_base_url,
            tx_id,
            resource_type,
            file_path,
            logger,
        )
        files_processed += 1
        if status is ProcessingStatus.SUCCESS:
            files_loaded += 1
            state["loaded_files"].add(str(file_path))
        elif status is ProcessingStatus.ERROR:
            errors[str(file_path)] = error_message
        elif status is ProcessingStatus.SKIPPED:
            continue
        elif status is ProcessingStatus.SERVER_ERROR:
            continue

    return files_processed, files_loaded


def get_or_create_transaction(
    session: requests.Session,
    api_base_url: str,
    state: LoadState,
) -> UUID:
    tx_id = UUID(uid) if (uid := state.get("tx_id")) else None
    if not tx_id:
        tx_id = create_transaction(session, api_base_url)
        state["tx_id"] = str(tx_id)
        logger.info(f"Created new transaction with ID: {tx_id}")
    else:
        logger.info(f"Continuing with existing transaction ID: {tx_id}")
    return tx_id


def create_transaction(session: requests.Session, api_base_url: str) -> UUID:
    response = session.post(
        url=f"{api_base_url}/resources/tx",
        json={
            "timeout": 3600,
            "release_metadata": {
                "release_num": "1.0.0",
                "release_desc": "Resource loading from directories",
                "release_date": "2025-04-08",
                "effective_from_date": "2025-04-08",
                "release_module": "ETL-SCALE-core",
                "git_revision_hash": "script",
            },
        },
    )
    response.raise_for_status()
    return UUID(response.json())


def create_resource_action_added(
    session: requests.Session,
    api_base_url: str,
    tx_id: UUID,
    resource_type: ResourceType,
    resource_cd: str,
    definition: Dict[str, Any],
) -> None:
    response = session.post(
        url=f"{api_base_url}/resources/actions/added",
        json={
            "tx_uid": str(tx_id),
            "resource_cd": resource_cd,
            "resource_type": resource_type,
            "definition": definition,
        },
    )
    response.raise_for_status()


def commit_transaction(
    session: requests.Session,
    api_base_url: str,
    tx_id: UUID,
) -> None:
    response = session.post(
        url=f"{api_base_url}/resources/tx/{str(tx_id)}/commit",
        json={
            "mode": "snapshot",
        },
    )
    response.raise_for_status()


def get_transaction_status(
    session: requests.Session,
    api_base_url: str,
    tx_id: UUID,
) -> Optional[str]:
    response = session.get(
        url=f"{api_base_url}/resources/tx/{str(tx_id)}",
    )
    response.raise_for_status()
    payload = response.json()
    return payload.get("status")


def list_resource_json_files(
    ceh_dir: Path,
    uni_dir: Path,
) -> Iterable[tuple[ResourceType, Path]]:
    if ceh_dir:
        for file in ceh_dir.glob("**/*.json"):
            yield "ceh", file
    if uni_dir:
        for file in uni_dir.glob("**/*.json"):
            yield "uni", file


def skip_processed_files(
    file_paths: Iterable[tuple[ResourceType, Path]],
    state: LoadState,
) -> Iterable[tuple[ResourceType, Path]]:
    skip_count = 0

    def inner():
        nonlocal skip_count
        for resource_type, file_path in file_paths:
            if str(file_path) in state["loaded_files"]:
                skip_count += 1
            else:
                yield resource_type, file_path

    yield from inner()

    logger.info(f"Files skipped: {skip_count}")


def load_state() -> LoadState:
    state_data = None
    if STATE_FILE.exists():
        with open(STATE_FILE, "r", encoding="utf-8") as f:
            state_data = json.load(f)
    if not state_data:
        return {"tx_id": None, "loaded_files": set()}

    state_data["loaded_files"] = set(state_data.get("loaded_files") or ())
    return state_data


def save_state(state: LoadState) -> None:
    state_for_save = state.copy()
    state_for_save["loaded_files"] = list(state_for_save["loaded_files"])
    with open(STATE_FILE, "w", encoding="utf-8") as f:
        json.dump(state_for_save, f, indent=2)


def load_errors() -> LoadErrors:
    if not ERRORS_FILE.exists():
        return {}

    with open(ERRORS_FILE, "r", encoding="utf-8") as f:
        errors_list = json.load(f)
        return {e["file"]: e["error"] for e in errors_list}


def save_errors(errors: LoadErrors) -> None:
    errors_list = [{"file": file, "error": error} for file, error in errors.items()]
    with open(ERRORS_FILE, "w", encoding="utf-8") as f:
        json.dump(errors_list, f, indent=2)


def main():
    api_base_url = os.getenv(
        "CEH_METAMODEL_BASE_URL",
        "http://localhost:8000/v1",
    )

    parser = argparse.ArgumentParser(
        description="Load resources from JSON files to API"
    )
    parser.add_argument(
        "--ceh-dir",
        type=Path,
        help="Directory containing CEH resource JSON files",
    )
    parser.add_argument(
        "--uni-dir",
        type=Path,
        help="Directory containing UNI resource JSON files",
    )
    args = parser.parse_args()

    if not args.ceh_dir and not args.uni_dir:
        parser.error("At least one of --ceh-dir or --uni-dir must be specified")

    OUTPUT_DIR.mkdir(parents=True, exist_ok=True)
    state = load_state()
    errors = load_errors()
    try:
        run_loading(
            ceh_dir=args.ceh_dir,
            uni_dir=args.uni_dir,
            api_base_url=api_base_url,
            state=state,
            errors=errors,
            # parallel=True,
        )
    finally:
        save_state(state)
        save_errors(errors)


if __name__ == "__main__":
    main()
