from datetime import datetime
from typing import Any, Iterator, Mapping, Optional, Sequence, Tu<PERSON>

from sqlalchemy import text
from sqlalchemy.orm import Session
from structlog.stdlib import Bound<PERSON>ogger, get_logger

from metaloader_rest_api import ceh_db, etl
from metaloader_rest_api.common_model import LAST_DATE
from metaloader_rest_api.common_repository import (
    Record,
    StageBatchRepository,
)

_DEFAULT_LOGGER = get_logger(__name__)


LOAD_PAGE_SIZE_DEFAULT = 100


def load_physical_model(
    session: Session,
    version_id: int,
    effective_date: datetime,
    dsn: Mapping[str, Any],
    schemas: Sequence[str],
    table_id: str,
    page_size: int = LOAD_PAGE_SIZE_DEFAULT,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    action_log = log.bind(action="load_physical_model")
    action_log.info(
        "begin",
        version=version_id,
        dsn=ceh_db.hide_dsn_password(dsn),
        schemas=schemas,
        table_id=table_id,
        page_size=page_size,
    )
    table_stage, attribute_stage = load_physical_model_to_stage(
        session,
        table_id,
        dsn,
        schemas,
        page_size,
        log,
    )
    load_physical_model_from_stage(
        session,
        version_id,
        effective_date,
        schemas,
        table_stage,
        attribute_stage,
        table_id,
        log,
    )
    load_physical_keys(
        session,
        version_id,
        effective_date,
        schemas,
        table_id,
        log,
    )
    action_log.info("end")


def load_physical_model_from_stage(
    session: Session,
    version_id: int,
    effective_date: datetime,
    schemas: Sequence[str],
    table_stage: str,
    attribute_stage: str,
    table_id: str,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    action_log = log.bind(action="load_physical_model_from_stage")
    action_log.info("begin")
    load_physical_tables_from_stage(
        session,
        version_id,
        effective_date,
        schemas,
        table_stage,
        attribute_stage,
        table_id,
        log,
    )
    load_physical_attributes_from_stage(
        session,
        version_id,
        effective_date,
        schemas,
        attribute_stage,
        table_id,
        log,
    )
    action_log.info("end")


def load_physical_tables_from_stage(
    session: Session,
    version_id: int,
    effective_date: datetime,
    schemas: Sequence[str],
    stage: str,
    attribute_stage: str,
    table_id: str,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    log.info("begin", table=table_id)

    merge_stage = f"stg.bridge_physical_table_{table_id}"

    log.info("create stage")
    session.execute(text(f"DROP TABLE IF EXISTS {merge_stage}"))
    session.execute(
        text(f"""
            CREATE TABLE {merge_stage} (
                source_rk                  BIGINT
              , data_layer_rk              SMALLINT
              , schema_name                TEXT
              , table_name                 TEXT
              , table_desc                 TEXT
              , table_type_rk              SMALLINT
              , table_type_cd              CHAR
              , is_dict_flg                BOOL
              , is_map_flg                 BOOL
              , is_hub_flg                 BOOL
              , is_sal_flg                 BOOL
              , is_mart_flg                BOOL
              , is_bridge_flg              BOOL
              , is_link_flg                BOOL
              , is_accessor_flg            BOOL
              , table_distribution_type_rk SMALLINT
              , table_distribution_type_cd CHAR
              , version_scd_type_rk        SMALLINT
              , history_scd_type_rk        SMALLINT
            )
        """)
    )

    session.execute(
        statement=text(f"""
                  INSERT INTO {merge_stage} (source_rk
                                           , data_layer_rk
                                           , schema_name
                                           , table_name
                                           , table_desc
                                           , table_type_rk
                                           , table_type_cd
                                           , is_dict_flg
                                           , is_map_flg
                                           , is_hub_flg
                                           , is_sal_flg
                                           , is_mart_flg
                                           , is_bridge_flg
                                           , is_link_flg
                                           , is_accessor_flg
                                           , table_distribution_type_rk
                                           , table_distribution_type_cd
                                           , version_scd_type_rk
                                           , history_scd_type_rk)
                       SELECT COALESCE(s.source_rk, -1)                    source_rk
                            , COALESCE(dl.data_layer_rk, -1)               data_layer_rk 
                            , t.schema_name                                schema_name
                            , t.table_name                                 table_name
                            , t.table_desc                                 table_desc
                            , COALESCE(tt.table_type_rk, -1)               table_type_rk
                            , t.table_type_cd                              table_type_cd
                            , COALESCE(nf.is_dict_flg,     FALSE)          is_dict_flg
                            , COALESCE(nf.is_map_flg,      FALSE)          is_map_flg
                            , COALESCE(nf.is_hub_flg,      FALSE)          is_hub_flg
                            , COALESCE(nf.is_sal_flg,      FALSE)          is_sal_flg
                            , COALESCE(nf.is_mart_flg,     FALSE)          is_mart_flg
                            , COALESCE(nf.is_bridge_flg,   FALSE)          is_bridge_flg
                            , COALESCE(nf.is_link_flg,     FALSE)          is_link_flg
                            , COALESCE(nf.is_accessor_flg, FALSE)          is_accessor_flg
                            , COALESCE(tdt.table_distribution_type_rk, -1) table_distribution_type_rk
                            , t.table_distribution_type_cd                 table_distribution_type_cd
                            , COALESCE(scdt.version_scd_type_rk, 1)        version_scd_type_rk
                            , COALESCE(scdt.history_scd_type_rk, 1)        history_scd_type_rk
                         FROM {stage}                                                                t 
                    LEFT JOIN metamodel.link_table_schema_x_data_layer                               dl
                           ON dl.schema_name = t.schema_name
                    LEFT JOIN metamodel.bridge_source                                                s
                           ON s.source_cd = dl.source_cd
                          AND s.effective_to_dttm = :effective_to_dttm
                          AND s.deleted_flg IS FALSE
                    LEFT JOIN metamodel.link_physical_table_type                                     tt
                           ON tt.physical_table_type_cd = t.table_type_cd
                    LEFT JOIN metamodel.link_physical_table_distribution_type                        tdt
                           ON tdt.physical_table_distribution_type_cd = t.table_distribution_type_cd
            LEFT JOIN LATERAL (SELECT priority_no
                                    , is_dict_flg
                                    , is_map_flg
                                    , is_hub_flg
                                    , is_sal_flg
                                    , is_mart_flg
                                    , is_bridge_flg
                                    , is_link_flg
                                    , is_accessor_flg
                                 FROM metamodel.link_table_name_x_flg
                                WHERE is_table_prefix_flg IS TRUE
                                  AND t.table_name  ^@ table_pattern_cd
                                  AND data_layer_rk =  dl.data_layer_rk
                                UNION ALL
                               SELECT priority_no
                                    , is_dict_flg
                                    , is_map_flg
                                    , is_hub_flg
                                    , is_sal_flg
                                    , is_mart_flg
                                    , is_bridge_flg
                                    , is_link_flg
                                    , is_accessor_flg
                                 FROM metamodel.link_table_name_x_flg
                                WHERE is_table_prefix_flg IS FALSE
                                  AND t.table_name  ~~ table_pattern_cd
                                  AND data_layer_rk =  dl.data_layer_rk
                             ORDER BY priority_no
                                LIMIT 1)                                                             nf
                           ON TRUE
                    LEFT JOIN (SELECT a.table_name
                                    , a.schema_name
                                    , MAX(version_scd_type_rk) version_scd_type_rk
                                    , MAX(history_scd_type_rk) history_scd_type_rk
                                 FROM {attribute_stage}                        a
                                 JOIN metamodel.link_attribute_name_x_scd_type scdt
                                   ON scdt.attribute_name = a.attribute_name
                             GROUP BY a.table_name
                                    , a.schema_name)                                                 scdt
                           ON scdt.table_name  = t.table_name
                          AND scdt.schema_name = t.schema_name
        """),
        params={
            "effective_to_dttm": LAST_DATE,
        },
    )

    log.info("merge")
    etl.merge(
        session=session,
        version_id=version_id,
        effective_date=effective_date,
        table="metamodel.bridge_physical_table",
        stage=merge_stage,
        primary_key="physical_table_rk",
        keys=[
            "table_name",
            "schema_name",
        ],
        values=[
            "source_rk",
            "data_layer_rk",
            "table_desc",
            "table_type_rk",
            "table_type_cd",
            "is_dict_flg",
            "is_map_flg",
            "is_hub_flg",
            "is_sal_flg",
            "is_mart_flg",
            "is_bridge_flg",
            "is_link_flg",
            "is_accessor_flg",
            "table_distribution_type_rk",
            "table_distribution_type_cd",
            "version_scd_type_rk",
            "history_scd_type_rk",
        ],
        filter_expression="schema_name = ANY(:schema_names)",
        params={"schema_names": schemas},
        log=log,
    )

    log.info("end")


def load_physical_attributes_from_stage(
    session: Session,
    version_id: int,
    effective_date: datetime,
    schemas: Sequence[str],
    stage: str,
    table_id: str,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    log.info("begin", table=table_id)

    merge_stage = f"stg.bridge_physical_attribute_{table_id}"

    log.info("create stage")
    session.execute(text(f"DROP TABLE IF EXISTS {merge_stage}"))
    session.execute(
        text(f"""
            CREATE TABLE {merge_stage} (
                schema_name         TEXT
              , table_name          TEXT
              , physical_table_rk   BIGINT
              , attribute_name      TEXT
              , attribute_no        SMALLINT
              , attribute_desc      TEXT
              , data_type_id        SMALLINT
              , data_type_rk        SMALLINT
              , data_type_size_cnt  INT
              , data_type_scale_cnt INT
              , is_nullable_flg     BOOLEAN
              , default_value_txt   TEXT
              , distribution_no     SMALLINT
              , data_domain_rk	    SMALLINT
              , metadata_type_rk    SMALLINT
            )
        """)
    )

    session.execute(
        statement=text(f"""
            INSERT INTO {merge_stage} (schema_name
                                     , table_name
                                     , physical_table_rk
                                     , attribute_name
                                     , attribute_no
                                     , attribute_desc
                                     , data_type_id
                                     , data_type_rk
                                     , data_type_size_cnt
                                     , data_type_scale_cnt
                                     , is_nullable_flg
                                     , default_value_txt
                                     , distribution_no
                                     , data_domain_rk
                                     , metadata_type_rk)
                 SELECT a.schema_name                      schema_name
                      , a.table_name                       table_name
                      , t.physical_table_rk                physical_table_rk
                      , a.attribute_name                   attribute_name
                      , a.attribute_no                     attribute_no
                      , a.attribute_desc                   attribute_desc
                      , a.data_type_id                     data_type_id
                      , COALESCE(adt.data_type_rk, -1)     data_type_rk
                      , a.data_type_size_cnt               data_type_size_cnt
                      , a.data_type_scale_cnt              data_type_scale_cnt
                      , a.is_nullable_flg                  is_nullable_flg
                      , a.default_value_txt                default_value_txt
                      , a.distribution_no                  distribution_no
                      , COALESCE(mdt.data_domain_rk
                               , pdd.data_domain_rk
                               , -1)                       data_domain_rk
                      , COALESCE(mdt.metadata_type_rk, -1) metadata_type_rk
                   FROM {stage}                                        a
                   JOIN metamodel.bridge_physical_table                t
                     ON t.table_name  = a.table_name
                    AND t.schema_name = a.schema_name
                    AND t.effective_to_dttm = :effective_to_dttm
                    AND t.deleted_flg IS FALSE
              LEFT JOIN dict.link_alias_data_type                      adt
                     ON adt.alias_data_type_id = a.data_type_id
                    AND adt.data_type_source_cd = 'physical_dm'
              LEFT JOIN metamodel.link_attribute_postfix_x_data_domain pdd
                     ON a.attribute_name ~~ pdd.attribute_postfix_cd
                    AND adt.data_type_rk =  pdd.data_type_rk
              LEFT JOIN metamodel.link_attribute_name_x_metadata_type  mdt
                     ON mdt.attribute_name = a.attribute_name
        """),
        params={
            "effective_to_dttm": LAST_DATE,
        },
    )

    log.info("merge")
    etl.merge(
        session=session,
        version_id=version_id,
        effective_date=effective_date,
        table="metamodel.bridge_physical_attribute",
        stage=merge_stage,
        primary_key="physical_attribute_rk",
        keys=[
            "physical_table_rk",
            "attribute_name",
        ],
        values=[
            "attribute_no",
            "attribute_desc",
            "data_type_id",
            "data_type_rk",
            "data_type_size_cnt",
            "data_type_scale_cnt",
            "is_nullable_flg",
            "default_value_txt",
            "distribution_no",
            "data_domain_rk",
            "metadata_type_rk",
        ],
        others=[
            "schema_name",
            "table_name",
        ],
        filter_expression="schema_name = ANY(:schema_names)",
        params={"schema_names": schemas},
        log=log,
    )

    log.info("end")


class StagePhysicalTableRepository(StageBatchRepository[Record]):
    def __init__(
        self,
        session: Session,
        table_id: str,
        page_size: int = StageBatchRepository.PAGE_SIZE_DEFAULT,
    ):
        table = "mart_physical_table"
        super().__init__(
            session,
            table,
            table_id,
            self._create_statement,
            self._load_statement,
            page_size,
        )

    @staticmethod
    def _create_statement(table: str) -> str:
        return f"""
            CREATE TABLE {table} (
                schema_name                TEXT
              , table_name                 TEXT
              , table_type_cd              CHAR
              , table_desc                 TEXT
              , table_distribution_type_cd CHAR
            )
        """

    @staticmethod
    def _load_statement(table: str) -> str:
        return f"""
            INSERT INTO {table} (schema_name
                               , table_name
                               , table_type_cd
                               , table_desc
                               , table_distribution_type_cd)
            VALUES (:schema_name
                  , :table_name
                  , :table_type_cd
                  , :table_desc
                  , :table_distribution_type_cd)
        """


class StagePhysicalAttributeRepository(StageBatchRepository[Record]):
    def __init__(
        self,
        session: Session,
        table_id: str,
        page_size: int = StageBatchRepository.PAGE_SIZE_DEFAULT,
    ):
        table = "mart_physical_attribute"
        super().__init__(
            session,
            table,
            table_id,
            self._create_statement,
            self._load_statement,
            page_size,
        )

    @staticmethod
    def _create_statement(table: str) -> str:
        return f"""
            CREATE TABLE {table} (
                schema_name         TEXT
              , table_name          TEXT
              , attribute_name      TEXT
              , attribute_no        SMALLINT
              , data_type_id        SMALLINT
              , data_type_size_cnt  INT
              , data_type_scale_cnt INT
              , is_nullable_flg     BOOLEAN
              , default_value_txt   TEXT
              , attribute_desc      TEXT
              , distribution_no     SMALLINT
            )
        """

    @staticmethod
    def _load_statement(table: str) -> str:
        return f"""
            INSERT INTO {table} (schema_name
                               , table_name
                               , attribute_name
                               , attribute_no
                               , data_type_id
                               , data_type_size_cnt
                               , data_type_scale_cnt
                               , is_nullable_flg
                               , default_value_txt
                               , attribute_desc
                               , distribution_no)
            VALUES (:schema_name
                  , :table_name
                  , :attribute_name
                  , :attribute_no
                  , :data_type_id
                  , :data_type_size_cnt
                  , :data_type_scale_cnt
                  , :is_nullable_flg
                  , :default_value_txt
                  , :attribute_desc
                  , :distribution_no)
        """


def load_physical_model_to_stage(
    session: Session,
    table_id: str,
    dsn: Mapping[str, Any],
    schemas: Sequence[str],
    page_size: int = LOAD_PAGE_SIZE_DEFAULT,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> Tuple[str, str]:
    action_log = log.bind(action="load_physical_model_to_stage")
    action_log.info("begin")
    table_stage = load_physical_tables_to_stage(
        session,
        table_id,
        dsn,
        schemas,
        page_size,
        log,
    )
    attribute_stage = load_physical_attributes_to_stage(
        session,
        table_id,
        dsn,
        schemas,
        page_size,
        log,
    )
    action_log.info("end")
    return table_stage, attribute_stage


def load_physical_tables_to_stage(
    session: Session,
    table_id: str,
    dsn: Mapping[str, Any],
    schemas: Sequence[str],
    page_size: int = LOAD_PAGE_SIZE_DEFAULT,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> str:
    repository = StagePhysicalTableRepository(
        session=session,
        table_id=table_id,
        page_size=page_size,
    )
    data = load_tables_from_db(dsn, schemas, page_size, log)

    load_physical_to_stage(repository, data)

    return repository.table


def load_physical_attributes_to_stage(
    session: Session,
    table_id: str,
    dsn: Mapping[str, Any],
    schemas: Sequence[str],
    page_size: int = LOAD_PAGE_SIZE_DEFAULT,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> str:
    repository = StagePhysicalAttributeRepository(
        session=session,
        table_id=table_id,
        page_size=page_size,
    )
    data = load_attributes_from_db(dsn, schemas, page_size, log)

    load_physical_to_stage(repository, data)

    return repository.table


def load_physical_keys(
    session: Session,
    version_id: int,
    effective_date: datetime,
    schemas: Sequence[str],
    table_id: str,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    log = log.bind(action="load_physical_keys")
    log.info("begin")
    stage = load_physical_keys_attributes_to_stage(
        session,
        schemas,
        table_id,
        log,
    )
    load_physical_keys_from_stage(
        session,
        version_id,
        effective_date,
        schemas,
        stage,
        table_id,
        log,
    )
    load_physical_keys_attributes_from_stage(
        session,
        version_id,
        effective_date,
        schemas,
        stage,
        table_id,
        log,
    )
    log.info("end")


def load_physical_keys_attributes_to_stage(
    session: Session,
    schemas: Sequence[str],
    table_id: str,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> str:
    stage = f"stg.physical_keys_attributes_{table_id}"

    log = log.bind(
        action="load_physical_keys_attributes_to_stage",
        table="physical_keys_attributes",
    )
    log.info("begin", table_id=table_id)

    log.info("create stage")
    session.execute(text(f"DROP TABLE IF EXISTS {stage}"))
    session.execute(
        text(f"""
            CREATE TABLE {stage} (
                key_name              TEXT
              , schema_name	          TEXT
              , physical_table_rk     BIGINT
              , table_name            TEXT
              , physical_attribute_rk BIGINT
              , attribute_name        TEXT
              , attribute_no          SMALLINT
            )
        """)
    )

    session.execute(
        statement=text(f"""
            INSERT INTO {stage} (key_name
                               , schema_name
                               , physical_table_rk
                               , table_name
                               , physical_attribute_rk
                               , attribute_name
                               , attribute_no)
                 SELECT table_name || '_dk'   key_name
                      , schema_name           schema_name
                      , physical_table_rk     physical_table_rk
                      , table_name            table_name
                      , physical_attribute_rk physical_attribute_rk
                      , attribute_name        attribute_name
                      , distribution_no       attribute_no
                   FROM metamodel.bridge_physical_attribute
                  WHERE schema_name = ANY(:schema_names) 
                    AND distribution_no IS NOT NULL
                    AND effective_to_dttm = :effective_to_dttm
                    AND deleted_flg IS FALSE
        """),
        params={
            "effective_to_dttm": LAST_DATE,
            "schema_names": schemas,
        },
    )

    log.info("end")

    return stage


def load_physical_keys_from_stage(
    session: Session,
    version_id: int,
    effective_date: datetime,
    schemas: Sequence[str],
    stage: str,
    table_id: str,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    log = log.bind(
        action="load_physical_keys_from_stage",
        table="bridge_key",
        stage=stage,
    )
    log.info("begin", table_id=table_id)

    merge_stage = f"stg.bridge_physical_key_{table_id}"

    log.info("create stage")
    session.execute(text(f"DROP TABLE IF EXISTS {merge_stage}"))
    session.execute(
        text(f"""
            CREATE TABLE {merge_stage} (
                key_name          TEXT
              , schema_name       TEXT
              , physical_table_rk BIGINT
              , table_name        TEXT
            )
        """)
    )
    session.execute(
        statement=text(f"""
            INSERT INTO {merge_stage} (key_name
                                     , schema_name
                                     , physical_table_rk
                                     , table_name)
                 SELECT
            DISTINCT ON (key_name
                       , schema_name)
                        key_name
                      , schema_name
                      , physical_table_rk
                      , table_name
                   FROM {stage}
               ORDER BY key_name
                      , schema_name
        """),
        params={
            "effective_to_dttm": LAST_DATE,
        },
    )

    log.info("merge")
    etl.merge(
        session=session,
        version_id=version_id,
        effective_date=effective_date,
        table="metamodel.bridge_physical_key",
        stage=merge_stage,
        primary_key="physical_key_rk",
        keys=[
            "key_name",
            "schema_name",
        ],
        values=[
            "physical_table_rk",
        ],
        others=[
            "table_name",
        ],
        filter_expression="schema_name = ANY(:schema_names)",
        params={"schema_names": schemas},
        log=log,
    )

    log.info("end")


def load_physical_keys_attributes_from_stage(
    session: Session,
    version_id: int,
    effective_date: datetime,
    schemas: Sequence[str],
    stage: str,
    table_id: str,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    log = log.bind(
        action="load_physical_keys_attributes_from_stage",
        table="link_physical_key_attribute",
        stage=stage,
    )
    log.info("begin", table=table_id)

    merge_stage = f"stg.link_physical_key_attribute_{table_id}"

    log.info("create stage")
    session.execute(text(f"DROP TABLE IF EXISTS {merge_stage}"))
    session.execute(
        text(f"""
            CREATE TABLE {merge_stage} (
                physical_key_rk       BIGINT
              , physical_attribute_rk BIGINT
              , schema_name           TEXT
              , key_name              TEXT
              , table_name            TEXT
              , attribute_name        TEXT
              , attribute_no          SMALLINT
            )
        """)
    )

    session.execute(
        statement=text(f"""
            INSERT INTO {merge_stage} (physical_key_rk      
                                     , physical_attribute_rk
                                     , schema_name          
                                     , key_name             
                                     , table_name           
                                     , attribute_name       
                                     , attribute_no)
                 SELECT k.physical_key_rk        physical_key_rk
                      , ka.physical_attribute_rk physical_attribute_rk
                      , ka.schema_name           schema_name
                      , ka.key_name              key_name
                      , ka.table_name            table_name
                      , ka.attribute_name        attribute_name
                      , ka.attribute_no          attribute_no
                   FROM {stage}                                  ka
                   JOIN metamodel.bridge_physical_key            k
                     ON k.key_name    = ka.key_name
                    AND k.schema_name = ka.schema_name
                    AND k.effective_to_dttm = :effective_to_dttm
                    AND k.deleted_flg IS FALSE
        """),
        params={
            "effective_to_dttm": LAST_DATE,
        },
    )

    log.info("merge")
    etl.merge(
        session=session,
        version_id=version_id,
        effective_date=effective_date,
        table="metamodel.link_physical_key_attribute",
        stage=merge_stage,
        primary_key=None,
        keys=[
            "physical_key_rk",
            "physical_attribute_rk",
        ],
        values=[
            "attribute_no",
        ],
        others=[
            "schema_name",
            "key_name",
            "table_name",
            "attribute_name",
        ],
        filter_expression="schema_name = ANY(:schema_names)",
        params={"schema_names": schemas},
        log=log,
    )

    log.info("end")


def load_physical_to_stage(
    repository: StageBatchRepository[Record],
    data: Iterator[Sequence[Mapping[str, Any]]],
) -> None:
    with repository:
        for rows in data:
            repository.load(batch=rows)


def load_tables_from_db(
    dsn: Mapping[str, Any],
    schemas: Sequence[str],
    page_size: int = LOAD_PAGE_SIZE_DEFAULT,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> Iterator[Sequence[Mapping[str, Any]]]:
    return load_from_db(
        dsn=dsn,
        action="load_tables_from_db",
        query="""
           SELECT ns.nspname    schema_name
                , c.relname     table_name
                , c.relkind     table_type_cd
                , d.description table_desc
                , dp.policytype table_distribution_type_cd
             FROM pg_catalog.pg_class c
             JOIN pg_catalog.pg_namespace ns
               ON ns.oid = c.relnamespace
              AND ns.nspname = ANY(%(schemas)s)
              AND c.relkind IN ('r', 'v')
        LEFT JOIN pg_catalog.pg_description d
               ON d.objoid = c.oid
              AND d.objsubid = 0
        LEFT JOIN pg_catalog.gp_distribution_policy dp
               ON dp.localoid = c.oid
            WHERE NOT EXISTS (SELECT NULL
                                FROM pg_catalog.pg_inherits i
                               WHERE i.inhrelid = c.oid)
        """,
        schemas=schemas,
        page_size=page_size,
        log=log,
    )


def load_attributes_from_db(
    dsn: Mapping[str, Any],
    schemas: Sequence[str],
    page_size: Optional[int] = LOAD_PAGE_SIZE_DEFAULT,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> Iterator[Sequence[Mapping[str, Any]]]:
    return load_from_db(
        dsn=dsn,
        action="load_attributes_from_db",
        query="""
           SELECT ns.nspname                                                         schema_name
                , c.relname                                                          table_name
                , a.attname                                                          attribute_name
                , a.attnum                                                           attribute_no
                , a.atttypid                                                         data_type_id
                , CASE
                  WHEN a.atttypid = 1042
                  THEN CASE
                       WHEN a.atttypmod != -1
                       THEN a.atttypmod - 4
                        END
                  WHEN a.atttypid = 1700
                  THEN CASE
                       WHEN a.atttypmod != -1
                       THEN ((a.atttypmod - 4) >> 16) & 65535
                        END
                  WHEN a.atttypid IN (1083, 1114, 1184, 1266)
                  THEN CASE
                       WHEN a.atttypmod < 0
                       THEN 6
                       ELSE a.atttypmod
                        END
                  WHEN a.atttypid = 1186
                   THEN CASE
                        WHEN a.atttypmod < 0
                          OR a.atttypmod & 65535 = 65535
                        THEN 6
                        ELSE a.atttypmod & 65535
                         END
                   END                                                               data_type_size_cnt
                , CASE a.atttypid
                  WHEN 1700
                  THEN CASE WHEN a.atttypmod != -1
                            THEN (a.atttypmod - 4) & 65535
                        END
                   END                                                               data_type_scale_cnt
                , NOT attnotnull                                                     is_nullable_flg
                , pg_catalog.pg_get_expr(ad.adbin, ad.adrelid)                       default_value_txt
                , d.description                                                      attribute_desc
                , (SELECT distribution_no + 1
                     FROM (SELECT GENERATE_SUBSCRIPTS(dp.distkey, 1) distribution_no
                                , UNNEST(dp.distkey)                 distkey) dk
                    WHERE dk.distkey = a.attnum)                                     distribution_no
             FROM pg_catalog.pg_class               c
             JOIN pg_catalog.pg_namespace           ns
               ON ns.oid = c.relnamespace
              AND ns.nspname = ANY(%(schemas)s)
              AND c.relkind IN ('r', 'v')
             JOIN pg_catalog.pg_attribute           a
               ON a.attrelid = c.oid
              AND a.attnum > 0
              AND a.attisdropped IS FALSE
             JOIN pg_catalog.pg_type                t
               ON t.oid = a.atttypid
        LEFT JOIN pg_catalog.pg_attrdef             ad
               ON ad.adrelid = c.oid
              AND ad.adnum = a.attnum
        LEFT JOIN pg_catalog.pg_description         d
               ON d.objoid = c.oid
              AND d.objsubid = a.attnum
        LEFT JOIN pg_catalog.gp_distribution_policy dp
               ON dp.localoid = c.oid
              AND a.attnum = ANY(dp.distkey)
            WHERE NOT EXISTS (SELECT NULL
                                FROM pg_catalog.pg_inherits i
                               WHERE i.inhrelid = c.oid)
        """,
        schemas=schemas,
        page_size=page_size,
        log=log,
    )


def load_from_db(
    dsn: Mapping[str, Any],
    action: str,
    query: str,
    schemas: Sequence[str],
    page_size: Optional[int] = LOAD_PAGE_SIZE_DEFAULT,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> Iterator[Sequence[Mapping[str, Any]]]:
    log = log.bind(action=action, schemas=schemas)
    return ceh_db.load(
        dsn=dsn,
        query=query,
        parameters={"schemas": schemas},
        fetch_size=page_size,
        log=log,
    )
