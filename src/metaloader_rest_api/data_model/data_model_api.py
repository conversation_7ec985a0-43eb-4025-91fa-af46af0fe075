from datetime import date, datetime
from typing import Any, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field
from sqlalchemy.orm import Session
from structlog.stdlib import BoundLogger, get_logger

from metaloader_rest_api.data_model import data_model_int

_DEFAULT_LOGGER = get_logger(__name__)


DATA_MODEL_SERVICE_DEFAULT = "prod1"
DATA_MODEL_MODULE_DEFAULT = "ADGP-core"
DATA_MODEL_RELEASE_DEFAULT = "999.999.999"
DATA_MODEL_PAGE_SIZE_DEFAULT = 100


class BaseDataModelParams(BaseModel):
    module: str = DATA_MODEL_MODULE_DEFAULT
    release: str = Field(
        pattern=r"\d+\.\d+\.\d+",
        default=DATA_MODEL_RELEASE_DEFAULT,
    )

    page_size: Optional[int] = DATA_MODEL_PAGE_SIZE_DEFAULT


class LoadDataModelParams(BaseDataModelParams):
    service: Optional[str] = DATA_MODEL_SERVICE_DEFAULT
    layers: Optional[List[str]] = None


def load_data_model(
    session: Session,
    load_id: UUID,
    effective_date: datetime,
    params: LoadDataModelParams,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    data_model_int.load_data_model(
        session=session,
        load_id=load_id,
        module=params.module,
        release=params.release,
        effective_date=effective_date,
        service=params.service,
        layers=params.layers,
        log=log,
    )


class LoadLogicalDataModelParams(LoadDataModelParams):
    layer: str
    columns: List[str]
    rows: List[List[Any]]
    as_of: date = Field(default_factory=date.today)


def load_logical_data_model(
    session: Session,
    load_id: UUID,
    effective_date: datetime,
    params: LoadLogicalDataModelParams,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    data_model_int.load_logical_data_model(
        session=session,
        load_id=load_id,
        module=params.module,
        version=params.release,
        effective_date=effective_date,
        layer=params.layer,
        columns=params.columns,
        rows=params.rows,
        as_of=params.as_of,
        log=log,
    )
