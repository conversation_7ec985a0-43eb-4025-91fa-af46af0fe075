from datetime import date, datetime
from typing import Any, Iterator, Mapping, Sequence, Tu<PERSON>

from sqlalchemy import text
from sqlalchemy.orm import Session
from structlog.stdlib import Bo<PERSON><PERSON>ogger, get_logger

from metaloader_rest_api import etl
from metaloader_rest_api.common_model import LAST_DATE
from metaloader_rest_api.common_repository import (
    BufferedStageBatchRepository,
    Record,
)

LOAD_PAGE_SIZE_DEFAULT = BufferedStageBatchRepository.PAGE_SIZE_DEFAULT

_DEFAULT_LOGGER = get_logger(__name__)


def load_logical_model(
    session: Session,
    version_id: int,
    effective_date: datetime,
    layer_id: int,
    schemas: Sequence[str],
    data: Iterator[Mapping[str, Any]],
    as_of: date,
    table_id: str,
    page_size: int = LOAD_PAGE_SIZE_DEFAULT,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    action_log = log.bind(action="load_logical_model")
    action_log.info(
        "begin",
        layer_id=layer_id,
        schemas=schemas,
        version_id=version_id,
        as_of=as_of,
        table_id=table_id,
    )
    domains_stage, tables_stage, attributes_stage = load_logical_model_to_stage(
        session,
        as_of,
        table_id,
        data,
        page_size,
        log,
    )
    load_logical_model_from_stage(
        session,
        version_id,
        effective_date,
        layer_id,
        schemas,
        as_of,
        domains_stage,
        tables_stage,
        attributes_stage,
        table_id,
        log,
    )
    load_logical_keys(
        session,
        version_id,
        effective_date,
        schemas,
        table_id,
        log,
    )
    action_log.info("end")


def load_logical_model_from_stage(
    session: Session,
    version_id: int,
    effective_date: datetime,
    layer_id: int,
    schemas: Sequence[str],
    as_of: date,
    domains_stage: str,
    tables_stage: str,
    attributes_stage: str,
    table_id: str,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    action_log = log.bind(action="load_logical_model_from_stage")
    action_log.info("begin")
    load_logical_domains_from_stage(
        session,
        version_id,
        effective_date,
        layer_id,
        domains_stage,
        table_id,
        log,
    )
    load_logical_tables_from_stage(
        session,
        version_id,
        effective_date,
        layer_id,
        schemas,
        tables_stage,
        attributes_stage,
        table_id,
        log,
    )
    load_logical_attributes_from_stage(
        session,
        version_id,
        effective_date,
        layer_id,
        schemas,
        attributes_stage,
        table_id,
        log,
    )
    action_log.info("end")


def load_logical_domains_from_stage(
    session: Session,
    version_id: int,
    effective_date: datetime,
    layer_id: int,
    stage: str,
    table_id: str,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    log = log.bind(
        action="load_logical_domains_from_stage",
        table="bridge_logical_domain",
        stage=stage,
    )
    log.info("begin", table_id=table_id)

    merge_stage = f"stg.bridge_logical_domain_{table_id}"

    log.info("create stage")
    session.execute(text(f"DROP TABLE IF EXISTS {merge_stage}"))
    session.execute(
        text(f"""
            CREATE TABLE {merge_stage} (
                domain_rk	            SMALLINT
              , domain_name	            TEXT
              , data_layer_rk           SMALLINT
              , model_major_version_num SMALLINT
              , model_minor_version_num SMALLINT
              , model_effective_date    DATE
            )
        """)
    )

    session.execute(
        statement=text(f"""
            INSERT INTO {merge_stage} (domain_rk
                                     , domain_name
                                     , data_layer_rk
                                     , model_major_version_num
                                     , model_minor_version_num
                                     , model_effective_date)
                 SELECT COALESCE(d.domain_rk, -1)  domain_rk
                      , ld.domain_name             domain_name
                      , :data_layer_rk             data_layer_rk
                      , ld.model_major_version_num model_major_version_num
                      , ld.model_minor_version_num model_minor_version_num
                      , ld.model_effective_date    model_effective_date
                   FROM {stage}          ld
              LEFT JOIN dict.dict_domain d
                     ON d.domain_name = ld.domain_name
        """),
        params={
            "data_layer_rk": layer_id,
        },
    )

    log.info("merge")
    etl.merge(
        session=session,
        version_id=version_id,
        effective_date=effective_date,
        table="metamodel.bridge_logical_domain",
        stage=merge_stage,
        primary_key="logical_domain_rk",
        keys=[
            "domain_name",
        ],
        values=[
            "domain_rk",
            "model_major_version_num",
            "model_minor_version_num",
        ],
        others=[
            "data_layer_rk",
            "model_effective_date",
        ],
        filter_expression="data_layer_rk = :data_layer_rk",
        params={"data_layer_rk": layer_id},
        log=log,
    )

    log.info("end")


def load_logical_tables_from_stage(
    session: Session,
    version_id: int,
    effective_date: datetime,
    layer_id: int,
    schemas: Sequence[str],
    stage: str,
    attribute_stage: str,
    table_id: str,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    log = log.bind(
        action="load_logical_tables_from_stage",
        table="bridge_logical_table",
        stage=stage,
    )
    log.info("begin", table_id=table_id)

    merge_stage = f"stg.bridge_logical_table_{table_id}"

    log.info("create stage")
    session.execute(text(f"DROP TABLE IF EXISTS {merge_stage}"))
    session.execute(
        text(f"""
            CREATE TABLE {merge_stage} (
                source_rk                    BIGINT
              , source_cd                    TEXT
              , data_layer_rk                BIGINT
              , schema_name	                 TEXT
              , table_name	                 TEXT
              , table_extra_name	         TEXT
              , table_desc	                 TEXT
              , table_extra_desc	         TEXT
              , is_dict_flg                  BOOL
              , is_map_flg                   BOOL
              , is_hub_flg                   BOOL
              , is_sal_flg                   BOOL
              , is_mart_flg                  BOOL
              , is_bridge_flg                BOOL
              , is_link_flg                  BOOL
              , table_data_vault_type_cd	 TEXT
              , version_scd_type_rk          SMALLINT
              , history_scd_type_rk          SMALLINT
              , attribute_cnt  	             SMALLINT
              , is_ref_flg	                 BOOL
              , logical_domain_rk	         BIGINT
              , domain_rk	                 SMALLINT
              , domain_name	                 TEXT
              , sub_domain_name	             TEXT
              , extra_sub_domain_name        TEXT
              , axon_id     	             TEXT
              , axon_url    	             TEXT
              , model_major_version_from_num SMALLINT
              , model_minor_version_from_num SMALLINT
              , model_major_version_to_num   SMALLINT
              , model_minor_version_to_num   SMALLINT
              , is_deprecated_flg            BOOL
              , model_effective_date	     DATE
            )
        """)
    )
    session.execute(
        statement=text(f"""
            INSERT INTO {merge_stage} (source_rk
                                     , source_cd
                                     , data_layer_rk
                                     , schema_name
                                     , table_name
                                     , table_extra_name
                                     , table_desc
                                     , table_extra_desc
                                     , is_dict_flg
                                     , is_map_flg
                                     , is_hub_flg
                                     , is_sal_flg
                                     , is_mart_flg
                                     , is_bridge_flg
                                     , is_link_flg
                                     , table_data_vault_type_cd
                                     , version_scd_type_rk
                                     , history_scd_type_rk
                                     , attribute_cnt
                                     , is_ref_flg
                                     , logical_domain_rk
                                     , domain_rk
                                     , domain_name
                                     , sub_domain_name
                                     , extra_sub_domain_name
                                     , axon_id
                                     , axon_url
                                     , model_major_version_from_num
                                     , model_minor_version_from_num
                                     , model_major_version_to_num
                                     , model_minor_version_to_num
                                     , is_deprecated_flg
                                     , model_effective_date)
                 SELECT COALESCE(s.source_rk, -1)             source_rk
                      , t.source_cd                           source_cd
                      , :data_layer_rk                        data_layer_rk
                      , t.schema_name                         schema_name
                      , t.table_name                          table_name
                      , t.table_extra_name                    table_extra_name
                      , t.table_desc                          table_desc
                      , t.table_extra_desc                    table_extra_desc
                      , COALESCE(dvtf.is_dict_flg,   FALSE)   is_dict_flg
                      , COALESCE(dvtf.is_map_flg,    FALSE)   is_map_flg
                      , COALESCE(dvtf.is_hub_flg,    FALSE)   is_hub_flg
                      , COALESCE(dvtf.is_sal_flg,    FALSE)   is_sal_flg
                      , COALESCE(dvtf.is_mart_flg,   FALSE)   is_mart_flg
                      , COALESCE(dvtf.is_bridge_flg, FALSE)   is_bridge_flg
                      , COALESCE(dvtf.is_link_flg,   FALSE)   is_link_flg
                      , t.table_data_vault_type_cd            table_data_vault_type_cd
                      , COALESCE(scdt.version_scd_type_rk, 1) version_scd_type_rk
                      , COALESCE(scdt.history_scd_type_rk, 1) history_scd_type_rk
                      , t.attribute_cnt                       attribute_cnt
                      , t.is_ref_flg                          is_ref_flg
                      , d.logical_domain_rk                   logical_domain_rk
                      , d.domain_rk                           domain_rk
                      , t.domain_name                         domain_name
                      , t.sub_domain_name                     sub_domain_name
                      , t.extra_sub_domain_name               extra_sub_domain_name
                      , t.axon_id                             axon_id
                      , t.axon_url                            axon_url
                      , t.model_major_version_from_num        model_major_version_from_num
                      , t.model_minor_version_from_num        model_minor_version_from_num
                      , t.model_major_version_to_num          model_major_version_to_num
                      , t.model_minor_version_to_num          model_minor_version_to_num
                      , t.is_deprecated_flg                   is_deprecated_flg
                      , t.model_effective_date                model_effective_date
                  FROM {stage}                                                    t
                  JOIN metamodel.bridge_logical_domain                            d
                    ON d.domain_name = t.domain_name
                   AND d.data_layer_rk = :data_layer_rk
                   AND d.effective_to_dttm = :effective_to_dttm
                   AND d.deleted_flg IS FALSE
             LEFT JOIN metamodel.bridge_source                                    s
                    ON s.source_cd = t.source_cd
                   AND s.effective_to_dttm = :effective_to_dttm
                   AND s.deleted_flg IS FALSE
             LEFT JOIN metamodel.link_table_data_vault_type_x_flg                 dvtf
                    ON dvtf.table_data_vault_type_cd = t.table_data_vault_type_cd
                   AND dvtf.data_layer_rk = :data_layer_rk
             LEFT JOIN (SELECT a.table_name
                             , a.schema_name
                             , MAX(version_scd_type_rk) version_scd_type_rk
                             , MAX(history_scd_type_rk) history_scd_type_rk
                          FROM {attribute_stage}                      a
                          JOIN metamodel.link_data_domain_x_scd_type  scdt
                            ON LOWER(scdt.data_domain_cd) = a.data_domain_cd
                           AND a.is_deprecated_flg IS FALSE
                      GROUP BY a.table_name
                             , a.schema_name)                                     scdt
                    ON scdt.table_name  = t.table_name
                   AND scdt.schema_name = t.schema_name
        """),
        params={
            "effective_to_dttm": LAST_DATE,
            "data_layer_rk": layer_id,
        },
    )

    log.info("merge")
    etl.merge(
        session=session,
        version_id=version_id,
        effective_date=effective_date,
        table="metamodel.bridge_logical_table",
        stage=merge_stage,
        primary_key="logical_table_rk",
        keys=[
            "table_name",
            "schema_name",
        ],
        values=[
            "source_rk",
            "source_cd",
            "data_layer_rk",
            "table_extra_name",
            "table_desc",
            "table_extra_desc",
            "is_dict_flg",
            "is_map_flg",
            "is_hub_flg",
            "is_sal_flg",
            "is_mart_flg",
            "is_bridge_flg",
            "is_link_flg",
            "table_data_vault_type_cd",
            "version_scd_type_rk",
            "history_scd_type_rk",
            "attribute_cnt",
            "is_ref_flg",
            "logical_domain_rk",
            "domain_rk",
            "domain_name",
            "sub_domain_name",
            "extra_sub_domain_name",
            "axon_id",
            "axon_url",
            "model_major_version_from_num",
            "model_minor_version_from_num",
            "model_major_version_to_num",
            "model_minor_version_to_num",
            "is_deprecated_flg",
        ],
        others=[
            "model_effective_date",
        ],
        filter_expression="schema_name = ANY(:schema_names)",
        params={"schema_names": schemas},
        log=log,
    )

    log.info("end")


def load_logical_attributes_from_stage(
    session: Session,
    version_id: int,
    effective_date: datetime,
    layer_id: int,
    schemas: Sequence[str],
    stage: str,
    table_id: str,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    log = log.bind(
        action="load_logical_attributes_from_stage",
        table="bridge_logical_attribute",
        stage=stage,
    )
    log.info("begin", table_id=table_id)

    merge_stage = f"stg.bridge_logical_attribute_{table_id}"

    log.info("create stage")
    session.execute(text(f"DROP TABLE IF EXISTS {merge_stage}"))
    session.execute(
        text(f"""
            CREATE TABLE {merge_stage} (
                record_no                    BIGINT
              , schema_name	                 TEXT              
              , logical_table_rk             BIGINT
              , table_name	                 TEXT
              , attribute_name	             TEXT
              , attribute_extra_name	     TEXT
              , data_type_rk	             SMALLINT
              , data_type_cd	             TEXT
              , data_type_size_cnt           SMALLINT
              , data_type_scale_cnt          SMALLINT
              , is_nullable_flg	             BOOLEAN
              , data_domain_rk	             SMALLINT
              , data_domain_cd	             TEXT
              , is_metadata_flg	             BOOLEAN
              , is_primary_key_flg	         BOOLEAN
              , is_foreign_key_flg	         BOOLEAN
              , foreign_schema_name          TEXT
              , foreign_table_name           TEXT
              , foreign_attribute_name       TEXT
              , attribute_desc	             TEXT
              , attribute_extra_desc	     TEXT
              , axon_id	                     TEXT
              , axon_url	                 TEXT
              , model_major_version_from_num SMALLINT
              , model_minor_version_from_num SMALLINT
              , model_major_version_to_num   SMALLINT
              , model_minor_version_to_num   SMALLINT
              , is_deprecated_flg            BOOL
              , model_effective_date	     DATE
            )
        """)
    )

    session.execute(
        statement=text(f"""
            INSERT INTO {merge_stage} (record_no                   
                                     , schema_name
                                     , logical_table_rk
                                     , table_name
                                     , attribute_name
                                     , attribute_extra_name
                                     , data_type_rk
                                     , data_type_cd
                                     , data_type_size_cnt
                                     , data_type_scale_cnt
                                     , is_nullable_flg
                                     , data_domain_rk
                                     , data_domain_cd
                                     , is_metadata_flg
                                     , is_primary_key_flg
                                     , is_foreign_key_flg
                                     , foreign_schema_name
                                     , foreign_table_name
                                     , foreign_attribute_name
                                     , attribute_desc
                                     , attribute_extra_desc
                                     , axon_id
                                     , axon_url
                                     , model_major_version_from_num
                                     , model_minor_version_from_num
                                     , model_major_version_to_num
                                     , model_minor_version_to_num
                                     , is_deprecated_flg
                                     , model_effective_date)
                 SELECT a.record_no                                                    record_no
                      , a.schema_name                                                  schema_name
                      , t.logical_table_rk                                             logical_table_rk
                      , a.table_name                                                   table_name
                      , a.attribute_name                                               attribute_name
                      , a.attribute_extra_name                                         attribute_extra_name
                      , COALESCE(adt.data_type_rk, -1)                                 data_type_rk
                      , a.data_type_cd                                                 data_type_cd
                      , a.data_type_size_cnt                                           data_type_size_cnt
                      , a.data_type_scale_cnt                                          data_type_scale_cnt
                      , a.is_nullable_flg                                              is_nullable_flg
                      , COALESCE(dd.data_domain_rk, -1)                                data_domain_rk
                      , a.data_domain_cd                                               data_domain_cd
                      , a.is_metadata_flg                                              is_metadata_flg
                      , a.is_primary_key_flg                                           is_primary_key_flg
                      , a.is_foreign_key_flg                                           is_foreign_key_flg
                      , a.foreign_schema_name                                          foreign_schema_name
                      , a.foreign_table_name                                           foreign_table_name
                      , a.foreign_attribute_name                                       foreign_attribute_name
                      , a.attribute_desc                                               attribute_desc
                      , a.attribute_extra_desc                                         attribute_extra_desc
                      , a.axon_id                                                      axon_id
                      , a.axon_url                                                     axon_url
                      , a.model_major_version_from_num                                 model_major_version_from_num
                      , a.model_minor_version_from_num                                 model_minor_version_from_num
                      , a.model_major_version_to_num                                   model_major_version_to_num
                      , a.model_minor_version_to_num                                   model_minor_version_to_num
                      , a.is_deprecated_flg                                            is_deprecated_flg
                      , a.model_effective_date                                         model_effective_date
                   FROM {stage}                                  a
                   JOIN metamodel.bridge_logical_table           t
                     ON t.table_name  = a.table_name
                    AND t.schema_name = a.schema_name
                    AND t.effective_to_dttm = :effective_to_dttm
                    AND t.deleted_flg IS FALSE
              LEFT JOIN dict.link_alias_data_type                adt
                     ON adt.alias_data_type_cd  = a.data_type_cd
                    AND adt.data_type_source_cd = 'logical_dm'
              LEFT JOIN dict.dict_data_domain                    dd
                     ON LOWER(dd.data_domain_cd) = a.data_domain_cd
        """),
        params={
            "effective_to_dttm": LAST_DATE,
        },
    )

    log.info("merge")
    etl.merge(
        session=session,
        version_id=version_id,
        effective_date=effective_date,
        table="metamodel.bridge_logical_attribute",
        stage=merge_stage,
        primary_key="logical_attribute_rk",
        keys=[
            "logical_table_rk",
            "attribute_name",
        ],
        values=[
            "record_no",
            "attribute_extra_name",
            "data_type_rk",
            "data_type_cd",
            "data_type_size_cnt",
            "data_type_scale_cnt",
            "is_nullable_flg",
            "data_domain_rk",
            "data_domain_cd",
            "is_metadata_flg",
            "is_primary_key_flg",
            "is_foreign_key_flg",
            "foreign_schema_name",
            "foreign_table_name",
            "foreign_attribute_name",
            "attribute_desc",
            "attribute_extra_desc",
            "axon_id",
            "axon_url",
            "model_major_version_from_num",
            "model_minor_version_from_num",
            "model_major_version_to_num",
            "model_minor_version_to_num",
            "is_deprecated_flg",
        ],
        others=[
            "schema_name",
            "table_name",
            "model_effective_date",
        ],
        filter_expression="schema_name = ANY(:schema_names)",
        params={"schema_names": schemas},
        log=log,
    )

    log.info("end")


def load_logical_keys(
    session: Session,
    version_id: int,
    effective_date: datetime,
    schemas: Sequence[str],
    table_id: str,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    log = log.bind(action="load_logical_keys")
    log.info("begin")
    stage = load_logical_keys_attributes_to_stage(
        session,
        schemas,
        table_id,
        log,
    )
    load_logical_keys_from_stage(
        session,
        version_id,
        effective_date,
        schemas,
        stage,
        table_id,
        log,
    )
    load_logical_keys_attributes_from_stage(
        session,
        version_id,
        effective_date,
        schemas,
        stage,
        table_id,
        log,
    )
    log.info("end")


def load_logical_keys_attributes_to_stage(
    session: Session,
    schemas: Sequence[str],
    table_id: str,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> str:
    stage = f"stg.logical_keys_attributes_{table_id}"

    log = log.bind(
        action="load_logical_keys_attributes_to_stage",
        table="logical_keys_attributes",
    )
    log.info("begin", table_id=table_id)

    log.info("create stage")
    session.execute(text(f"DROP TABLE IF EXISTS {stage}"))
    session.execute(
        text(f"""
            CREATE TABLE {stage} (
                key_name                     TEXT     NOT NULL
              , schema_name	                 TEXT     NOT NULL
              , logical_table_rk             BIGINT   NOT NULL
              , table_name                   TEXT     NOT NULL
              , logical_attribute_rk         BIGINT   NOT NULL
              , attribute_name               TEXT     NOT NULL
              , attribute_no                 SMALLINT NOT NULL
              , foreign_logical_table_rk     BIGINT
              , foreign_logical_attribute_rk BIGINT
              , foreign_schema_name          TEXT
              , foreign_table_name           TEXT
              , foreign_attribute_name       TEXT
              , is_primary_flg               BOOL     NOT NULL
              , is_foreign_flg               BOOL     NOT NULL
              , model_major_version_from_num SMALLINT
              , model_minor_version_from_num SMALLINT
              , model_major_version_to_num	 SMALLINT
              , model_minor_version_to_num	 SMALLINT
              , is_deprecated_flg            BOOL     NOT NULL
              , model_effective_date         DATE     NOT NULL
            )
        """)
    )

    session.execute(
        statement=text(f"""
            INSERT INTO {stage} (key_name
                               , schema_name
                               , logical_table_rk
                               , table_name
                               , logical_attribute_rk
                               , attribute_name
                               , attribute_no
                               , foreign_logical_table_rk
                               , foreign_logical_attribute_rk
                               , foreign_schema_name
                               , foreign_table_name
                               , foreign_attribute_name
                               , is_primary_flg
                               , is_foreign_flg
                               , model_major_version_from_num
                               , model_minor_version_from_num
                               , model_major_version_to_num
                               , model_minor_version_to_num
                               , is_deprecated_flg
                               , model_effective_date)
                 SELECT table_name || '_pk'                              key_name
                      , schema_name                                      schema_name
                      , logical_table_rk                                 logical_table_rk
                      , table_name                                       table_name
                      , logical_attribute_rk                             logical_attribute_rk
                      , attribute_name
                      , ROW_NUMBER() OVER (PARTITION BY logical_table_rk
                                               ORDER BY record_no)       attribute_no
                      , NULL                                             foreign_logical_table_rk
                      , NULL                                             foreign_logical_attribute_rk
                      , NULL                                             foreign_schema_name
                      , NULL                                             foreign_table_name
                      , NULL                                             foreign_attribute_name
                      , TRUE                                             is_primary_flg
                      , FALSE                                            is_foreign_flg
                      , model_major_version_from_num                     model_major_version_from_num
                      , model_minor_version_from_num                     model_minor_version_from_num
                      , model_major_version_to_num                       model_major_version_to_num
                      , model_minor_version_to_num                       model_minor_version_to_num
                      , is_deprecated_flg                                is_deprecated_flg
                      , model_effective_date                             model_effective_date
                   FROM metamodel.bridge_logical_attribute
                  WHERE is_primary_key_flg IS TRUE
                    AND schema_name = ANY(:schema_names)
                    AND effective_to_dttm = :effective_to_dttm
                    AND deleted_flg IS FALSE
                  UNION ALL
                 SELECT a.table_name || '_' || a.attribute_name || '_fk' key_name
                      , a.schema_name                                    schema_name
                      , a.logical_table_rk                               logical_table_rk
                      , a.table_name                                     table_name
                      , a.logical_attribute_rk                           logical_attribute_rk
                      , a.attribute_name                                 attribute_name
                      , 1                                                attribute_no
                      , af.logical_table_rk                              foreign_logical_table_rk
                      , af.logical_attribute_rk                          foreign_logical_attribute_rk
                      , a.foreign_schema_name                            foreign_schema_name
                      , a.foreign_table_name                             foreign_table_name
                      , a.foreign_attribute_name                         foreign_attribute_name
                      , FALSE                                            is_primary_flg
                      , TRUE                                             is_foreign_flg
                      , a.model_major_version_from_num                   model_major_version_from_num
                      , a.model_minor_version_from_num                   model_minor_version_from_num
                      , a.model_major_version_to_num                     model_major_version_to_num
                      , a.model_minor_version_to_num                     model_minor_version_to_num
                      , a.is_deprecated_flg                              is_deprecated_flg
                      , a.model_effective_date                           model_effective_date
                   FROM (SELECT *
                           FROM metamodel.bridge_logical_attribute
                          WHERE is_foreign_key_flg IS TRUE
                            AND schema_name = ANY(:schema_names)
                            AND effective_to_dttm = :effective_to_dttm
                            AND deleted_flg IS FALSE)                a
              LEFT JOIN metamodel.bridge_logical_attribute           af
                     ON af.table_name     = a.foreign_table_name
                    AND af.attribute_name = a.foreign_attribute_name
                    AND af.schema_name    = a.foreign_schema_name
                    AND af.effective_to_dttm = :effective_to_dttm
                    AND af.deleted_flg IS FALSE
        """),
        params={
            "effective_to_dttm": LAST_DATE,
            "schema_names": schemas,
        },
    )

    log.info("end")

    return stage


def load_logical_keys_from_stage(
    session: Session,
    version_id: int,
    effective_date: datetime,
    schemas: Sequence[str],
    stage: str,
    table_id: str,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    log = log.bind(
        action="load_logical_keys_from_stage",
        table="bridge_logical_key",
        stage=stage,
    )
    log.info("begin", table_id=table_id)

    merge_stage = f"stg.bridge_logical_key_{table_id}"

    log.info("create stage")
    session.execute(text(f"DROP TABLE IF EXISTS {merge_stage}"))
    session.execute(
        text(f"""
            CREATE TABLE {merge_stage} (
                key_name                     TEXT
              , schema_name                  TEXT
              , logical_table_rk             BIGINT
              , table_name                   TEXT
              , is_primary_flg               BOOL
              , is_foreign_flg               BOOL
              , model_major_version_from_num SMALLINT
              , model_minor_version_from_num SMALLINT
              , model_major_version_to_num   SMALLINT
              , model_minor_version_to_num   SMALLINT
              , is_deprecated_flg            BOOL
              , model_effective_date	     DATE
            )
        """)
    )
    session.execute(
        statement=text(f"""
            INSERT INTO {merge_stage} (key_name
                                     , schema_name
                                     , logical_table_rk
                                     , table_name
                                     , is_primary_flg
                                     , is_foreign_flg
                                     , model_major_version_from_num
                                     , model_minor_version_from_num
                                     , model_major_version_to_num
                                     , model_minor_version_to_num
                                     , is_deprecated_flg
                                     , model_effective_date)
                 SELECT
            DISTINCT ON (key_name
                       , schema_name)
                        key_name                     key_name
                      , schema_name                  schema_name
                      , logical_table_rk             logical_table_rk
                      , table_name                   table_name
                      , is_primary_flg               is_primary_flg
                      , is_foreign_flg               is_foreign_flg
                      , model_major_version_from_num model_major_version_from_num
                      , model_minor_version_from_num model_minor_version_from_num
                      , model_major_version_to_num   model_major_version_to_num
                      , model_minor_version_to_num   model_minor_version_to_num
                      , is_deprecated_flg            is_deprecated_flg
                      , model_effective_date         model_effective_date
                   FROM {stage}
               ORDER BY key_name
                      , schema_name
                      , model_major_version_to_num   DESC NULLS FIRST
                      , model_minor_version_to_num   DESC NULLS FIRST
                      , model_major_version_from_num      NULLS FIRST
                      , model_minor_version_from_num      NULLS FIRST                      
                      , model_effective_date         DESC
        """),
        params={
            "effective_to_dttm": LAST_DATE,
        },
    )

    session.commit()

    log.info("merge")
    etl.merge(
        session=session,
        version_id=version_id,
        effective_date=effective_date,
        table="metamodel.bridge_logical_key",
        stage=merge_stage,
        primary_key="logical_key_rk",
        keys=[
            "key_name",
            "schema_name",
        ],
        values=[
            "logical_table_rk",
            "is_primary_flg",
            "is_foreign_flg",
            "model_major_version_from_num",
            "model_minor_version_from_num",
            "model_major_version_to_num",
            "model_minor_version_to_num",
            "is_deprecated_flg",
        ],
        others=[
            "table_name",
            "model_effective_date",
        ],
        filter_expression="schema_name = ANY(:schema_names)",
        params={"schema_names": schemas},
        log=log,
    )

    log.info("end")


def load_logical_keys_attributes_from_stage(
    session: Session,
    version_id: int,
    effective_date: datetime,
    schemas: Sequence[str],
    stage: str,
    table_id: str,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    log = log.bind(
        action="load_logical_keys_attributes_from_stage",
        table="link_logical_key_attribute",
        stage=stage,
    )
    log.info("begin", table=table_id)

    merge_stage = f"stg.link_logical_key_attribute_{table_id}"

    log.info("create stage")
    session.execute(text(f"DROP TABLE IF EXISTS {merge_stage}"))
    session.execute(
        text(f"""
            CREATE TABLE {merge_stage} (
                logical_key_rk               BIGINT
              , logical_attribute_rk         BIGINT
              , schema_name                  TEXT
              , key_name                     TEXT
              , table_name                   TEXT
              , attribute_name               TEXT
              , attribute_no                 SMALLINT
              , foreign_logical_key_rk       BIGINT
              , foreign_key_name             TEXT
              , foreign_logical_attribute_rk BIGINT
              , foreign_schema_name          TEXT
              , foreign_table_name           TEXT
              , foreign_attribute_name       TEXT
            )
        """)
    )

    session.execute(
        statement=text(f"""
            INSERT INTO {merge_stage} (logical_key_rk
                                     , logical_attribute_rk
                                     , schema_name
                                     , key_name
                                     , table_name
                                     , attribute_name
                                     , attribute_no
                                     , foreign_logical_key_rk
                                     , foreign_key_name
                                     , foreign_logical_attribute_rk
                                     , foreign_schema_name
                                     , foreign_table_name
                                     , foreign_attribute_name)
                 SELECT k.logical_key_rk                logical_key_rk
                      , ka.logical_attribute_rk         logical_attribute_rk
                      , ka.schema_name                  schema_name
                      , ka.key_name                     key_name
                      , ka.table_name                   table_name
                      , ka.attribute_name               attribute_name
                      , ka.attribute_no                 attribute_no
                      , kf.logical_key_rk               foreign_logical_key_rk
                      , kf.key_name                     foreign_key_name
                      , ka.foreign_logical_attribute_rk foreign_logical_attribute_rk
                      , ka.foreign_schema_name          foreign_schema_name
                      , ka.foreign_table_name           foreign_table_name
                      , ka.foreign_attribute_name       foreign_attribute_name
                   FROM {stage}                                           ka
                   JOIN metamodel.bridge_logical_key                      k
                     ON k.key_name    = ka.key_name
                    AND k.schema_name = ka.schema_name
                    AND k.effective_to_dttm = :effective_to_dttm
                    AND k.deleted_flg IS FALSE
              LEFT JOIN metamodel.bridge_logical_key                      kf
                     ON kf.logical_table_rk = ka.foreign_logical_table_rk
                    AND kf.effective_to_dttm = :effective_to_dttm
                    AND kf.is_primary_flg IS TRUE
                    AND kf.deleted_flg IS FALSE
        """),
        params={
            "effective_to_dttm": LAST_DATE,
        },
    )

    log.info("merge")
    etl.merge(
        session=session,
        version_id=version_id,
        effective_date=effective_date,
        table="metamodel.link_logical_key_attribute",
        stage=merge_stage,
        primary_key=None,
        keys=[
            "logical_key_rk",
            "logical_attribute_rk",
        ],
        values=[
            "attribute_no",
            "foreign_logical_key_rk",
            "foreign_logical_attribute_rk",
        ],
        others=[
            "schema_name",
            "key_name",
            "table_name",
            "attribute_name",
            "foreign_key_name",
            "foreign_schema_name",
            "foreign_table_name",
            "foreign_attribute_name",
        ],
        filter_expression="schema_name = ANY(:schema_names)",
        params={"schema_names": schemas},
        log=log,
    )

    log.info("end")


class StageLogicalRepository(BufferedStageBatchRepository[Record]):
    def __init__(
        self,
        session: Session,
        table_id: str,
        page_size: int = BufferedStageBatchRepository.PAGE_SIZE_DEFAULT,
    ):
        table = "mart_logical_model"
        super().__init__(
            session,
            table,
            table_id,
            self._create_statement,
            self._load_statement,
            page_size,
        )

    @staticmethod
    def _create_statement(table: str) -> str:
        return f"""
            CREATE TABLE {table} (
                record_no                        BIGINT   NOT NULL
              , domain_name	                     TEXT     NOT NULL
              , domain_model_version_num	     TEXT     NOT NULL
              , schema_name	                     TEXT     NOT NULL
              , table_name	                     TEXT     NOT NULL
              , table_extra_name	             TEXT
              , table_data_vault_type_cd	     TEXT
              , table_desc	                     TEXT
              , table_extra_desc	             TEXT
              , attribute_cnt  	                 SMALLINT        
              , is_ref_flg	                     BOOL     NOT NULL
              , sub_domain_name	                 TEXT
              , extra_sub_domain_name            TEXT
              , table_axon_id     	             TEXT
              , table_axon_url    	             TEXT
              , table_model_version_from_num	 TEXT     NOT NULL
              , table_model_version_to_num       TEXT
              , attribute_name	                 TEXT     NOT NULL
              , attribute_extra_name	         TEXT
              , data_type_cd	                 TEXT
              , data_domain_cd	                 TEXT
              , is_nullable_flg	                 BOOLEAN  NOT NULL
              , is_metadata_flg	                 BOOLEAN  NOT NULL
              , is_primary_key_flg	             BOOLEAN  NOT NULL
              , is_foreign_key_flg	             BOOLEAN  NOT NULL
              , foreign_schema_name              TEXT
              , foreign_table_name               TEXT
              , foreign_attribute_name           TEXT
              , attribute_desc	                 TEXT
              , attribute_extra_desc	         TEXT
              , attribute_axon_id	             TEXT
              , attribute_axon_url	             TEXT
              , attribute_model_version_from_num TEXT     NOT NULL
              , attribute_model_version_to_num   TEXT
            )
        """

    @staticmethod
    def _load_statement(table: str) -> str:
        return f"""
            INSERT INTO {table} (record_no
                               , domain_name
                               , domain_model_version_num
                               , schema_name
                               , table_name
                               , table_extra_name
                               , table_data_vault_type_cd
                               , table_desc
                               , table_extra_desc
                               , attribute_cnt
                               , is_ref_flg
                               , sub_domain_name
                               , extra_sub_domain_name
                               , table_axon_id
                               , table_axon_url
                               , table_model_version_from_num
                               , table_model_version_to_num
                               , attribute_name
                               , attribute_extra_name
                               , data_type_cd
                               , data_domain_cd
                               , is_nullable_flg
                               , is_metadata_flg
                               , is_primary_key_flg
                               , is_foreign_key_flg
                               , foreign_schema_name
                               , foreign_table_name
                               , foreign_attribute_name
                               , attribute_desc
                               , attribute_extra_desc
                               , attribute_axon_id
                               , attribute_axon_url
                               , attribute_model_version_from_num
                               , attribute_model_version_to_num)
            VALUES (:record_no                         
                  , LOWER(:domain_name)	                      
                  , :domain_model_version_num	      
                  , LOWER(:schema_name)	                      
                  , LOWER(:table_name)	                      
                  , :table_extra_name	              
                  , LOWER(:table_data_vault_type_cd)	      
                  , :table_desc	                      
                  , :table_extra_desc	              
                  , :attribute_cnt  	                  
                  , :is_ref_flg	                      
                  , :sub_domain_name	                  
                  , :extra_sub_domain_name             
                  , :table_axon_id     	              
                  , :table_axon_url    	              
                  , :table_model_version_from_num	  
                  , :table_model_version_to_num        
                  , LOWER(:attribute_name)	                  
                  , :attribute_extra_name	          
                  , LOWER(:data_type_cd)	                  
                  , LOWER(:data_domain_cd)	                  
                  , :is_nullable_flg	                  
                  , :is_metadata_flg	                  
                  , :is_primary_key_flg	              
                  , :is_foreign_key_flg	              
                  , LOWER(:foreign_schema_name)               
                  , LOWER(:foreign_table_name)                
                  , LOWER(:foreign_attribute_name)            
                  , :attribute_desc	                  
                  , :attribute_extra_desc	          
                  , :attribute_axon_id	              
                  , :attribute_axon_url	              
                  , :attribute_model_version_from_num  
                  , :attribute_model_version_to_num)
        """


def load_logical_model_to_stage(
    session: Session,
    as_of: date,
    table_id: str,
    data: Iterator[Mapping[str, Any]],
    page_size: int = LOAD_PAGE_SIZE_DEFAULT,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> Tuple[str, str, str]:
    action_log = log.bind(action="load_logical_model_to_stage")
    action_log.info("begin")

    repository = StageLogicalRepository(
        session=session,
        table_id=table_id,
        page_size=page_size,
    )
    repository.load_all(data)
    stage = repository.table

    domains_stage = load_logical_domains_to_stage(
        session,
        as_of,
        stage,
        table_id,
        log,
    )

    tables_stage = load_logical_tables_to_stage(
        session,
        as_of,
        stage,
        domains_stage,
        table_id,
        log,
    )

    attributes_stage = load_logical_attributes_to_stage(
        session,
        as_of,
        stage,
        domains_stage,
        table_id,
        log,
    )

    action_log.info("end")

    return domains_stage, tables_stage, attributes_stage


def load_logical_domains_to_stage(
    session: Session,
    as_of: date,
    stage: str,
    table_id: str,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> str:
    log = log.bind(
        action="load_logical_domains_to_stage",
        table="mart_logical_domain",
        stage=stage,
    )
    log.info("begin", table_id=table_id)

    mart_stage = f"stg.mart_logical_domain_{table_id}"

    log.info("create stage")
    session.execute(text(f"DROP TABLE IF EXISTS {mart_stage}"))
    session.execute(
        text(f"""
            CREATE TABLE {mart_stage} (
                domain_name	            TEXT
              , model_major_version_num SMALLINT
              , model_minor_version_num SMALLINT
              , model_effective_date    DATE
            )
        """)
    )

    session.execute(
        statement=text(f"""
            INSERT INTO {mart_stage} (domain_name
                                    , model_major_version_num
                                    , model_minor_version_num
                                    , model_effective_date)
                 SELECT
            DISTINCT ON (domain_name)
                        domain_name                                                                     domain_name
                      , SPLIT_PART(domain_model_version_num, '.', 1)::SMALLINT                          model_major_version_num
                      , COALESCE(NULLIF(SPLIT_PART(domain_model_version_num, '.', 2), '')::SMALLINT, 0) model_minor_version_num
                      , :model_effective_date                                                           model_effective_date
                   FROM {stage}
               ORDER BY domain_name
                      , model_major_version_num DESC
                      , model_minor_version_num DESC
        """),
        params={
            "model_effective_date": as_of,
        },
    )

    log.info("end")

    return mart_stage


def load_logical_tables_to_stage(
    session: Session,
    as_of: date,
    stage: str,
    domains_stage: str,
    table_id: str,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> str:
    log = log.bind(
        action="load_logical_tables_to_stage",
        table="mart_logical_table",
        stage=stage,
    )
    log.info("begin", table_id=table_id)

    mart_stage = f"stg.mart_logical_table_{table_id}"

    log.info("create stage")
    session.execute(text(f"DROP TABLE IF EXISTS {mart_stage}"))
    session.execute(
        text(f"""
            CREATE TABLE {mart_stage} (
                source_cd                    TEXT
              , schema_name	                 TEXT
              , table_name	                 TEXT
              , table_extra_name	         TEXT 
              , table_data_vault_type_cd	 TEXT 
              , table_desc	                 TEXT 
              , table_extra_desc	         TEXT 
              , attribute_cnt  	             SMALLINT 
              , is_ref_flg	                 BOOL
              , domain_name	                 TEXT 
              , sub_domain_name	             TEXT 
              , extra_sub_domain_name        TEXT 
              , axon_id     	             TEXT 
              , axon_url    	             TEXT 
              , model_major_version_from_num SMALLINT 
              , model_minor_version_from_num SMALLINT 
              , model_major_version_to_num   SMALLINT 
              , model_minor_version_to_num   SMALLINT 
              , is_deprecated_flg            BOOL
              , model_effective_date	     DATE 
            )
        """)
    )
    session.execute(
        statement=text(f"""
            INSERT INTO {mart_stage} (source_cd
                                    , schema_name
                                    , table_name
                                    , table_extra_name
                                    , table_data_vault_type_cd
                                    , table_desc
                                    , table_extra_desc
                                    , attribute_cnt
                                    , is_ref_flg
                                    , domain_name
                                    , sub_domain_name
                                    , extra_sub_domain_name
                                    , axon_id
                                    , axon_url
                                    , model_major_version_from_num
                                    , model_minor_version_from_num
                                    , model_major_version_to_num
                                    , model_minor_version_to_num
                                    , is_deprecated_flg
                                    , model_effective_date)
                 SELECT 'DTPL'                                                         source_cd
                      , t.schema_name                                                  schema_name
                      , t.table_name                                                   table_name
                      , t.table_extra_name                                             table_extra_name
                      , t.table_data_vault_type_cd                                     table_data_vault_type_cd
                      , t.table_desc                                                   table_desc
                      , t.table_extra_desc                                             table_extra_desc
                      , t.attribute_cnt                                                attribute_cnt
                      , t.is_ref_flg                                                   is_ref_flg
                      , t.domain_name                                                  domain_name
                      , t.sub_domain_name                                              sub_domain_name
                      , t.extra_sub_domain_name                                        extra_sub_domain_name
                      , t.axon_id                                                      axon_id
                      , t.axon_url                                                     axon_url
                      , t.model_major_version_from_num                                 model_major_version_from_num
                      , t.model_minor_version_from_num                                 model_minor_version_from_num
                      , t.model_major_version_to_num                                   model_major_version_to_num
                      , t.model_minor_version_to_num                                   model_minor_version_to_num
                      , t.model_major_version_to_num IS NOT NULL
                    AND CASE
                        WHEN t.model_major_version_to_num  = d.model_major_version_num
                        THEN t.model_minor_version_to_num <= d.model_minor_version_num
                        ELSE t.model_major_version_to_num <  d.model_major_version_num
                         END                                                           is_deprecated_flg
                      , :model_effective_date                                          model_effective_date
                   FROM (SELECT
                    DISTINCT ON (table_name
                               , schema_name)
                                schema_name
                              , table_name
                              , table_extra_name
                              , table_data_vault_type_cd
                              , table_desc
                              , table_extra_desc
                              , attribute_cnt
                              , is_ref_flg
                              , domain_name
                              , sub_domain_name
                              , extra_sub_domain_name
                              , table_axon_id                                                                          axon_id
                              , table_axon_url                                                                         axon_url
                              , SPLIT_PART(table_model_version_from_num, '.', 1)::SMALLINT                             model_major_version_from_num
                              , COALESCE(NULLIF(SPLIT_PART(table_model_version_from_num, '.', 2), '')::SMALLINT, 0)    model_minor_version_from_num
                              , SPLIT_PART(table_model_version_to_num, '.', 1)::SMALLINT                               model_major_version_to_num
                              , CASE WHEN table_model_version_to_num IS NOT NULL
                                THEN COALESCE(NULLIF(SPLIT_PART(table_model_version_to_num, '.', 2), '')::SMALLINT, 0)
                                 END                                                                                   model_minor_version_to_num
                           FROM {stage}
                       ORDER BY table_name
                              , schema_name
                              , model_major_version_to_num DESC NULLS FIRST
                              , model_minor_version_to_num DESC NULLS FIRST) t
                   JOIN {domains_stage}                                       d
                     ON d.domain_name = t.domain_name
        """),
        params={
            "model_effective_date": as_of,
        },
    )

    log.info("end")

    return mart_stage


def load_logical_attributes_to_stage(
    session: Session,
    as_of: date,
    stage: str,
    domains_stage: str,
    table_id: str,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> str:
    log = log.bind(
        action="load_logical_attributes_to_stage",
        table="bridge_logical_attribute",
        stage=stage,
    )
    log.info("begin", table_id=table_id)

    mart_stage = f"stg.mart_logical_attribute_{table_id}"

    log.info("create stage")
    session.execute(text(f"DROP TABLE IF EXISTS {mart_stage}"))
    session.execute(
        text(f"""
            CREATE TABLE {mart_stage} ( 
                record_no                    BIGINT
              , schema_name	                 TEXT
              , table_name	                 TEXT
              , attribute_name	             TEXT
              , attribute_extra_name	     TEXT
              , data_type_cd	             TEXT
              , is_nullable_flg	             BOOLEAN
              , data_type_size_cnt           SMALLINT
              , data_type_scale_cnt          SMALLINT
              , data_domain_cd	             TEXT
              , is_metadata_flg	             BOOLEAN
              , is_primary_key_flg	         BOOLEAN
              , is_foreign_key_flg	         BOOLEAN
              , foreign_schema_name          TEXT
              , foreign_table_name           TEXT
              , foreign_attribute_name       TEXT
              , attribute_desc	             TEXT
              , attribute_extra_desc	     TEXT
              , axon_id	                     TEXT
              , axon_url	                 TEXT
              , model_major_version_from_num SMALLINT
              , model_minor_version_from_num SMALLINT
              , model_major_version_to_num   SMALLINT
              , model_minor_version_to_num   SMALLINT
              , is_deprecated_flg            BOOL
              , model_effective_date	     DATE
            )
        """)
    )

    session.execute(
        statement=text(f"""
            INSERT INTO {mart_stage} (record_no
                                    , schema_name
                                    , table_name
                                    , attribute_name
                                    , attribute_extra_name
                                    , data_type_cd
                                    , data_type_size_cnt
                                    , data_type_scale_cnt
                                    , is_nullable_flg
                                    , data_domain_cd
                                    , is_metadata_flg
                                    , is_primary_key_flg
                                    , is_foreign_key_flg
                                    , foreign_schema_name
                                    , foreign_table_name
                                    , foreign_attribute_name
                                    , attribute_desc
                                    , attribute_extra_desc
                                    , axon_id
                                    , axon_url
                                    , model_major_version_from_num
                                    , model_minor_version_from_num
                                    , model_major_version_to_num
                                    , model_minor_version_to_num
                                    , is_deprecated_flg
                                    , model_effective_date)
                 SELECT a.record_no                                                             record_no
                      , a.schema_name                                                           schema_name
                      , a.table_name                                                            table_name
                      , a.attribute_name                                                        attribute_name
                      , a.attribute_extra_name                                                  attribute_extra_name
                      , SPLIT_PART(a.data_type_cd, '(', 1)                                      data_type_cd
                      , NULLIF(SPLIT_PART(SPLIT_PART(a.data_type_cd, '(', 2), ')', 1), '')::INT data_type_size_cnt
                      , NULL                                                                    data_type_scale_cnt
                      , a.is_nullable_flg                                                       is_nullable_flg
                      , a.data_domain_cd                                                        data_domain_cd
                      , a.is_metadata_flg                                                       is_metadata_flg
                      , a.is_primary_key_flg                                                    is_primary_key_flg
                      , a.is_foreign_key_flg                                                    is_foreign_key_flg
                      , a.foreign_schema_name                                                   foreign_schema_name
                      , a.foreign_table_name                                                    foreign_table_name
                      , a.foreign_attribute_name                                                foreign_attribute_name
                      , a.attribute_desc                                                        attribute_desc
                      , a.attribute_extra_desc                                                  attribute_extra_desc
                      , a.axon_id                                                               axon_id
                      , a.axon_url                                                              axon_url
                      , a.model_major_version_from_num                                          model_major_version_from_num
                      , a.model_minor_version_from_num                                          model_minor_version_from_num
                      , a.model_major_version_to_num                                            model_major_version_to_num
                      , a.model_minor_version_to_num                                            model_minor_version_to_num
                      , a.model_major_version_to_num IS NOT NULL
                    AND CASE
                        WHEN a.model_major_version_to_num  = d.model_major_version_num
                        THEN a.model_minor_version_to_num <= d.model_minor_version_num
                        ELSE a.model_major_version_to_num <  d.model_major_version_num
                         END                                                                    is_deprecated_flg
                      , :model_effective_date                                                   model_effective_date
                   FROM (SELECT
                    DISTINCT ON (table_name
                               , attribute_name
                               , schema_name)
                                record_no
                              , domain_name
                              , schema_name
                              , table_name
                              , attribute_name
                              , attribute_extra_name
                              , data_type_cd
                              , data_domain_cd
                              , is_nullable_flg
                              , is_metadata_flg
                              , is_primary_key_flg
                              , is_foreign_key_flg
                              , foreign_table_name
                              , foreign_schema_name
                              , foreign_attribute_name
                              , attribute_desc
                              , attribute_extra_desc
                              , attribute_axon_id                                                                          axon_id
                              , attribute_axon_url                                                                         axon_url
                              , SPLIT_PART(attribute_model_version_from_num, '.', 1)::SMALLINT                             model_major_version_from_num
                              , COALESCE(NULLIF(SPLIT_PART(attribute_model_version_from_num, '.', 2), '')::SMALLINT, 0)    model_minor_version_from_num
                              , SPLIT_PART(attribute_model_version_to_num, '.', 1)::SMALLINT                               model_major_version_to_num
                              , CASE WHEN attribute_model_version_to_num IS NOT NULL
                                THEN COALESCE(NULLIF(SPLIT_PART(attribute_model_version_to_num, '.', 2), '')::SMALLINT, 0)
                                 END                                                                                       model_minor_version_to_num
                           FROM {stage}
                       ORDER BY table_name
                              , attribute_name
                              , schema_name
                              , model_major_version_to_num DESC NULLS FIRST
                              , model_minor_version_to_num DESC NULLS FIRST) a
                   JOIN {domains_stage}                                       d
                     ON d.domain_name = a.domain_name
        """),
        params={
            "model_effective_date": as_of,
        },
    )

    log.info("end")

    return mart_stage
