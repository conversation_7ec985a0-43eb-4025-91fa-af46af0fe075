from datetime import datetime
from typing import Sequence

from sqlalchemy import text
from sqlalchemy.orm import Session
from structlog.stdlib import Bo<PERSON><PERSON>og<PERSON>, get_logger

from metaloader_rest_api import etl
from metaloader_rest_api.common_model import LAST_DATE

LOAD_PAGE_SIZE_DEFAULT = 100

_DEFAULT_LOGGER = get_logger(__name__)


def load_data_model(
    session: Session,
    version_id: int,
    effective_date: datetime,
    schemas: Sequence[str],
    table_id: str,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    action_log = log.bind(action="load_data_model")
    action_log.info(
        "begin",
        version=version_id,
        table_id=table_id,
    )
    load_tables(session, version_id, effective_date, schemas, table_id, log)
    load_attributes(session, version_id, effective_date, schemas, table_id, log)
    load_keys(session, version_id, effective_date, schemas, table_id, log)
    load_keys_attributes(session, version_id, effective_date, schemas, table_id, log)
    action_log.info("end")


def load_tables(
    session: Session,
    version_id: int,
    effective_date: datetime,
    schemas: Sequence[str],
    table_id: str,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    log = log.bind(
        action="load_tables",
        table="bridge_table",
    )
    log.info("begin", table_id=table_id)

    stage = f"stg.bridge_table_{table_id}"

    log.info("create stage")
    session.execute(text(f"DROP TABLE IF EXISTS {stage}"))
    session.execute(
        text(f"""
            CREATE TABLE {stage} (
                domain_rk                    SMALLINT
              , data_layer_rk                SMALLINT
              , source_rk                    BIGINT
              , schema_name                  TEXT
              , table_name                   TEXT
              , table_desc                   TEXT
              , table_type_rk                SMALLINT
              , table_distribution_type_rk   SMALLINT
              , is_dict_flg                  BOOL
              , is_map_flg                   BOOL
              , is_hub_flg                   BOOL
              , is_sal_flg                   BOOL
              , is_mart_flg                  BOOL
              , is_bridge_flg                BOOL
              , is_link_flg                  BOOL
              , is_temporal_flg              BOOL
              , is_accessor_flg              BOOL
              , version_scd_type_rk          SMALLINT
              , history_scd_type_rk          SMALLINT
              , model_major_version_from_num SMALLINT
              , model_minor_version_from_num SMALLINT
              , model_major_version_to_num   SMALLINT
              , model_minor_version_to_num   SMALLINT
              , is_deprecated_flg            BOOL
              , model_effective_date	     DATE
            )
        """)
    )
    session.execute(
        statement=text(f"""
            INSERT INTO {stage} (domain_rk
                               , data_layer_rk
                               , source_rk
                               , schema_name
                               , table_name
                               , table_desc
                               , table_type_rk
                               , table_distribution_type_rk
                               , is_dict_flg
                               , is_map_flg
                               , is_hub_flg
                               , is_sal_flg
                               , is_mart_flg
                               , is_bridge_flg
                               , is_link_flg
                               , is_temporal_flg
                               , is_accessor_flg
                               , version_scd_type_rk
                               , history_scd_type_rk
                               , model_major_version_from_num
                               , model_minor_version_from_num
                               , model_major_version_to_num
                               , model_minor_version_to_num
                               , is_deprecated_flg
                               , model_effective_date)
                 SELECT COALESCE(lt.domain_rk, -1)                   domain_rk
                      , COALESCE(lt.data_layer_rk, pt.data_layer_rk) data_layer_rk
                      , COALESCE(lt.source_rk, -1)                   source_rk
                      , pt.schema_name                               schema_name
                      , pt.table_name                                table_name
                      , COALESCE(lt.table_desc, pt.table_desc)       table_desc
                      , pt.table_type_rk                             table_type_rk
                      , pt.table_distribution_type_rk                table_distribution_type_rk
                      , COALESCE(lt.is_dict_flg,   FALSE)            is_dict_flg
                      , COALESCE(lt.is_map_flg,    FALSE)            is_map_flg
                      , COALESCE(lt.is_hub_flg,    FALSE)            is_hub_flg
                      , COALESCE(lt.is_sal_flg,    FALSE)            is_sal_flg
                      , COALESCE(lt.is_mart_flg,   FALSE)            is_mart_flg
                      , COALESCE(lt.is_bridge_flg, FALSE)            is_bridge_flg
                      , COALESCE(lt.is_link_flg,   FALSE)            is_link_flg
                      , FALSE                                        is_temporal_flg
                      , pt.is_accessor_flg                           is_accessor_flg
                      , COALESCE(lt.version_scd_type_rk, -1)         version_scd_type_rk
                      , COALESCE(lt.history_scd_type_rk, -1)         history_scd_type_rk
                      , lt.model_major_version_from_num              model_major_version_from_num
                      , lt.model_minor_version_from_num              model_minor_version_from_num
                      , lt.model_major_version_to_num                model_major_version_to_num
                      , lt.model_minor_version_to_num                model_minor_version_to_num
                      , COALESCE(lt.is_deprecated_flg, FALSE)        is_deprecated_flg
                      , lt.model_effective_date                      model_effective_date
                   FROM (SELECT *
                           FROM metamodel.bridge_physical_table
                          WHERE schema_name = ANY(:schema_names)
                            AND effective_to_dttm = :effective_to_dttm
                            AND deleted_flg IS FALSE)                  pt
              LEFT JOIN metamodel.bridge_logical_table                 lt
                     ON lt.table_name  = pt.table_name
                    AND lt.schema_name = pt.schema_name
                    AND lt.effective_to_dttm = :effective_to_dttm
                    AND lt.deleted_flg IS FALSE
        """),
        params={
            "effective_to_dttm": LAST_DATE,
            "schema_names": schemas,
        },
    )

    log.info("merge")
    etl.merge(
        session=session,
        version_id=version_id,
        effective_date=effective_date,
        table="metamodel.bridge_table",
        stage=stage,
        primary_key="table_rk",
        keys=[
            "table_name",
            "schema_name",
        ],
        values=[
            "domain_rk",
            "data_layer_rk",
            "source_rk",
            "table_desc",
            "table_type_rk",
            "table_distribution_type_rk",
            "is_dict_flg",
            "is_map_flg",
            "is_hub_flg",
            "is_sal_flg",
            "is_mart_flg",
            "is_bridge_flg",
            "is_link_flg",
            "is_temporal_flg",
            "is_accessor_flg",
            "version_scd_type_rk",
            "history_scd_type_rk",
            "model_major_version_from_num",
            "model_minor_version_from_num",
            "model_major_version_to_num",
            "model_minor_version_to_num",
            "is_deprecated_flg",
        ],
        others=[
            "model_effective_date",
        ],
        filter_expression="schema_name = ANY(:schema_names)",
        params={"schema_names": schemas},
        log=log,
    )

    log.info("end")


def load_attributes(
    session: Session,
    version_id: int,
    effective_date: datetime,
    schemas: Sequence[str],
    table_id: str,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    log = log.bind(
        action="load_attributes",
        table="bridge_attribute",
    )
    log.info("begin", table_id=table_id)

    stage = f"stg.bridge_attribute_{table_id}"

    log.info("create stage")
    session.execute(text(f"DROP TABLE IF EXISTS {stage}"))
    session.execute(
        text(f"""
            CREATE TABLE {stage} (
                table_rk                     BIGINT
              , attribute_name               TEXT
              , attribute_no                 SMALLINT
              , attribute_desc               TEXT
              , data_type_rk                 SMALLINT
              , data_type_size_cnt           SMALLINT
              , data_type_scale_cnt          SMALLINT
              , is_nullable_flg              BOOL
              , default_value_txt            TEXT
              , data_domain_rk               SMALLINT
              , is_metadata_flg              BOOL
              , model_major_version_from_num SMALLINT
              , model_minor_version_from_num SMALLINT
              , model_major_version_to_num   SMALLINT
              , model_minor_version_to_num   SMALLINT
              , is_deprecated_flg            BOOL
              , model_effective_date	     DATE
            )
        """)
    )
    session.execute(
        statement=text(f"""
            INSERT INTO {stage} (table_rk
                               , attribute_name
                               , attribute_no
                               , attribute_desc
                               , data_type_rk
                               , data_type_size_cnt
                               , data_type_scale_cnt
                               , is_nullable_flg
                               , default_value_txt
                               , data_domain_rk
                               , is_metadata_flg
                               , model_major_version_from_num
                               , model_minor_version_from_num
                               , model_major_version_to_num
                               , model_minor_version_to_num
                               , is_deprecated_flg
                               , model_effective_date)
                 SELECT t.table_rk                                     table_rk
                      , pa.attribute_name                              attribute_name
                      , pa.attribute_no                                attribute_no
                      , COALESCE(la.attribute_desc, pa.attribute_desc) attribute_desc
                      , pa.data_type_rk                                data_type_rk
                      , pa.data_type_size_cnt                          data_type_size_cnt
                      , pa.data_type_scale_cnt                         data_type_scale_cnt
                      , pa.is_nullable_flg                             is_nullable_flg
                      , pa.default_value_txt                           default_value_txt
                      , COALESCE(la.data_domain_rk, -1)                data_domain_rk
                      , COALESCE(la.is_metadata_flg, FALSE)            is_metadata_flg
                      , la.model_major_version_from_num                model_major_version_from_num
                      , la.model_minor_version_from_num                model_minor_version_from_num
                      , la.model_major_version_to_num                  model_major_version_to_num
                      , la.model_minor_version_to_num                  model_minor_version_to_num
                      , COALESCE(la.is_deprecated_flg, FALSE)          is_deprecated_flg
                      , la.model_effective_date                        model_effective_date
                   FROM (SELECT *
                           FROM metamodel.bridge_physical_attribute
                          WHERE schema_name = ANY(:schema_names)
                            AND effective_to_dttm = :effective_to_dttm
                            AND deleted_flg IS FALSE)                  pa
                   JOIN metamodel.bridge_table                         t
                     ON t.table_name  = pa.table_name
                    AND t.schema_name = pa.schema_name
                    AND t.effective_to_dttm = :effective_to_dttm
                    AND t.deleted_flg IS FALSE
              LEFT JOIN metamodel.bridge_logical_attribute             la
                     ON la.table_name     = pa.table_name
                    AND la.attribute_name = pa.attribute_name
                    AND la.schema_name    = pa.schema_name
                    AND la.effective_to_dttm = :effective_to_dttm
                    AND la.deleted_flg IS FALSE
        """),
        params={
            "effective_to_dttm": LAST_DATE,
            "schema_names": schemas,
        },
    )

    log.info("merge")
    etl.merge(
        session=session,
        version_id=version_id,
        effective_date=effective_date,
        table="metamodel.bridge_attribute",
        stage=stage,
        primary_key="attribute_rk",
        keys=[
            "table_rk",
            "attribute_name",
        ],
        values=[
            "attribute_no",
            "attribute_desc",
            "data_type_rk",
            "data_type_size_cnt",
            "data_type_scale_cnt",
            "is_nullable_flg",
            "default_value_txt",
            "data_domain_rk",
            "is_metadata_flg",
            "model_major_version_from_num",
            "model_minor_version_from_num",
            "model_major_version_to_num",
            "model_minor_version_to_num",
            "is_deprecated_flg",
        ],
        others=[
            "model_effective_date",
        ],
        filter_expression="""
            table_rk IN (SELECT table_rk
                           FROM metamodel.bridge_table
                          WHERE schema_name = ANY(:schema_names)
                            AND effective_to_dttm = :effective_to_dttm)
        """,
        params={"schema_names": schemas},
        log=log,
    )

    log.info("end")


def load_keys(
    session: Session,
    version_id: int,
    effective_date: datetime,
    schemas: Sequence[str],
    table_id: str,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    log = log.bind(
        action="load_keys",
        table="bridge_key",
    )
    log.info("begin", table_id=table_id)

    merge_stage = f"stg.bridge_key_{table_id}"

    log.info("create stage")
    session.execute(text(f"DROP TABLE IF EXISTS {merge_stage}"))
    session.execute(
        text(f"""
            CREATE TABLE {merge_stage} (
                key_rk                       BIGINT
              , key_name                     TEXT
              , table_rk                     BIGINT
              , is_primary_flg               BOOL
              , is_foreign_flg               BOOL
              , is_unique_flg                BOOL
              , is_hash_flg                  BOOL
              , is_distribution_flg          BOOL
              , model_major_version_from_num SMALLINT
              , model_minor_version_from_num SMALLINT
              , model_major_version_to_num   SMALLINT
              , model_minor_version_to_num   SMALLINT
              , is_deprecated_flg            BOOL
              , model_effective_date	     DATE
            )
        """)
    )
    session.execute(
        statement=text(f"""
            INSERT INTO {merge_stage} (key_rk
                                     , key_name
                                     , table_rk
                                     , is_primary_flg
                                     , is_foreign_flg
                                     , is_unique_flg
                                     , is_hash_flg
                                     , is_distribution_flg
                                     , model_major_version_from_num
                                     , model_minor_version_from_num
                                     , model_major_version_to_num
                                     , model_minor_version_to_num
                                     , is_deprecated_flg
                                     , model_effective_date)
                 SELECT lk.logical_key_rk               key_rk
                      , lk.key_name                     key_name
                      , t.table_rk                      table_rk
                      , lk.is_primary_flg               is_primary_flg
                      , lk.is_foreign_flg               is_foreign_flg
                      , FALSE                           is_unique_flg
                      , FALSE                           is_hash_flg
                      , FALSE                           is_distribution_flg
                      , lk.model_major_version_from_num model_major_version_from_num
                      , lk.model_minor_version_from_num model_minor_version_from_num
                      , lk.model_major_version_to_num   model_major_version_to_num
                      , lk.model_minor_version_to_num   model_minor_version_to_num
                      , lk.is_deprecated_flg            is_deprecated_flg
                      , lk.model_effective_date         model_effective_date
                   FROM metamodel.bridge_logical_key              lk
                   JOIN metamodel.bridge_table                    t
                     ON t.table_name  = lk.table_name
                    AND t.schema_name = lk.schema_name
                    AND lk.schema_name = ANY(:schema_names)
                    AND t.effective_to_dttm = :effective_to_dttm
                    AND t.deleted_flg IS FALSE
                    AND lk.effective_to_dttm = :effective_to_dttm
                    AND lk.deleted_flg IS FALSE
                  UNION ALL
                 SELECT physical_key_rk              key_rk
                      , key_name                     key_name
                      , table_rk                     table_rk
                      , FALSE                        is_primary_flg
                      , FALSE                        is_foreign_flg
                      , FALSE                        is_unique_flg
                      , FALSE                        is_hash_flg
                      , TRUE                         is_distribution_flg
                      , model_major_version_from_num model_major_version_from_num
                      , model_minor_version_from_num model_minor_version_from_num
                      , model_major_version_to_num   model_major_version_to_num
                      , model_minor_version_to_num   model_minor_version_to_num
                      , is_deprecated_flg            is_deprecated_flg
                      , model_effective_date         model_effective_date
                    FROM (SELECT
                     DISTINCT ON (pka.physical_key_rk)
                                 pka.physical_key_rk
                               , pka.key_name
                               , t.table_rk
                               , a.model_major_version_from_num
                               , a.model_minor_version_from_num
                               , a.model_major_version_to_num
                               , a.model_minor_version_to_num
                               , a.is_deprecated_flg
                               , a.model_effective_date
                            FROM metamodel.link_physical_key_attribute      pka
                            JOIN metamodel.bridge_table                     t
                              ON t.table_name  = pka.table_name
                             AND t.schema_name = pka.schema_name
                             AND pka.schema_name = ANY(:schema_names)
                             AND t.effective_to_dttm = :effective_to_dttm
                             AND t.deleted_flg IS FALSE
                             AND pka.effective_to_dttm = :effective_to_dttm
                             AND pka.deleted_flg IS FALSE
                            JOIN metamodel.bridge_attribute                 a
                              ON a.table_rk        = t.table_rk
                             AND a.attribute_name  = pka.attribute_name
                             AND a.effective_to_dttm = :effective_to_dttm
                             AND a.deleted_flg IS FALSE
                        ORDER BY pka.physical_key_rk
                               , a.model_major_version_to_num DESC NULLS FIRST
                               , a.model_minor_version_to_num DESC NULLS FIRST
                               , a.model_effective_date       DESC) k
        """),
        params={
            "effective_to_dttm": LAST_DATE,
            "schema_names": schemas,
        },
    )

    log.info("merge")
    etl.merge(
        session=session,
        version_id=version_id,
        effective_date=effective_date,
        table="metamodel.bridge_key",
        stage=merge_stage,
        primary_key=None,
        keys=[
            "key_rk",
        ],
        values=[
            "is_distribution_flg",
            "model_major_version_from_num",
            "model_minor_version_from_num",
            "model_major_version_to_num",
            "model_minor_version_to_num",
            "is_deprecated_flg",
        ],
        others=[
            "key_name",
            "table_rk",
            "is_primary_flg",
            "is_foreign_flg",
            "is_unique_flg",
            "is_hash_flg",
            "model_effective_date",
        ],
        filter_expression="""
            table_rk IN (SELECT table_rk
                           FROM metamodel.bridge_table
                          WHERE schema_name = ANY(:schema_names)
                            AND effective_to_dttm = :effective_to_dttm)
        """,
        params={"schema_names": schemas},
        log=log,
    )

    log.info("end")


def load_keys_attributes(
    session: Session,
    version_id: int,
    effective_date: datetime,
    schemas: Sequence[str],
    table_id: str,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    log = log.bind(
        action="load_keys_attributes",
        table="link_key_attribute",
    )
    log.info("begin", table=table_id)

    merge_stage = f"stg.link_key_attribute_{table_id}"

    log.info("create stage")
    session.execute(text(f"DROP TABLE IF EXISTS {merge_stage}"))
    session.execute(
        text(f"""
            CREATE TABLE {merge_stage} (
                key_rk               BIGINT
              , attribute_rk         BIGINT
              , attribute_no         SMALLINT
              , foreign_key_rk       BIGINT
              , foreign_attribute_rk BIGINT
            )
        """)
    )

    session.execute(
        statement=text(f"""
            INSERT INTO {merge_stage} (key_rk
                                     , attribute_rk
                                     , attribute_no
                                     , foreign_key_rk
                                     , foreign_attribute_rk)
                 SELECT lka.logical_key_rk                                key_rk
                      , a.attribute_rk                                    attribute_rk
                      , lka.attribute_no                                  attribute_no
                      , CASE
                        WHEN lka.foreign_logical_key_rk IS NOT NULL
                         AND kf.key_rk                  IS NULL
                        THEN -1
                        ELSE lka.foreign_logical_key_rk
                         END                                              foreign_key_rk
                      , CASE
                        WHEN lka.foreign_logical_attribute_rk IS NOT NULL
                         AND af.attribute_rk                  IS NULL
                        THEN -1
                        ELSE af.attribute_rk
                         END                                              foreign_attribute_rk
                   FROM metamodel.link_logical_key_attribute           lka
                   JOIN metamodel.bridge_table                         t
                     ON t.table_name  = lka.table_name
                    AND t.schema_name = lka.schema_name
                    AND lka.schema_name = ANY(:schema_names)
                    AND lka.effective_to_dttm = :effective_to_dttm
                    AND lka.deleted_flg IS FALSE
                    AND t.effective_to_dttm = :effective_to_dttm
                    AND t.deleted_flg IS FALSE
                   JOIN metamodel.bridge_attribute                     a
                     ON a.table_rk       = t.table_rk
                    AND a.attribute_name = lka.attribute_name
                    AND a.effective_to_dttm = :effective_to_dttm
                    AND a.deleted_flg IS FALSE
              LEFT JOIN metamodel.bridge_table                         tf
                     ON tf.table_name  = lka.foreign_table_name
                    AND tf.schema_name = lka.foreign_schema_name
                    AND tf.effective_to_dttm = :effective_to_dttm
                    AND tf.deleted_flg IS FALSE
              LEFT JOIN metamodel.bridge_attribute                     af
                     ON af.table_rk       = tf.table_rk
                    AND af.attribute_name = lka.foreign_attribute_name
                    AND af.effective_to_dttm = :effective_to_dttm
                    AND af.deleted_flg IS FALSE
              LEFT JOIN metamodel.bridge_key                           kf
                     ON kf.key_rk   = lka.foreign_logical_key_rk
                    AND kf.effective_to_dttm = :effective_to_dttm
                    AND kf.deleted_flg IS FALSE
                  UNION ALL
                 SELECT pka.physical_key_rk key_rk
                      , a.attribute_rk      attribute_rk
                      , pka.attribute_no    attribute_no
                      , NULL                foreign_key_rk
                      , NULL                foreign_attribute_rk
                   FROM metamodel.link_physical_key_attribute          pka
                   JOIN metamodel.bridge_table                         t
                     ON t.table_name  = pka.table_name
                    AND t.schema_name = pka.schema_name
                    AND pka.schema_name = ANY(:schema_names)
                    AND pka.effective_to_dttm = :effective_to_dttm
                    AND pka.deleted_flg IS FALSE
                    AND t.effective_to_dttm = :effective_to_dttm
                    AND t.deleted_flg IS FALSE
                   JOIN metamodel.bridge_attribute                     a
                     ON a.table_rk       = t.table_rk
                    AND a.attribute_name = pka.attribute_name
                    AND a.effective_to_dttm = :effective_to_dttm
                    AND a.deleted_flg IS FALSE
        """),
        params={
            "effective_to_dttm": LAST_DATE,
            "schema_names": schemas,
        },
    )

    log.info("merge")
    etl.merge(
        session=session,
        version_id=version_id,
        effective_date=effective_date,
        table="metamodel.link_key_attribute",
        stage=merge_stage,
        primary_key=None,
        keys=[
            "key_rk",
            "attribute_rk",
        ],
        values=[
            "attribute_no",
            "foreign_key_rk",
            "foreign_attribute_rk",
        ],
        filter_expression="""
            key_rk IN (SELECT key_rk
                         FROM metamodel.bridge_key
                        WHERE table_rk IN (SELECT table_rk
                                             FROM metamodel.bridge_table
                                            WHERE schema_name = ANY(:schema_names)
                                              AND effective_to_dttm = :effective_to_dttm)
                          AND effective_to_dttm = :effective_to_dttm)
        """,
        params={"schema_names": schemas},
        log=log,
    )

    log.info("end")
