from contextlib import contextmanager
from datetime import date, datetime
from typing import Any, Optional, Sequence, Tuple
from uuid import UUID

from sqlalchemy import text
from sqlalchemy.orm import Session
from structlog.stdlib import BoundLogger, get_logger

from metaloader_rest_api import ceh_db
from metaloader_rest_api.common_model import LAST_DATE
from metaloader_rest_api.common_repository import SessionResource, get_table_id
from metaloader_rest_api.data_model import data_model, logical_model, physical_model
from metaloader_rest_api.deployment import get_deployment

_DEFAULT_LOGGER = get_logger(__name__)


def load_data_model(
    session: Session,
    load_id: UUID,
    module: str,
    release: str,
    effective_date: datetime,
    service: Optional[str],
    layers: Optional[Sequence[str]],
    page_size: int = physical_model.LOAD_PAGE_SIZE_DEFAULT,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    log = log.bind(module=module, version=release, service=service, layers=layers)

    module_version = get_module_version(
        session=session,
        module=module,
        service=service,
        release=release,
        effective_date=effective_date,
        log=log,
    )
    with module_version as module_version:
        module_id, _, version_id = module_version
        table_id = get_table_id(load_id)

        if service:
            dsn = ceh_db.get_dsn()
            schemas = get_service_schemas(session, module_id, service)

            physical_model.load_physical_model(
                session=session,
                version_id=version_id,
                effective_date=effective_date,
                dsn=dsn,
                schemas=schemas,
                page_size=page_size,
                table_id=table_id,
                log=log,
            )
        else:
            schemas = get_layers_schemas(session, layers)

        data_model.load_data_model(
            session=session,
            version_id=version_id,
            effective_date=effective_date,
            schemas=schemas,
            table_id=table_id,
            log=log,
        )


def load_logical_data_model(
    session: Session,
    load_id: UUID,
    module: str,
    version: str,
    effective_date: datetime,
    layer: str,
    columns: Sequence[str],
    rows: Sequence[Sequence[Any]],
    as_of: date,
    page_size: int = logical_model.LOAD_PAGE_SIZE_DEFAULT,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    log = log.bind(module=module, version=version, layer=layer)

    module_version = get_module_version(
        session=session,
        module=module,
        service=None,
        release=version,
        effective_date=effective_date,
        log=log,
    )
    with module_version as module_version:
        _, _, version_id = module_version
        layer_id, schemas = get_layer_schemas(session, layer)
        data = (dict(zip(columns, row)) for row in rows)
        table_id = get_table_id(load_id)

        logical_model.load_logical_model(
            session=session,
            version_id=version_id,
            effective_date=effective_date,
            layer_id=layer_id,
            schemas=schemas,
            data=data,
            as_of=as_of,
            table_id=table_id,
            page_size=page_size,
            log=log,
        )


@contextmanager
def get_module_version(
    session: Session,
    module: str,
    service: Optional[str],
    release: str,
    effective_date: datetime,
    log: BoundLogger = _DEFAULT_LOGGER,
):
    with SessionResource(session):
        yield get_deployment(
            session,
            module,
            service,
            release,
            effective_date,
            log,
        )


def get_service_schemas(
    session: Session,
    module_id: int,
    service: str,
) -> Sequence[str]:
    return session.scalars(
        statement=text("""
            SELECT ss.schema_name
              FROM metamodel.link_service_schema ss
              JOIN metamodel.bridge_service      s
                ON s.service_rk = ss.service_rk
               AND s.service_cd = :service_cd
               AND ss.code_delivery_rk = :code_delivery_rk
               AND s.effective_to_dttm = :effective_to_dttm
               AND s.deleted_flg IS FALSE
        """),
        params={
            "effective_to_dttm": LAST_DATE,
            "code_delivery_rk": module_id,
            "service_cd": service,
        },
    ).fetchall()


def get_layers_schemas(
    session: Session,
    layers: Sequence[str],
) -> Sequence[str]:
    return session.scalars(
        statement=text("""
            SELECT tsdl.schema_name
              FROM metamodel.link_table_schema_x_data_layer tsdl
              JOIN dict.dict_data_layer                     dl
                ON dl.data_layer_rk = tsdl.data_layer_rk
               AND dl.data_layer_cd = ANY (:data_layer_cds)
        """),
        params={
            "data_layer_cds": layers,
        },
    ).fetchall()


def get_layer_schemas(
    session: Session,
    layer: str,
) -> Tuple[int, Sequence[str]]:
    layer_id = session.scalars(
        statement=text("""
            SELECT data_layer_rk
              FROM dict.dict_data_layer 
             WHERE data_layer_cd = :data_layer_cd
        """),
        params={
            "data_layer_cd": layer,
        },
    ).one()

    schemas = session.scalars(
        statement=text("""
            SELECT schema_name
              FROM metamodel.link_table_schema_x_data_layer
             WHERE data_layer_rk = :data_layer_rk
        """),
        params={
            "data_layer_rk": layer_id,
        },
    ).fetchall()

    return layer_id, schemas
