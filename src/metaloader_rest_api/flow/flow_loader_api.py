from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import BaseModel
from sqlalchemy.orm import Session
from structlog.stdlib import Bo<PERSON><PERSON>ogger, get_logger

from metaloader_rest_api.common_param import (
    DbOptions,
    DeploymentInfo,
    FailsafeOptions,
    HttpOptions,
)
from metaloader_rest_api.flow import flow_loader_int

_DEFAULT_LOGGER = get_logger(__name__)


class LoadFlowsParams(BaseModel):
    deployment: DeploymentInfo = DeploymentInfo()
    with_tasks: bool = False
    limit: Optional[int] = None
    http_options: HttpOptions = HttpOptions()
    db_options: DbOptions = DbOptions()
    failsafe_options: FailsafeOptions = FailsafeOptions()


def load_flows(
    session: Session,
    load_id: UUID,
    effective_date: datetime,
    params: LoadFlowsParams,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    return flow_loader_int.load_flows(
        session=session,
        load_id=load_id,
        module=params.deployment.module,
        service=params.deployment.service,
        release=params.deployment.release,
        effective_date=effective_date,  # params.deployment.effective_date
        with_tasks=params.with_tasks,
        limit=params.limit,
        http_timeout=params.http_options.timeout,
        http_retries=params.http_options.retries,
        http_page_size=params.http_options.page_size,
        db_fetch_size=params.db_options.page_size,
        db_page_size=params.db_options.page_size,
        db_commit=params.db_options.commit,
        fail_threshold=params.failsafe_options.threshold,
        log=log,
    )
