from datetime import datetime

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, bindparam, text
from sqlalchemy.dialects.postgresql import ARRAY, JSON<PERSON>
from sqlalchemy.orm import Session
from sqlalchemy.sql.sqltypes import SMALLINT, TEXT
from structlog.stdlib import <PERSON><PERSON><PERSON><PERSON><PERSON>, get_logger

from metaloader_rest_api import etl
from metaloader_rest_api.common_model import LAST_DATE
from metaloader_rest_api.common_repository import (
    BufferedStageBatchRepository,
    StageBatchRepository,
)
from metaloader_rest_api.flow.flow_model import Flow, FlowLinkType, FlowType

_DEFAULT_LOGGER = get_logger(__name__)


class FlowRepository(BufferedStageBatchRepository[Flow]):
    def __init__(
        self,
        session: Session,
        module_id: int,
        service_id: int,
        version_id: int,
        effective_date: datetime,
        table_id: str,
        page_size: int = StageBatchRepository.PAGE_SIZE_DEFAULT,
        log: BoundLogger = _DEFAULT_LOGGER,
    ):
        table = "mart_flow"
        super().__init__(
            session,
            table,
            table_id,
            self._create_statement,
            self._load_statement,
            page_size,
            log,
        )
        self._module_id = module_id
        self._service_id = service_id
        self._version_id = version_id
        self._effective_date = effective_date

    @property
    def module_id(self) -> int:
        return self._module_id

    @module_id.setter
    def module_id(self, value: int) -> None:
        self._module_id = value

    @property
    def version_id(self) -> int:
        return self._version_id

    @version_id.setter
    def version_id(self, value: int) -> None:
        self._version_id = value

    @property
    def effective_date(self) -> datetime:
        return self._effective_date

    @effective_date.setter
    def effective_date(self, value: datetime) -> None:
        self._effective_date = value

    def __exit__(self, exc_type, exc_value, traceback):
        try:
            return super().__exit__(exc_type, exc_value, traceback)
        except Exception as exception:
            exc_type = type(exception)
            raise exception
        finally:
            if exc_type is None:
                self.load_bridge()
                self.load_link()
                self.load_link_service()

    def _prepare_load_statement(self) -> TextClause:
        statement = super()._prepare_load_statement()
        statement = statement.bindparams(
            bindparam("name", type_=TEXT()),
            bindparam("description", type_=TEXT()),
            bindparam("type", type_=SMALLINT()),
            bindparam("status", type_=SMALLINT()),
            bindparam("tags", type_=ARRAY(TEXT)),
            bindparam("dag", type_=JSONB(none_as_null=True)),
            bindparam("tasks", type_=JSONB(none_as_null=True)),
        )
        return statement

    @staticmethod
    def _create_statement(table: str) -> str:
        return f"""
            CREATE TABLE {table} (
                flow_name              TEXT
              , flow_desc              TEXT
              , flow_type_rk           SMALLINT
              , flow_release_status_rk SMALLINT
              , tag_list               TEXT[]
              , dag_json               JSONB
              , dag_task_json          JSONB
            )
        """

    @staticmethod
    def _load_statement(table: str) -> str:
        return f"""
            INSERT INTO {table} (flow_name             
                               , flow_desc             
                               , flow_type_rk          
                               , flow_release_status_rk
                               , tag_list              
                               , dag_json              
                               , dag_task_json)
            VALUES (:name
                  , :description
                  , :type
                  , :status
                  , :tags
                  , :dag
                  , :tasks)
        """

    def load_bridge(self) -> None:
        log = self._log.bind(
            action="load_bridge",
            table="bridge_flow",
        )
        log.info("begin", table_id=self._table_id)

        merge_stage = f"stg.bridge_flow_{self._table_id}"

        log.info("create merge_stage")
        self._session.execute(text(f"DROP TABLE IF EXISTS {merge_stage}"))
        self._session.execute(
            text(f"""
                CREATE TABLE {merge_stage} (
                    flow_name              TEXT
                  , flow_desc              TEXT
                  , flow_type_rk           SMALLINT
                  , flow_release_status_rk SMALLINT
                  , tag_list               TEXT[]
                  , dag_json               JSONB
                  , dag_hash               BIGINT
                  , dag_task_json          JSONB
                  , dag_task_hash          BIGINT
                  , code_delivery_rk       SMALLINT
                )
            """)
        )
        self._session.execute(
            statement=text(f"""
                INSERT INTO {merge_stage} (flow_name             
                                         , flow_desc             
                                         , flow_type_rk          
                                         , flow_release_status_rk
                                         , tag_list              
                                         , dag_json              
                                         , dag_hash              
                                         , dag_task_json         
                                         , dag_task_hash         
                                         , code_delivery_rk)
                     SELECT  
                DISTINCT ON (flow_name)
                            flow_name
                          , flow_desc
                          , flow_type_rk
                          , flow_release_status_rk
                          , tag_list
                          , dag_json
                          , JSONB_HASH_EXTENDED(dag_json, 0) dag_hash
                          , dag_task_json
                          , JSONB_HASH_EXTENDED(dag_task_json, 0) dag_task_hash
                          , :code_delivery_rk
                       FROM {self.table}
            """),
            params={
                "code_delivery_rk": self._module_id,
            },
        )

        log.info("merge")
        etl.merge(
            session=self._session,
            version_id=self._version_id,
            effective_date=self._effective_date,
            table="metamodel.bridge_flow",
            stage=merge_stage,
            primary_key="flow_rk",
            keys=["flow_name"],
            values=[
                "flow_desc",
                "flow_type_rk",
                "flow_release_status_rk",
                "tag_list",
                "dag_hash",
                "dag_task_hash",
            ],
            others=[
                "dag_json",
                "dag_task_json",
                "code_delivery_rk",
            ],
            filter_expression=f"""
                    flow_type_rk != {FlowType.GROUP}
                AND code_delivery_rk = :code_delivery_rk
            """,
            params={"code_delivery_rk": self._module_id},
            log=log,
        )

        log.info("end")

    def load_link(self) -> None:
        log = self._log.bind(
            action="load_link",
            table="link_flow",
        )
        log.info("begin", table_id=self._table_id)

        merge_stage = f"stg.link_flow_{self._table_id}"

        log.info("create merge_stage")
        self._session.execute(text(f"DROP TABLE IF EXISTS {merge_stage}"))
        self._session.execute(
            text(f"""
                CREATE TABLE {merge_stage} (
                    source_flow_rk    BIGINT
                  , target_flow_rk    BIGINT
                  , owner_flow_rk     BIGINT
                  , flow_link_type_rk SMALLINT
                  , code_delivery_rk  SMALLINT
                )
            """)
        )
        self._session.execute(
            statement=text(f"""
                INSERT INTO {merge_stage} (source_flow_rk
                                         , target_flow_rk
                                         , owner_flow_rk
                                         , flow_link_type_rk
                                         , code_delivery_rk)
                     SELECT
                DISTINCT ON (source_flow_rk
                           , target_flow_rk)
                            MIN(flow_rk) FILTER (WHERE work_flow_flg IS FALSE) source_flow_rk
                          , MIN(flow_rk) FILTER (WHERE work_flow_flg IS TRUE)  target_flow_rk
                          , -1                                                 owner_flow_rk
                          , {FlowLinkType.TRIGGER}                             flow_link_type_rk
                          , :code_delivery_rk
                       FROM (WITH f
                               AS (SELECT flow_rk
                                        , flow_name
                                        , tag_list
                                        , SUBSTR(flow_name, STRPOS(flow_name, '_') + 1) root_flow_name
                                        , flow_type_rk = {FlowType.WORK}                work_flow_flg
                                     FROM metamodel.bridge_flow
                                    WHERE flow_type_rk IN ({FlowType.WORK}, {FlowType.CONTROL})
                                      AND code_delivery_rk = :code_delivery_rk
                                      AND effective_to_dttm = :effective_to_dttm
                                      AND deleted_flg IS FALSE)
                           SELECT flow_rk
                                , flow_name
                                , root_flow_name
                                , work_flow_flg
                             FROM f
                            UNION ALL
                           SELECT flow_rk
                                , flow_name
                                , tag_root_flow_name root_flow_name
                                , work_flow_flg
                             FROM (SELECT f.flow_rk
                                        , f.flow_name
                                        , f.root_flow_name
                                        , SUBSTR(t.tag_name, STRPOS(t.tag_name, '_') + 1) tag_root_flow_name
                                        , work_flow_flg
                                     FROM f
                             JOIN LATERAL UNNEST(tag_list) t(tag_name)
                                       ON CASE 
                                          WHEN work_flow_flg
                                          THEN t.tag_name ^@ 'cf:'
                                          ELSE t.tag_name ^@ 'wf:'
                                            OR t.tag_name ^@ 'wrk:'
                                           END) f_t
                            WHERE tag_root_flow_name != root_flow_name) f
                   GROUP BY root_flow_name
                     HAVING COUNT(DISTINCT work_flow_flg) > 1
            """),
            params={
                "code_delivery_rk": self._module_id,
                "effective_to_dttm": LAST_DATE,
            },
        )

        log.info("merge")
        etl.merge(
            session=self._session,
            version_id=self._version_id,
            effective_date=self._effective_date,
            table="metamodel.link_flow",
            stage=merge_stage,
            primary_key=None,
            keys=[
                "source_flow_rk",
                "target_flow_rk",
                "owner_flow_rk",
                "flow_link_type_rk",
            ],
            values=[],
            others=["code_delivery_rk"],
            filter_expression="""
                    owner_flow_rk = -1
                AND code_delivery_rk = :code_delivery_rk
            """,
            params={"code_delivery_rk": self._module_id},
            log=log,
        )

        log.info("end")

    def load_link_service(self) -> None:
        log = self._log.bind(
            action="load_link_service",
            table="link_flow_service",
        )
        log.info("begin", table_id=self._table_id)

        merge_stage = f"stg.link_flow_service_{self._table_id}"

        log.info("create merge_stage")
        self._session.execute(text(f"DROP TABLE IF EXISTS {merge_stage}"))
        self._session.execute(
            text(f"""
                CREATE TABLE {merge_stage} (
                    flow_rk          BIGINT
                  , service_rk       BIGINT
                  , code_delivery_rk BIGINT
                )
            """)
        )
        self._session.execute(
            statement=text(f"""
                INSERT INTO {merge_stage} (flow_rk
                                         , service_rk
                                         , code_delivery_rk)
                     SELECT flow_rk
                          , :service_rk
                          , :code_delivery_rk
                       FROM metamodel.bridge_flow
                      WHERE flow_type_rk != {FlowType.GROUP}
                        AND code_delivery_rk = :code_delivery_rk
                        AND effective_to_dttm = :effective_to_dttm
                        AND deleted_flg IS FALSE
            """),
            params={
                "service_rk": self._service_id,
                "code_delivery_rk": self._module_id,
                "effective_to_dttm": LAST_DATE,
            },
        )

        log.info("merge")
        etl.merge(
            session=self._session,
            version_id=self._version_id,
            effective_date=self._effective_date,
            table="metamodel.link_flow_service",
            stage=merge_stage,
            primary_key=None,
            keys=[
                "flow_rk",
                "service_rk",
            ],
            values=[],
            others=["code_delivery_rk"],
            filter_expression="code_delivery_rk = :code_delivery_rk",
            params={"code_delivery_rk": self._module_id},
            log=log,
        )

        log.info("end")
