from datetime import datetime
from typing import (
    Callable,
    ContextManager,
    Iterator,
    Optional,
)
from uuid import UUID

from sqlalchemy.orm import Session
from structlog.stdlib import BoundLogger, get_logger

from metaloader_rest_api.airflow.airflow_auth import airflow_auth
from metaloader_rest_api.airflow.airflow_dag import AirflowDag
from metaloader_rest_api.airflow.airflow_dag_provider import airflow_dag_provider
from metaloader_rest_api.ceh_service.ceh_service_provider import (
    provide_ceh_service_urls,
)
from metaloader_rest_api.common_param import (
    DB_COMMIT,
    DB_FETCH_SIZE_DEFAULT,
    DB_PAGE_SIZE_DEFAULT,
    FAILSAFE_THRESHOLD_DEFAULT,
    HTTP_PAGE_SIZE_DEFAULT,
    HTTP_RETRIES_DEFAULT,
    HTTP_TIMEOUT_DEFAULT,
)
from metaloader_rest_api.common_repository import SessionResource
from metaloader_rest_api.deployment import get_deployment
from metaloader_rest_api.flow.flow_factory import flow_factory
from metaloader_rest_api.flow.flow_model import Flow
from metaloader_rest_api.flow.flow_repository import FlowRepository
from metaloader_rest_api.http.http_client import http_client

_DEFAULT_LOGGER = get_logger(__name__)


def load_flows(
    session: Session,
    load_id: UUID,
    module: str,
    service: str,
    release: str,
    effective_date: datetime,
    with_tasks: bool = False,
    limit: Optional[int] = None,
    http_timeout: float = HTTP_TIMEOUT_DEFAULT,
    http_retries: int = HTTP_RETRIES_DEFAULT,
    http_page_size: int = HTTP_PAGE_SIZE_DEFAULT,
    db_fetch_size: int = DB_FETCH_SIZE_DEFAULT,
    db_page_size: int = DB_PAGE_SIZE_DEFAULT,
    db_commit: Optional[bool] = DB_COMMIT,
    fail_threshold: int = FAILSAFE_THRESHOLD_DEFAULT,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    log = log.bind(
        action="load_flows",
        load_id=load_id,
        module=module,
        service=service,
        release=release,
        effective_date=effective_date,
    )
    log.info(
        "begin",
        with_tasks=with_tasks,
        limit=limit,
        http_timeout=http_timeout,
        http_retries=http_retries,
        http_page_size=http_page_size,
        db_fetch_size=db_fetch_size,
        db_page_size=db_page_size,
        db_commit=db_commit,
        fail_threshold=fail_threshold,
    )
    try:
        module_id, service_id, version_id = get_deployment(
            session,
            module,
            service,
            release,
            effective_date,
            log,
        )
        airflow_url, _ = provide_ceh_service_urls(session, service_id)
        log.info(
            "init",
            module_id=module_id,
            version_id=version_id,
            airflow_url=airflow_url,
        )
        with SessionResource(
            session,
            db_commit,
            log,
        ):
            _load_flows(
                flow_repository=FlowRepository(
                    session=session,
                    module_id=module_id,
                    service_id=service_id,
                    version_id=version_id,
                    effective_date=effective_date,
                    table_id=load_id.hex,
                    page_size=db_page_size,
                    log=log,
                ),
                airflow_dag_provider=airflow_dag_provider(
                    http_client=http_client(
                        url=airflow_url,
                        auth=airflow_auth(),
                        timeout=http_timeout,
                        retries=http_retries,
                    ),
                    page_size=http_page_size,
                    page_limit=limit,
                    log=log,
                ),
                fail_threshold=fail_threshold,
                log=log,
            )
        log.info("end")
    except Exception:
        log.exception("fail")
        raise

    return None


def _load_flows(
    flow_repository: ContextManager[Callable[[Flow], None]],
    airflow_dag_provider: ContextManager[Iterator[AirflowDag]],
    fail_threshold: int = FAILSAFE_THRESHOLD_DEFAULT,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    with airflow_dag_provider as airflow_dags, flow_repository as store_flow:
        if fail_threshold == 0:
            log.warning("fail_threshold_off")
        fails = fail_threshold
        for airflow_dag in airflow_dags:
            try:
                store_flow(flow_factory(airflow_dag))
                fails = fail_threshold
            except Exception:
                fails -= 1
                log.exception("fail", flow=airflow_dag.id)
                if fails == 0:
                    log.error("fail_threshold_reached")
                    raise RuntimeError(f"Fail threshold {fail_threshold} reached")
