import re

from metaloader_rest_api.airflow.airflow_dag import AirflowDag
from metaloader_rest_api.flow.flow_model import Flow, FlowType, flow


def flow_factory(airflow_dag: AirflowDag) -> Flow:
    return flow(
        type=_flow_type(airflow_dag.id),
        name=airflow_dag.id,
        description=airflow_dag.description,
        tags=airflow_dag.tags,
        dag=airflow_dag.dag,
        tasks=airflow_dag.tasks,
    )


_FLOW_TYPES = {
    "wf": FlowType.WORK,
    "wrk": FlowType.WORK,
    "cf_master": FlowType.MASTER,
    "cf": FlowType.CONTROL,
    "sf": FlowType.SERVICE,
}
_FLOW_TYPE_PATTERN = re.compile(
    "|".join(f"^{type}(?=_)" for type in _FLOW_TYPES.keys())
)


def _flow_type(name: str) -> FlowType:
    if match := re.search(_FLOW_TYPE_PATTERN, name):
        return _FLOW_TYPES[match.group()]
    return FlowType.UNDEFINED
