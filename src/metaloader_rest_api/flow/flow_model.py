from enum import IntEnum
from typing import Any, Optional, Sequence, TypedDict


class FlowType(IntEnum):
    UNDEFINED = -1
    MASTER = 3
    GROUP = 4
    CONTROL = 2
    WORK = 1
    SERVICE = 5


class FlowStatus(IntEnum):
    DEPLOYED = 1
    UNDEPLOYED = 3
    DEPRECATED = 2


class FlowLinkType(IntEnum):
    TRIGGER = 2
    SUBSCRIBER = 1


class Flow(TypedDict):
    type: FlowType
    name: str
    description: Optional[str]
    tags: Sequence[str]
    status: FlowStatus
    dag: Any
    tasks: Optional[Sequence[Any]]


def flow(
    type: FlowType,
    name: str,
    description: str = None,
    tags: Sequence[str] = (),
    status: FlowStatus = FlowStatus.DEPLOYED,
    dag: Any = None,
    tasks: Optional[Sequence[Any]] = None,
) -> Flow:
    return Flow(
        type=type,
        name=name,
        description=description,
        tags=tags,
        status=status,
        dag=dag,
        tasks=tasks,
    )
