from ssl import create_default_context

from httpx import Auth, Client, HTTPTransport
from structlog.stdlib import get_logger

_DEFAULT_LOGGER = get_logger(__name__)

HTTP_DEFAULT_TIMEOUT = 5.0
HTTP_DEFAULT_RETRIES = 3


def http_client(
    url: str,
    auth: Auth = None,
    timeout: float = HTTP_DEFAULT_TIMEOUT,
    retries: int = HTTP_DEFAULT_RETRIES,
) -> Client:
    return Client(
        base_url=url,
        transport=HTTPTransport(
            verify=create_default_context(),
            retries=retries,
        ),
        auth=auth,
        follow_redirects=True,
        timeout=timeout,
    )
