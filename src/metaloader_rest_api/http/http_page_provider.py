from itertools import count, islice
from typing import Callable, Iterator, Optional, Sequence, TypeVar

from httpx import Client, QueryParams, Response
from structlog.stdlib import BoundLogger, get_logger

_DEFAULT_LOGGER = get_logger(__name__)


_T = TypeVar("_T", covariant=True)

PageQueryParamsFactory = Callable[[int], QueryParams]

PageResponseProcessor = Callable[[Response], Sequence[_T]]


HTTP_DEFAULT_PAGE_SIZE = 100


def http_page_provider(
    client: Client,
    url: str,
    query_params_factory: PageQueryParamsFactory,
    response_processor: PageResponseProcessor[_T],
    size: int = HTTP_DEFAULT_PAGE_SIZE,
    limit: Optional[int] = None,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> Iterator[Sequence[_T]]:
    log = log.bind(actor="http_page_provider")
    log.info("init", url=url, size=size, limit=limit)
    pages, items = 0, 0
    for offset in islice(count(step=size), limit):
        response = client.get(
            url=url,
            params=query_params_factory(offset),
        )
        result = response_processor(response)

        if result:
            yield result

        pages += 1
        items += len(result)
        log.info("page", pages=pages, items=items)

        if len(result) < size:
            log.info("done", pages=pages, items=items)
            return
    log.info("limit", pages=pages, items=items)
