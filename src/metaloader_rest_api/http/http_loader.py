from contextlib import contextmanager
from typing import Any, Callable, Con<PERSON><PERSON><PERSON><PERSON>, Iterator, Optional

from httpx import Client
from structlog.stdlib import <PERSON><PERSON><PERSON><PERSON><PERSON>, get_logger

from metaloader_rest_api.yaml.bytes_iterator_yaml_loader import (
    bytes_iterator_yaml_loader,
)
from metaloader_rest_api.yaml.yaml_loader import YAML_LOADER_DEFAULT, Yaml, YamlLoader
from metaloader_rest_api.yaml.yaml_stream import ReadableYamlStream

_DEFAULT_LOGGER = get_logger(__name__)


def load_http_text(
    client: Client,
    url: str,
) -> str:
    return client.get(url).raise_for_status().text


def load_http_json(
    client: Client,
    url: str,
) -> Any:
    return client.get(url).raise_for_status().json()


HttpLoader = Callable[[str], Iterator[bytes]]


@contextmanager
def http_byte_loader(
    client: Client,
    url: str,
    chunk_size: Optional[int] = None,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> Iterator[HttpLoader]:
    log = log.bind(actor="http_byte_loader", url=url)
    log.info("init", chunk_size=chunk_size)

    client.head(url=f"{url}/").raise_for_status()

    def load(url_: str) -> Iterator[bytes]:
        yield from (
            client.get(f"{url}/{url_}")
            .raise_for_status()
            .iter_bytes(chunk_size=chunk_size)
        )

    yield load


HttpYamlLoader = ContextManager[Callable[[str], Yaml]]


_YAML_CHUNK_SIZE = ReadableYamlStream.buffer_size


@contextmanager
def http_yaml_loader(
    client: Client,
    url: str,
    yaml_loader: YamlLoader = YAML_LOADER_DEFAULT,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> Iterator[Callable[[str], Yaml]]:
    log = log.bind(actor="http_yaml_loader", url=url)
    log.info("init", yaml_loader=yaml_loader)

    load_yaml = bytes_iterator_yaml_loader(yaml_loader)
    with (
        client as client,
        http_byte_loader(
            client,
            url,
            _YAML_CHUNK_SIZE,
            log,
        ) as load_url,
    ):

        def load(url_: str) -> Any:
            return load_yaml(url_, load_url(url_))

        yield load
