from datetime import datetime
from enum import Enum
from typing import List, Optional, <PERSON><PERSON>
from uuid import UUID

from pydantic import BaseModel, constr


class ReleaseMetadata(BaseModel):
    release_module: str
    release_num: constr(pattern=r"\d+\.\d+\.\d+")
    release_desc: Optional[str] = None
    release_date: Optional[datetime] = None
    effective_from_date: datetime

    @property
    def release_nums(self) -> Tuple[int, int, int]:
        major, minor, fix = self.release_num.split(".", 2)
        return int(major), int(minor), int(fix)


class TransactionStatus(Enum):
    OPENED = "opened"
    COMMITTED = "committed"
    ROLLEDBACK = "rolledback"
    IN_PROGRESS = "in_progress"
    ETL_ERROR = "etl_error"

    @property
    def complete(self) -> bool:
        return not (self is self.OPENED or self is self.IN_PROGRESS)


class TransactionMode(Enum):
    INCREMENTAL = "incremental"
    SNAPSHOT = "snapshot"


class ResourceAction(Enum):
    ADD = "add"
    MODIFY = "modify"
    DELETE = "delete"


class ResourceType(Enum):
    CEH = "ceh"
    UNI = "uni"


class TransactionInfo(BaseModel):
    task: str
    status: TransactionStatus
    description: Optional[str] = None


class ResourceTransactionInfo(BaseModel):
    uid: UUID
    status: TransactionStatus
    release_metadata: Optional[ReleaseMetadata]
    resources: List[str]


class ResourceActionErrorDetails(BaseModel):
    msg: str
    resource_cd: str
    existing_action: Optional[ResourceAction] = None
