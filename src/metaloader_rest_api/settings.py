from functools import lru_cache
from typing import Dict

from pydantic import BaseModel, Field, HttpUrl, PostgresDsn, RedisDsn
from sqlalchemy import make_url

from metaloader_rest_api.helpers import read_settings_from_env
from metaloader_rest_api.logging import (
    DEFAULT_LOG_FORMAT,
    DEFAULT_LOG_LEVEL,
    LogFormat,
    LogLevel,
)


class Settings(BaseModel):
    database_url: PostgresDsn = Field(alias="DATABASE_URL")
    celery_broker_url: RedisDsn = Field(alias="CELERY_BROKER_URL")
    airflow_username: str = Field(alias="AF_USERNAME")
    airflow_password: str = Field(alias="AF_PASSWORD")
    auth_disabled: bool = Field(alias="AUTH_DISABLED", default=False)
    oauth_token_url: HttpUrl = Field(
        alias="OAUTH_TOKEN_URL",
        default="https://build-auth.dtpl.corp.dev.vtb/realms/ceh-realm/protocol/openid-connect/token",
    )
    rs256_public_key: str = Field(
        alias="RS256_PUBLIC_KEY",
        default="MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1qozn9G8b3g0Wnbyn7vp3FL3FqQ60CDlbixP"
        "7u0e9BTS14rNGgDCuD0GypU8JIElUiyiGeOwP5Y1gduC2bNh3BFySi3/h436O0eKUPE1X2IImQH+DILM"
        "3JcOdSwc202apEdLNZiD94So/iZYOWZXSPX3056oPla9h4pqf23vcunCNNoBRCxgzJDfnzMRAZpbxd78"
        "O3P4wuXVl+dDTj67PZCNSUuTomgLcTD3vxaVe9aF5R0sHA9UFt6wep8Nhr9MzoRWMqJT3MraV6hej9HH"
        "zMHIxhkRtaWonCj5emxDZFUb8QWTF3h4pu9wekEnHRG3ZsuoXP1rb3U+jPv7wU6YHQIDAQAB",
    )
    public_roles: str = Field(
        alias="PUBLIC_ROLES",
        pattern="^[a-zA-Z0-9_]+(,[a-zA-Z0-9_]+)*$",
        default="read_role",
    )
    log_format: LogFormat = Field(alias="LOG_FORMAT", default=DEFAULT_LOG_FORMAT)
    log_level: LogLevel = Field(alias="LOG_LEVEL", default=DEFAULT_LOG_LEVEL)

    class Config:
        populate_by_name = True


@lru_cache
def read_settings() -> Settings:
    return read_settings_from_env(settings_model=Settings)


def get_loggable_settings(settings: Settings) -> Dict[str, str]:
    settings_copy = settings.model_copy()
    settings_copy.database_url = _obfuscate_connection_string(
        settings.database_url.unicode_string()
    )
    settings_copy.airflow_password = "******"

    obfuscated = settings_copy.model_dump(by_alias=True)

    for key, value in obfuscated.items():
        if not isinstance(value, str):
            obfuscated[key] = str(value)

    return obfuscated


def _obfuscate_connection_string(connstr: str) -> str:
    return str(make_url(connstr))  # алхимия делает обфускацию пароля из коробки
