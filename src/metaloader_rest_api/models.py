import json
import uuid
from datetime import datetime, timedelta, timezone
from enum import Enum, IntEnum
from functools import partial
from typing import Literal, Optional

from sqlalchemy import (
    BigInteger,
    Column,
    DateTime,
    SmallInteger,
    create_engine,
)
from sqlalchemy.dialects.postgresql import J<PERSON>N<PERSON>, UUID
from sqlalchemy.orm import Session, declarative_base, sessionmaker

from metaloader_rest_api.helpers import JsonDict

Base = declarative_base()

METAMODEL_SERVICE_ID = 1  # service_cd=metamodel


class TransactionStatus(IntEnum):
    OPENED = 1  # service_transaction_status_cd=created
    COMMITTED = 4  # =commit
    ROLLEDBACK = 5  # =rollback
    IN_PROGRESS = 6  # =?
    ETL_ERROR = 7  # =?


NON_RESOLVED_STATUSES = (
    TransactionStatus.OPENED,
    TransactionStatus.IN_PROGRESS,
)


class TransactionMode(IntEnum):
    SNAPSHOT = 1
    INCREMENT = 2


class ResourceAction(IntEnum):
    ADD = 1
    MODIFY = 2
    DELETE = 4


class ResourceType(Enum):
    CEH = "ceh"
    UNI = "uni"


def now_at_utc() -> datetime:
    return datetime.now(timezone.utc)


class TransactionEntity(IntEnum):
    TASK = 1
    RESOURCE = 2


class BaseTransaction(Base):
    TIMEOUT_DEFAULT = timedelta(hours=3)
    __tablename__ = "service_transaction_status"
    __table_args__ = {"schema": "metamodel"}

    id: uuid.UUID = Column(
        "service_transaction_uid",
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
    )
    entity_type = Column(
        "entity_type",
        SmallInteger,
        nullable=False,
    )
    created_at: datetime = Column(
        "request_dttm",
        DateTime(timezone=True),
        nullable=False,
        default=now_at_utc,
    )
    status_changed_at: datetime = Column(
        "status_dttm",
        DateTime(timezone=True),
        nullable=False,
        default=now_at_utc,
    )
    resolved_at: Optional[datetime] = Column(
        "response_dttm",
        DateTime(timezone=True),
    )
    service_rk: Literal[1] = Column(
        "service_rk",
        SmallInteger,
        nullable=False,
        default=METAMODEL_SERVICE_ID,
    )
    status: int = Column(
        "service_transaction_status_rk",
        SmallInteger,
        nullable=False,
    )
    mode: Optional[int] = Column(
        "data_processing_mode_rk",
        SmallInteger,
    )
    timeout_sec: int = Column(
        "transaction_timeout_duration",
        BigInteger,
    )
    request: JsonDict = Column(
        "transaction_request_json",
        JSONB,
    )
    response: Optional[JsonDict] = Column(
        "transaction_response_json",
        JSONB,
    )

    __mapper_args__ = {
        "polymorphic_identity": "base",
        "polymorphic_on": entity_type,
    }


class TaskTransaction(BaseTransaction):
    __mapper_args__ = {"polymorphic_identity": TransactionEntity.TASK}


class ResourceTransaction(BaseTransaction):
    __mapper_args__ = {"polymorphic_identity": TransactionEntity.RESOURCE}


class SessionFactory:
    def __init__(self, database_url: str):
        engine = create_engine(
            database_url,
            json_serializer=get_json_serializer(),
            pool_pre_ping=True,
        )
        self._make_session = sessionmaker(
            autocommit=False, autoflush=False, bind=engine
        )

    def __call__(self) -> Session:
        return self._make_session()


def get_json_serializer():
    return partial(json.dumps, default=str)
