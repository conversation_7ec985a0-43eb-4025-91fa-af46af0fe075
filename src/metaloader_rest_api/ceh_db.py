import os
from typing import Any, Iterator, Mapping, Optional, Sequence, Union

from psycopg2 import connect
from psycopg2.extras import RealDictCursor
from structlog.stdlib import BoundLogger, get_logger

_DEFAULT_LOGGER = get_logger(__name__)


def get_dsn() -> Mapping[str, Any]:
    return {
        "host": os.getenv("CEH_DB_HOST"),
        "port": os.getenv("CEH_DB_PORT"),
        "dbname": os.getenv("CEH_DB_NAME"),
        "user": os.getenv("CEH_DB_USER"),
        "password": os.getenv("CEH_DB_PASSWORD"),
    }


def hide_dsn_password(dsn: Mapping[str, Any]) -> Mapping[str, Any]:
    return {key: value for key, value in dsn.items() if key != "password"}


LOAD_DEFAULT_FETCH_SIZE = 100


def load(
    dsn: Mapping[str, Any],
    query: str,
    parameters: Union[Mapping[str, Any], Sequence[Any], None] = None,
    fetch_size: Optional[int] = LOAD_DEFAULT_FETCH_SIZE,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> Iterator[Sequence[Mapping[str, Any]]]:
    log = log.bind(**hide_dsn_password(dsn))
    log.info("connecting to db")
    try:
        connection = connect(**dsn)
    except:
        log.exception("failed to connect to db")
        raise
    with connection:
        try:
            cursor = connection.cursor(cursor_factory=RealDictCursor)
        except:
            log.exception("failed to prepare query execution")
            raise
        with cursor:
            log.info("executing query", query=query, parameters=parameters)
            try:
                cursor.execute(query, parameters)
            except:
                log.exception("failed to execute query")
                raise

            log = log.bind(fetch_size=fetch_size)
            log.info("fetching rows")
            fetches_count = 0
            rows_count = 0
            while True:
                try:
                    rows = cursor.fetchmany(fetch_size)
                    fetches_count += 1
                except:
                    log.exception("failed to fetch rows", fetches=fetches_count)
                    raise

                if rows:
                    rows_count += len(rows)
                    yield rows
                else:
                    log.info(
                        "fetched rows",
                        rows=rows_count,
                        fetches=fetches_count - 1,
                    )
                    break
