from datetime import datetime
from itertools import chain
from typing import Any, Mapping, Optional, Sequence

from sqlalchemy import text
from sqlalchemy.orm import Session
from structlog.stdlib import BoundLogger, get_logger

from metaloader_rest_api.common_model import LAST_DATE

_DEFAULT_LOGGER = get_logger(__name__)


def merge(
    session: Session,
    version_id: Optional[int],
    effective_date: datetime,
    table: str,
    stage: str,
    primary_key: Optional[str],
    keys: Sequence[str],
    values: Sequence[str],
    others: Optional[Sequence[str]] = None,
    filter_expression: Optional[str] = None,
    params: Optional[Mapping[str, Any]] = None,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    log = log.bind(
        action="etl_merge",
        table=table,
        stage=stage,
        version_id=version_id,
        effective_date=effective_date,
    )
    log.info(
        "begin",
        primary_key=primary_key,
        keys=keys,
        values=values,
        others=others,
        filter_expression=filter_expression,
        params=params,
    )

    if primary_key is None:
        primary_key = ""

    if others is None:
        others = []

    if params is None:
        params = {}

    columns = list(chain(keys, values, others))

    session.execute(
        statement=text(f"""
              WITH s
                AS (SELECT *
                      FROM (SELECT {f"t.{primary_key}," if primary_key else ""}
                                   {", ".join(f"t.{column} t_{column}" for column in columns)}
                                 , {", ".join(f"s.{column} s_{column}" for column in columns)}
                                 , CASE
                                   WHEN t.{keys[0]} IS NULL
                                   THEN 'I'
                                   WHEN s.{keys[0]} IS NULL
                                    AND t.deleted_flg IS FALSE
                                   THEN 'D'
                                   WHEN s.{keys[0]} IS NOT NULL
                                    AND ({" OR ".join(f"t.{value} IS DISTINCT FROM s.{value}" for value in values)
                                         if values else "FALSE"}
                                      OR t.deleted_flg IS TRUE)
                                   THEN 'U'
                                    END _action_flg
                              FROM (SELECT *
                                      FROM {table}
                                     WHERE effective_to_dttm = :effective_to_dttm
                                    {f"AND ({filter_expression})" if filter_expression else ""}) t
                         FULL JOIN {stage} s
                                ON {" AND ".join(f"s.{key} = t.{key}" for key in keys)}) s
                     WHERE _action_flg IS NOT NULL)
                 , u
                AS (UPDATE {table} t
                       SET effective_to_dttm = :effective_from_dttm
                      FROM s
                     WHERE s._action_flg != 'I'
                       AND t.effective_to_dttm = :effective_to_dttm
                       AND {f"t.{primary_key} = s.{primary_key}"
                            if primary_key else 
                            " AND ".join(chain(
                                (f"t.{key} = s.t_{key}" for key in keys),
                                ((f"({filter_expression})",) if filter_expression else ()),
                            ))}
                 RETURNING NULL)
            INSERT INTO {table} ({primary_key}{"," if primary_key else ""}
                                 {", ".join(columns)}
                                 {", version_rk" if version_id is not None else ""}
                               , effective_from_dttm
                               , effective_to_dttm
                               , deleted_flg)
                 SELECT {f"NEXTVAL('metamodel.md_seq') {primary_key}," if primary_key else ""}
                        {", ".join(f"s_{column} {column}" for column in columns)}
                        {", :version_rk" if version_id is not None else ""}
                      , :effective_from_dttm effective_from_dttm
                      , :effective_to_dttm   effective_to_dttm
                      , FALSE                deleted_flg
                   FROM s
                  WHERE _action_flg = 'I'
                  UNION ALL
                 SELECT {primary_key}{"," if primary_key else ""}
                        {", ".join(f"t_{column} {column}" for column in columns)}
                        {", :version_rk" if version_id is not None else ""}
                      , :effective_from_dttm effective_from_dttm
                      , :effective_to_dttm   effective_to_dttm
                      , TRUE                 deleted_flg
                   FROM s
                  WHERE _action_flg = 'D' 
                  UNION ALL
                 SELECT {primary_key}{"," if primary_key else ""}
                        {", ".join(f"s_{column} {column}" for column in columns)}
                        {", :version_rk" if version_id is not None else ""}
                      , :effective_from_dttm effective_from_dttm
                      , :effective_to_dttm   effective_to_dttm
                      , FALSE                deleted_flg
                   FROM s
                  WHERE _action_flg = 'U'
        """),
        params={
            "version_rk": version_id,
            "effective_from_dttm": effective_date,
            "effective_to_dttm": LAST_DATE,
            **params,
        },
    )
    log.info("end")


def merge_without_history(
    session: Session,
    table: str,
    stage: str,
    primary_key: Optional[str],
    keys: Sequence[str],
    values: Sequence[str],
    others: Optional[Sequence[str]] = None,
    filter_expression: Optional[str] = None,
    sequence: Optional[str] = None,
    params: Optional[Mapping[str, Any]] = None,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    log = log.bind(
        action="etl_merge_without_history",
        table=table,
        stage=stage,
    )
    log.info(
        "begin",
        primary_key=primary_key,
        keys=keys,
        values=values,
        others=others,
        sequence=sequence,
        filter_expression=filter_expression,
        params=params,
    )

    if primary_key is None:
        primary_key = ""

    if others is None:
        others = []

    if params is None:
        params = {}

    if sequence is None:
        sequence = "metamodel.md_seq"

    columns = list(chain(keys, values, others))

    session.execute(
        statement=text(f"""
                   WITH s
                     AS (SELECT *
                           FROM (SELECT {f"t.{primary_key}," if primary_key else ""}
                                        {", ".join(f"t.{column} t_{column}" for column in columns)}
                                      , {", ".join(f"s.{column} s_{column}" for column in columns)}
                                      , CASE
                                        WHEN t.{keys[0]} IS NULL
                                        THEN 'I'
                                        WHEN s.{keys[0]} IS NULL
                                         AND t.deleted_flg IS FALSE
                                        THEN 'D'
                                        WHEN s.{keys[0]} IS NOT NULL
                                         AND ({" OR ".join(f"t.{value} IS DISTINCT FROM s.{value}" for value in values)
                                              if values else "FALSE"}
                                           OR t.deleted_flg IS TRUE)
                                        THEN 'U'
                                         END _action_flg 
                                   FROM (SELECT *
                                           FROM {table}
                                       {f"WHERE {filter_expression}" if filter_expression else ""}) t
                              FULL JOIN {stage} s
                                     ON {" AND ".join(f"s.{key} = t.{key}" for key in keys)}) s
                          WHERE _action_flg IS NOT NULL)
                      , u AS (UPDATE {table} t
                                 SET {", ".join(f"{column} = s.s_{column}" for column in columns)}
                                   , deleted_flg = FALSE
                                FROM s
                               WHERE s._action_flg = 'U'
                                 AND {" AND ".join(f"t.{key} = s.t_{key}" for key in keys)}
                           RETURNING NULL)
                      , d AS (UPDATE {table} t
                                 SET deleted_flg = TRUE
                                FROM s
                               WHERE s._action_flg = 'D'
                                 AND {" AND ".join(f"t.{key} = s.t_{key}" for key in keys)}
                           RETURNING NULL)
            INSERT INTO {table} ({primary_key}{"," if primary_key else ""}
                                 {", ".join(columns)}
                               , deleted_flg)
                 SELECT {f"NEXTVAL('{sequence}') {primary_key}," if primary_key else ""}
                        {", ".join(f"s_{column} {column}" for column in columns)}
                      , FALSE deleted_flg
                   FROM s
                  WHERE _action_flg = 'I'
        """),
        params=params,
    )
    log.info("end")
