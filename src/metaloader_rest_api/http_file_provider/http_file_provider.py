from typing import Iterator, Optional
from html.parser import <PERSON><PERSON><PERSON><PERSON><PERSON>

from httpx import <PERSON><PERSON><PERSON><PERSON>rror, Response
from structlog.stdlib import <PERSON><PERSON><PERSON>og<PERSON>, get_logger

from metaloader_rest_api.file_provider.file_provider import (
    FileProvider,
    FileProviderCheckError,
    FileProviderError,
)
from metaloader_rest_api.http.http_client import http_client

_DEFAULT_LOGGER = get_logger(__name__)


class HttpFileProvider(FileProvider):
    default_timeout = 5.0
    default_retries = 3

    _skip_statuses = {404, 410}

    def __init__(
        self,
        url: str,
        timeout: float = default_timeout,
        retries: int = default_retries,
        chunk_size: Optional[int] = None,
        log: BoundLogger = _DEFAULT_LOGGER,
    ):
        self._client = http_client(
            url=url,
            timeout=timeout,
            retries=retries,
        )
        self._url = url
        self._chunk_size = chunk_size

        self._log = log.bind(actor="http_file_provider", url=url)
        self._log.info(
            "init",
            timeout=timeout,
            retries=retries,
            chunk_size=chunk_size,
        )

    def __enter__(self):
        self._log.info("connect")

        self._client.__enter__()

        return self

    def __exit__(
        self,
        exc_type,
        exc_value,
        traceback,
    ):
        self._log.info("disconnect")

        self._client.__exit__(exc_type, exc_value, traceback)

    def check(self, path: str = "/") -> None:
        try:
            self._client.head(path).raise_for_status()
        except HTTPError as error:
            raise FileProviderCheckError(f"URL {self._url}/{path} not found") from error

    def get(self, path: str) -> Optional[Iterator[bytes]]:
        response = self._client.get(path)
        if self._check_status(response):
            return None
        return response.iter_bytes(self._chunk_size)

    def list_dir_items(self, path: str) -> Iterator[str]:
        # NOTE: файловый сервер может быть сконфигурирован так, что если в конце пути, который
        # указывает на папку нет слэша, он может сделать редирект, вернуть ошибку или вовсе закрыть
        # соединение. Всегда добавлять слэш -- чуть более безопасная опция.
        # Чтобы совсем обезопаситься, можно проверять оба пути -- со слэшом и без.
        if not path.endswith("/"):
            path += "/"

        response = self._client.get(path)
        _path = f"{self._url}/{path}"

        try:
            response.raise_for_status()
        except HTTPError as error:
            raise FileProviderCheckError(f"URL {_path} not found") from error

        if response.headers["Content-Type"] != "text/html":
            raise FileProviderError(f"URL {_path} is not a directory")

        parsed_items = _parse_html_directory_items(response.text)
        return iter(parsed_items)

    def _check_status(self, response: Response) -> bool:
        if response.status_code in self._skip_statuses:
            return True
        response.raise_for_status()
        return False


class _SimpleHtmlDirParser(HTMLParser):
    """Примитивный парсер содержимого директории, представленного в виде HTML

    Например, `<a href="/d/subdir/">subdir</a></>`.
    """

    def __init__(self):
        super().__init__()
        self.directory_items: list[str] = []

        self._current_href: Optional[str] = None
        self._current_text: Optional[str] = None

    def handle_starttag(self, tag: str, attrs: list[tuple]) -> None:
        if tag != "a":
            return

        self._current_href = self._current_text = None

        for attr_name, attr_value in attrs:
            if attr_name == "href":
                self._current_href = attr_value
                break

    def handle_endtag(self, tag: str) -> None:
        if tag != "a":
            return

        name_from_href = self._get_last_path_entry(self._current_href)
        name_from_href_stripped = name_from_href.rstrip("/")
        current_text = self._current_text.strip(" /")
        if (
            name_from_href
            and name_from_href_stripped not in (".", "..")
            and current_text.endswith(name_from_href_stripped)
        ):
            self.directory_items.append(name_from_href)

        self._current_href = self._current_text = None

    def handle_data(self, data: str) -> None:
        self._current_text = data

    @staticmethod
    def _get_last_path_entry(path: str) -> Optional[str]:
        if not path or path == "/":
            return path

        start_idx = -2 if path.endswith("/") else -1
        for idx in range(start_idx, -len(path), -1):
            if path[idx] == "/":
                return path[idx + 1 :]
        return path


def _parse_html_directory_items(html_content: str) -> list[str]:
    parser = _SimpleHtmlDirParser()
    parser.feed(html_content)
    return parser.directory_items
