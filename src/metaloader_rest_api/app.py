import io
import time
from datetime import datetime
from pathlib import Path as FilePath
from types import SimpleNamespace
from typing import Any, Optional, Sequence
from uuid import UUID

import kombu.exceptions
import prometheus_client
import pydantic
import structlog
from celery.result import AsyncResult
from fastapi import (
    BackgroundTasks,
    Body,
    Depends,
    FastAPI,
    File,
    HTTPException,
    Path,
    Query,
    Request,
    Response,
    UploadFile,
    status,
)
from fastapi.openapi.docs import (
    get_redoc_html,
    get_swagger_ui_html,
    get_swagger_ui_oauth2_redirect_html,
)
from fastapi.responses import JSONResponse
from sqlalchemy.exc import NoResultFound
from sqlalchemy.orm import Session
from starlette.staticfiles import StaticFiles
from uvicorn.protocols.utils import get_path_with_query_string

from metaloader_rest_api import (
    models,
    prometheus_metrics,
    resource_providers_resource_loader,
    schemas,
)
from metaloader_rest_api.ceh_module import ceh_module_api
from metaloader_rest_api.ceh_resource import (
    ceh_resource_legacy_model,
    ceh_resource_legacy_repository,
)
from metaloader_rest_api.ceh_service import ceh_service_api
from metaloader_rest_api.ceh_version import ceh_version_api
from metaloader_rest_api.celery_tasks.app_factory import get_celery_app
from metaloader_rest_api.data_model import data_model_api
from metaloader_rest_api.dev_kit import converter_excel
from metaloader_rest_api.dev_kit.converter_excel import LoadLogicalDataModelDemoFile
from metaloader_rest_api.flow import flow_loader_api
from metaloader_rest_api.flow_processor import flow_processor_api
from metaloader_rest_api.gzip.route import GzipRoute
from metaloader_rest_api.helpers import JsonDict
from metaloader_rest_api.loader_info_system.services.handler import (
    create_tx_for_load_system_info,
)
from metaloader_rest_api.logging import (
    get_metamodel_log_kwargs,
    pass_metamodel_log_kwargs,
    setup_logging,
)
from metaloader_rest_api.logic import (
    CELERY_QUEUE_PARALLEL,
    create_resource_actions_staging_table,
    create_tx,
    ensure_transaction_is_opened,
    execute_transactions_timeout,
    get_resource_transaction_info_response,
    get_tx,
    query_transaction,
    send_task,
    tx_manager,
    update_transaction_instance_on_resolution,
)
from metaloader_rest_api.master_flow import (
    master_flow_item_loader_api,
    master_flow_loader_api,
)
from metaloader_rest_api.models import SessionFactory, now_at_utc
from metaloader_rest_api.resource_repository import (
    ResourceStageSequentialRepository,
    resource_exists,
)
from metaloader_rest_api.resource_schemas import CehResource, UniResource
from metaloader_rest_api.settings import get_loggable_settings, read_settings

settings = read_settings()

setup_logging(log_format=settings.log_format, log_level=settings.log_level)
app_logger = structlog.stdlib.get_logger(__name__)
error_logger = structlog.stdlib.get_logger("api.error")
app_logger.info("Settings read", **get_loggable_settings(settings))
app = FastAPI(docs_url=None, redoc_url=None)
celery_client = get_celery_app(settings)
get_session = SessionFactory(database_url=settings.database_url.unicode_string())
app.mount(
    path="/static",
    app=StaticFiles(
        directory=str(FilePath(__file__).parent.resolve() / "static"),
    ),
    name="static",
)
app.router.route_class = GzipRoute  # noqa


# noinspection PyUnreachableCode
if True:  # TO-DO: поменять, когда будем готовы зарелизить аутентификацию
    # if settings.auth_disabled:
    auth = SimpleNamespace(
        with_roles=lambda *args, **kwargs: lambda: None,
    )
else:
    auth = Authorization(  # noqa
        oauth_token_url=str(settings.oauth_token_url),
        rs256_public_key=settings.rs256_public_key,
        public_roles=settings.public_roles.split(","),
    )
auth_with_public_role = auth.with_roles()
auth_with_modify_role = auth.with_roles("modify_role")


def yield_session():
    db = get_session()
    try:
        yield db
    finally:
        db.close()


@app.middleware("http")
async def logging_middleware(request: Request, call_next) -> Response:
    structlog.contextvars.clear_contextvars()

    start_time = time.perf_counter_ns()
    # If the call_next raises an error, we still want to return our own 500 response,
    # so we can add headers to it (process time, request ID...)
    response = Response(status_code=500)
    try:
        response = await call_next(request)
    except Exception:
        error_logger.exception("Uncaught exception")
        raise
    finally:
        process_time = time.perf_counter_ns() - start_time
        status_code = response.status_code
        url = get_path_with_query_string(request.scope)  # noqa
        client_host = request.client.host
        client_port = request.client.port
        http_method = request.method
        http_version = request.scope["http_version"]
        kwargs = {
            "http.url": str(request.url),
            "http.status_code": status_code,
            "http.method": http_method,
            "http.version": http_version,
            "tcp.remote.ip": client_host,
            "tcp.remote.port": client_port,
            "duration": process_time,
        }
        if auth_err_msg := getattr(request.state, "auth_err_msg", None):
            kwargs["http.auth_err_msg"] = auth_err_msg
        kwargs.update(get_metamodel_log_kwargs(request))
        app_logger.info(
            f"""{client_host}:{client_port} - "{http_method} {url} HTTP/{http_version}" {status_code}""",
            **kwargs,
        )
        return response


@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    # noinspection PyUnresolvedReferences
    return get_swagger_ui_html(
        openapi_url=app.openapi_url,
        title=app.title + " - Swagger UI",
        oauth2_redirect_url=app.swagger_ui_oauth2_redirect_url,
        swagger_js_url="/static/swagger-ui-bundle.js",
        swagger_css_url="/static/swagger-ui.css",
    )


# noinspection PyUnresolvedReferences
@app.get(app.swagger_ui_oauth2_redirect_url, include_in_schema=False)
async def swagger_ui_redirect():
    return get_swagger_ui_oauth2_redirect_html()


@app.get("/redoc", include_in_schema=False)
async def redoc_html():
    # noinspection PyUnresolvedReferences
    return get_redoc_html(
        openapi_url=app.openapi_url,
        title=app.title + " - ReDoc",
        redoc_js_url="/static/redoc.standalone.js",
    )


@app.get("/metrics", include_in_schema=False)
async def metrics():
    prometheus_metrics.set_heartbit(celery_client)
    return Response(
        content=prometheus_client.generate_latest(),
        media_type=prometheus_client.CONTENT_TYPE_LATEST,
    )


@app.get(
    "/v1/tx/{tx_uid}",
    dependencies=[Depends(auth_with_public_role)],
)
def get_transaction_info(
    tx_uid: UUID,
    db: Session = Depends(yield_session),
) -> schemas.TransactionInfo:
    if tx := get_tx(db, tx_uid):
        return tx
    raise HTTPException(status_code=404, detail="Transaction not found")


@app.put(
    "/v1/versions",
    dependencies=[Depends(auth_with_modify_role)],
)
def put_version(
    params: ceh_version_api.VersionParam,
    db_session: Session = Depends(yield_session),
) -> UUID:
    with tx_manager(
        db_session,
        app_logger,
        "put_version",
        params,
    ) as (load_id, effective_date):
        ceh_version_api.put_version(
            session=db_session,
            load_id=load_id,
            effective_date=effective_date,
            params=params,
            log=app_logger,
        )
        return load_id


@app.post(
    "/v1/resources/tx",
    dependencies=[Depends(auth_with_modify_role)],
)
def create_resource_transaction(
    background_tasks: BackgroundTasks,
    request: Request,
    release_metadata: schemas.ReleaseMetadata,
    timeout: int = Body(default=60 * 30, gte=0, lt=60 * 60 * 24),
    db: Session = Depends(yield_session),
) -> UUID:
    transaction = models.ResourceTransaction(
        status=models.TransactionStatus.OPENED.value,
        timeout_sec=timeout,
        request=release_metadata.model_dump(exclude_defaults=True),
    )
    db.add(transaction)
    db.commit()

    create_resource_actions_staging_table(db, transaction.id)
    db.commit()

    pass_metamodel_log_kwargs(request, tx_uid=str(transaction.id))

    background_tasks.add_task(
        execute_transactions_timeout, get_session, app_logger, None
    )

    prometheus_metrics.set_non_resolved_transactions(db)

    return transaction.id


@app.post(
    "/v1/resources/tx/{tx_uid}/rollback",
    dependencies=[Depends(auth_with_modify_role)],
)
def rollback_resource_transaction(
    request: Request,
    tx_uid: UUID,
    db: Session = Depends(yield_session),
) -> None:
    pass_metamodel_log_kwargs(request, tx_uid=str(tx_uid))

    transaction = query_transaction(tx_uid, models.ResourceTransaction, db, request)
    ensure_transaction_is_opened(transaction, request)

    update_transaction_instance_on_resolution(
        transaction=transaction,
        status=schemas.TransactionStatus.ROLLEDBACK,
    )
    db.add(transaction)
    db.commit()


@app.post(
    "/v1/resources/tx/{tx_uid}/commit",
    dependencies=[Depends(auth_with_modify_role)],
)
def commit_resource_transaction(
    request: Request,
    tx_uid: UUID,
    mode: schemas.TransactionMode = Body(..., embed=True),
    db: Session = Depends(yield_session),
) -> Any:
    pass_metamodel_log_kwargs(
        request,
        tx_uid=str(tx_uid),
        tx_mode=mode.value,
    )

    transaction = query_transaction(tx_uid, models.ResourceTransaction, db, request)
    ensure_transaction_is_opened(transaction, request)

    try:
        task_result: AsyncResult = celery_client.send_task(
            name="run_resources_integration_etl",
            kwargs=dict(
                tx_uid=str(tx_uid),
            ),
            queue="sequential",
        )
        pass_metamodel_log_kwargs(
            request,
            celery_task_id=task_result.task_id,
        )
    except kombu.exceptions.OperationalError as err:
        pass_metamodel_log_kwargs(request, error=str(err))
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"detail": "Failed to schedule task. Please try again later."},
        )

    prometheus_metrics.set_transaction_resource_actions(
        db, tx_uid, endpoint_name="commit"
    )

    return Response(
        status_code=status.HTTP_202_ACCEPTED,
        headers={"Location": f"/v1/resources/tx/{tx_uid}"},
    )


@app.get(
    "/v1/resources/tx/{tx_uid}",
    dependencies=[Depends(auth_with_public_role)],
)
def get_resource_transaction_info(
    request: Request,
    tx_uid: UUID,
    db: Session = Depends(yield_session),
) -> schemas.ResourceTransactionInfo:
    pass_metamodel_log_kwargs(request, tx_uid=str(tx_uid))
    transaction = query_transaction(tx_uid, models.ResourceTransaction, db, request)
    transaction_info = get_resource_transaction_info_response(db, transaction)
    return transaction_info


_responses__create_resource_action = {
    status.HTTP_409_CONFLICT: {"model": schemas.ResourceActionErrorDetails},
}
_description__create_resource_action = (
    "В рамках одной транзакции можно добавить только одно действие для каждого "
    "уникального resource_cd.\n"
    "Ожидается, что resource_cd в json-определении и в теле запроса совпадают. В противном случае "
    "при загрузке в метамодель будет использовано значение из json-определения."
)


@app.post(
    "/v1/resources/actions/added",
    status_code=status.HTTP_200_OK,
    responses=_responses__create_resource_action,
    dependencies=[Depends(auth_with_modify_role)],
    description=_description__create_resource_action,
)
@app.post(
    "/v1/resources/actions/modified",
    status_code=status.HTTP_200_OK,
    responses=_responses__create_resource_action,
    dependencies=[Depends(auth_with_modify_role)],
    description=_description__create_resource_action,
)
def create_resource_action_added_or_modified(
    request: Request,
    tx_uid: UUID = Body(...),
    resource_cd: str = Body(..., min_length=1),
    resource_type: schemas.ResourceType = Body(...),
    definition: JsonDict = Body(...),
    db: Session = Depends(yield_session),
) -> Any:
    pass_metamodel_log_kwargs(
        request,
        tx_uid=str(tx_uid),
        resource_cd=resource_cd,
        resource_type=resource_type.value,
    )

    action_type = (
        models.ResourceAction.ADD
        if request.url.path.endswith("/added")
        else models.ResourceAction.MODIFY
    )
    resource_schema_cls, db_resource_type = {
        schemas.ResourceType.CEH: (CehResource, models.ResourceType.CEH),
        schemas.ResourceType.UNI: (UniResource, models.ResourceType.UNI),
    }[resource_type]

    try:
        resource_schema_cls(**definition)
    except pydantic.ValidationError as err:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=err.errors(),
        )

    payload_resource_cd = definition.get("resource_cd")
    if payload_resource_cd and payload_resource_cd != resource_cd:
        app_logger.warning(
            "resource_cd in the json definition and in the body don't match",
            body_resource_cd=resource_cd,
            json_resource_cd=payload_resource_cd,
        )
    resource_cd = payload_resource_cd

    transaction = query_transaction(tx_uid, models.ResourceTransaction, db, request)
    ensure_transaction_is_opened(transaction, request)

    resource_exists_ = resource_exists(db, resource_cd)
    if action_type is models.ResourceAction.ADD and resource_exists_:
        pass_metamodel_log_kwargs(request, warn="resource already exists")
    elif action_type is models.ResourceAction.MODIFY and not resource_exists_:
        pass_metamodel_log_kwargs(request, warn="resource doesn't exist")

    repo = ResourceStageSequentialRepository(session=db, load_id=transaction.id)
    existing_action = repo.add_resource_action(
        resource_cd=resource_cd,
        action=action_type,
        resource_type=db_resource_type,
        definition=definition,
    )
    db.commit()
    if existing_action:
        return _409_resource_action_already_exists(resource_cd, existing_action)


@app.post(
    "/v1/resources/actions/deleted",
    status_code=status.HTTP_200_OK,
    responses=_responses__create_resource_action,
    dependencies=[Depends(auth_with_modify_role)],
    description=_description__create_resource_action,
)
def create_resource_action_deleted(
    request: Request,
    tx_uid: UUID = Body(...),
    resource_cd: str = Body(..., min_length=1),
    db: Session = Depends(yield_session),
) -> Any:
    pass_metamodel_log_kwargs(
        request,
        tx_uid=str(tx_uid),
        resource_cd=resource_cd,
    )

    transaction = query_transaction(tx_uid, models.ResourceTransaction, db, request)
    ensure_transaction_is_opened(transaction, request)

    if not resource_exists(db, resource_cd):
        pass_metamodel_log_kwargs(request, warn="resource doesn't exist")

    repo = ResourceStageSequentialRepository(session=db, load_id=transaction.id)
    existing_action = repo.add_resource_action(
        resource_cd=resource_cd,
        action=models.ResourceAction.DELETE,
    )
    db.commit()
    if existing_action:
        return _409_resource_action_already_exists(resource_cd, existing_action)


@app.post(
    "/v1/resources/load_from_providers",
    dependencies=[Depends(auth_with_modify_role)],
)
def load_resources_from_providers(
    params: resource_providers_resource_loader.LoadResourcesFromProvidersParams,
    db_session: Session = Depends(yield_session),
) -> UUID:
    return send_task(
        "load_resources_from_providers",
        params,
        CELERY_QUEUE_PARALLEL,
        celery_client,
        db_session,
        app_logger,
    )


@app.get(
    "/v1/resources/legacy/{type}/{effective_date}/names",
    tags=["resource"],
    description="Resource names",
)
def get_resource_names_legacy(
    type: ceh_resource_legacy_model.ResourceType = Path(...),
    effective_date: datetime = Path(
        ...,
        description="Date (timestamp) after which the resources were affected",
    ),
    offset: int = Query(..., ge=0),
    limit: int = Query(default=ceh_resource_legacy_repository.DEFAULT_LIMIT, gt=0),
    order: ceh_resource_legacy_model.ResourceOrder = Query(
        default=ceh_resource_legacy_repository.DEFAULT_ORDER
    ),
    db_session: Session = Depends(yield_session),
) -> Sequence[str]:
    return ceh_resource_legacy_repository.names(
        session=db_session,
        type=type,
        effective_date=effective_date,
        offset=offset,
        limit=limit,
        order=order,
    )


@app.get(
    "/v1/resources/legacy/{type}/effective_date",
    tags=["resource"],
    description="Resources effective date (timestamp)",
)
def get_resource_effective_date_legacy(
    type: ceh_resource_legacy_model.ResourceType = Path(...),
    db_session: Session = Depends(yield_session),
) -> Optional[datetime]:
    return ceh_resource_legacy_repository.effective_date(
        session=db_session,
        type=type,
    )


@app.get(
    "/v1/resources/legacy/{name}",
    tags=["resource"],
    description="Resource",
    responses={
        404: {"description": "Resource not found"},
        410: {"description": "Resource deleted"},
    },
)
def get_resource_legacy(
    name: str = Path(...),
    db_session: Session = Depends(yield_session),
) -> Any:
    try:
        resource = ceh_resource_legacy_repository.resource(
            session=db_session,
            name=name,
        )
    except NoResultFound:
        raise HTTPException(
            status_code=404,
            detail=f"Resource '{name}' not found",
        )
    if resource is None:
        raise HTTPException(
            status_code=410,
            detail=f"Resource '{name}' deleted",
        )
    return resource


@app.post(
    "/v1/flows/load",
    dependencies=[Depends(auth_with_modify_role)],
)
def load_flows(
    params: flow_loader_api.LoadFlowsParams,
    db_session: Session = Depends(yield_session),
) -> UUID:
    return send_task(
        "load_flows",
        params,
        CELERY_QUEUE_PARALLEL,
        celery_client,
        db_session,
        app_logger,
    )


@app.post(
    "/v1/flows/load/all",
    dependencies=[Depends(auth_with_modify_role)],
)
def load_all_flows(
    params: flow_loader_api.LoadFlowsParams,
    db_session: Session = Depends(yield_session),
) -> UUID:
    return send_task(
        "load_all_flows",
        params,
        CELERY_QUEUE_PARALLEL,
        celery_client,
        db_session,
        app_logger,
    )


@app.post(
    "/v1/flows/masters/load",
    dependencies=[Depends(auth_with_modify_role)],
)
def load_master_flows(
    params: master_flow_loader_api.LoadMasterFlowsParams,
    db_session: Session = Depends(yield_session),
) -> UUID:
    return send_task(
        "load_master_flows",
        params,
        CELERY_QUEUE_PARALLEL,
        celery_client,
        db_session,
        app_logger,
    )


@app.post(
    "/v1/flows/masters/load/all",
    dependencies=[Depends(auth_with_modify_role)],
)
def load_all_master_flows(
    params: master_flow_loader_api.LoadMasterFlowsParams,
    db_session: Session = Depends(yield_session),
) -> UUID:
    return send_task(
        "load_all_master_flows",
        params,
        CELERY_QUEUE_PARALLEL,
        celery_client,
        db_session,
        app_logger,
    )


@app.post(
    "/v1/flows/masters/items/load",
    dependencies=[Depends(auth_with_modify_role)],
)
def load_mster_flow_items(
    params: master_flow_item_loader_api.LoadMasterFlowItemsParams,
    db_session: Session = Depends(yield_session),
) -> UUID:
    return send_task(
        "load_master_flow_items",
        params,
        CELERY_QUEUE_PARALLEL,
        celery_client,
        db_session,
        app_logger,
    )


@app.post(
    "/v1/flows/process",
    dependencies=[Depends(auth_with_modify_role)],
)
def process_flows(
    params: flow_processor_api.ProcessFlowsParams,
    db_session: Session = Depends(yield_session),
) -> UUID:
    return send_task(
        "process_flows",
        params,
        CELERY_QUEUE_PARALLEL,
        celery_client,
        db_session,
        app_logger,
    )


@app.post(
    "/v1/sources/load_from_xlsx",
    summary="Загрузка шаблона stg_bridge_source",
    description="""
    Выполняет загрузку информации по системам и подсистемам
    """,
    dependencies=[Depends(auth_with_modify_role)],
)
def load_sources_from_xlsx(
    xlsx_file: UploadFile = File(
        ...,
        title="Обязательный xlsx",
        description="Ожидается шаблон stg_bridge_source",
    ),
    db: Session = Depends(yield_session),
    effective_from_dttm: Optional[datetime] = Body(default=""),
    background_tasks: BackgroundTasks = None,
) -> UUID:
    tx_id, task, task_args = create_tx_for_load_system_info(
        db,
        xlsx_file.filename,
        xlsx_file.file.read(),
    )

    if effective_from_dttm == "" or effective_from_dttm is None:
        effective_from_dttm = now_at_utc()

    task_args.append(effective_from_dttm)

    background_tasks.add_task(task, *task_args)

    return tx_id


@app.post(
    "/v1/module/load",
    dependencies=[Depends(auth_with_modify_role)],
)
def load_modules(
    params: ceh_module_api.LoadModulesParams,
    db_session: Session = Depends(yield_session),
) -> UUID:
    return send_task(
        "load_modules",
        params,
        CELERY_QUEUE_PARALLEL,
        celery_client,
        db_session,
        app_logger,
    )


@app.post(
    "/v1/data_model/load",
    dependencies=[Depends(auth_with_modify_role)],
)
def load_data_model(
    params: data_model_api.LoadDataModelParams,
    db_session: Session = Depends(yield_session),
) -> UUID:
    return send_task(
        "load_data_model",
        params,
        CELERY_QUEUE_PARALLEL,
        celery_client,
        db_session,
        app_logger,
    )


@app.post(
    "/v1/data_model/load_logical",
    dependencies=[Depends(auth_with_modify_role)],
)
def load_logical_data_model(
    params: data_model_api.LoadLogicalDataModelParams,
    db_session: Session = Depends(yield_session),
) -> UUID:
    return send_task(
        "load_logical_data_model",
        params,
        CELERY_QUEUE_PARALLEL,
        celery_client,
        db_session,
        app_logger,
    )


@app.post(
    "/v1/demo/data_model/excel_to_json",
    dependencies=[Depends(auth_with_modify_role)],
)
async def load_logical_data_model_demo(
    xlsx_file: UploadFile = File(...),
    data_layer: str = Body(...),
    db_session: Session = Depends(yield_session),
) -> UUID:
    file_content = await xlsx_file.read()
    file_io = io.BytesIO(file_content)

    params = LoadLogicalDataModelDemoFile(
        data_layer=data_layer, file_name=xlsx_file.filename, file_io=file_io
    )

    load_id, effective_date = create_tx(
        db_session, app_logger, "load_logical_data_model_demo"
    )
    log = app_logger.bind(load_id=load_id, effective_date=effective_date)

    json_data = converter_excel.transform_excel_to_json(
        params,
        log,
    )

    data_model_api.load_logical_data_model(
        db_session,
        load_id,
        effective_date,
        json_data,
        log,
    )

    return load_id


@app.post(
    "/v1/service/load",
    dependencies=[Depends(auth_with_modify_role)],
)
def load_services(
    params: ceh_service_api.LoadServicesParams,
    db_session: Session = Depends(yield_session),
) -> UUID:
    return send_task(
        "load_services",
        params,
        CELERY_QUEUE_PARALLEL,
        celery_client,
        db_session,
        app_logger,
    )


def _409_resource_action_already_exists(
    resource_cd: str, existing_action: models.ResourceAction
) -> JSONResponse:
    return JSONResponse(
        status_code=status.HTTP_409_CONFLICT,
        content=schemas.ResourceActionErrorDetails(
            msg="the transaction already has an action for this resource",
            resource_cd=resource_cd,
            existing_action={
                models.ResourceAction.ADD: schemas.ResourceAction.ADD,
                models.ResourceAction.MODIFY: schemas.ResourceAction.MODIFY,
                models.ResourceAction.DELETE: schemas.ResourceAction.DELETE,
            }[existing_action],
        ).model_dump(mode="json", exclude_unset=True),
    )
