from contextlib import contextmanager
from datetime import datetime, timezone
from traceback import format_exc
from typing import (
    Any,
    Callable,
    Dict,
    Iterator,
    Mapping,
    Optional,
    Tuple,
    Type,
    TypeVar,
    Union,
)
from uuid import UUID

import celery
from celery import Celery
from fastapi import HTTPException, Request
from pydantic import BaseModel
from sqlalchemy import text
from sqlalchemy.orm import Session
from structlog.stdlib import BoundLogger

from metaloader_rest_api import models, schemas
from metaloader_rest_api.logging import pass_metamodel_log_kwargs
from metaloader_rest_api.resource_repository import ResourceStageSequentialRepository

_TRANSACTION_STATUS_S2M = {
    schemas.TransactionStatus.OPENED: models.TransactionStatus.OPENED,
    schemas.TransactionStatus.COMMITTED: models.TransactionStatus.COMMITTED,
    schemas.TransactionStatus.ROLLEDBACK: models.TransactionStatus.ROLLEDBACK,
    schemas.TransactionStatus.IN_PROGRESS: models.TransactionStatus.IN_PROGRESS,
    schemas.TransactionStatus.ETL_ERROR: models.TransactionStatus.ETL_ERROR,
}
_TRANSACTION_STATUS_M2S = {v: k for k, v in _TRANSACTION_STATUS_S2M.items()}


CELERY_QUEUE_SEQUENTIAL = "sequential"
CELERY_QUEUE_PARALLEL = "parallel"
CELERY_QUEUE_DEFAULT = CELERY_QUEUE_PARALLEL


_ConcreteTransaction = TypeVar(
    "_ConcreteTransaction",
    bound=Union[models.TaskTransaction, models.ResourceTransaction],
)


def query_transaction(
    tx_uid: UUID,
    tx_type: Type[_ConcreteTransaction],
    db: Session,
    request: Request,
) -> _ConcreteTransaction:
    transaction = (
        db.query(tx_type).filter(tx_type.id == tx_uid).first()  # noqa
    )
    if not transaction:
        err_msg = "Transaction not found"
        pass_metamodel_log_kwargs(request, error=err_msg)
        raise HTTPException(status_code=404, detail=err_msg)

    return transaction


def ensure_transaction_is_opened(
    transaction: models.BaseTransaction, request: Request
) -> None:
    if transaction.status == models.TransactionStatus.OPENED.value:
        return

    try:
        tx_status = models.TransactionStatus(transaction.status).name.lower()
    except ValueError:
        tx_status = str(transaction.status)

    err_msg = "Attempting to modify a transaction that is not open"
    pass_metamodel_log_kwargs(request, tx_status=tx_status, error=err_msg)
    raise HTTPException(
        status_code=400,
        detail=err_msg,
    )


def update_transaction_instance_on_resolution(
    transaction: models.BaseTransaction,
    status: schemas.TransactionStatus,
) -> None:
    now = models.now_at_utc()

    transaction.resolved_at = now
    transaction.status_changed_at = now
    transaction.status = _TRANSACTION_STATUS_S2M[status].value


def get_resource_transaction_info_response(
    session: Session,
    transaction: models.ResourceTransaction,
) -> schemas.ResourceTransactionInfo:
    resource_cds = ResourceStageSequentialRepository(
        session=session,
        load_id=transaction.id,
    ).get_resource_cds()
    return schemas.ResourceTransactionInfo(
        uid=transaction.id,
        status=_TRANSACTION_STATUS_M2S[transaction.status],  # type: ignore
        release_metadata=schemas.ReleaseMetadata(**transaction.request),
        resources=resource_cds,
    )


def execute_transactions_timeout(
    get_session: Callable[[], Session],
    logger: BoundLogger,
    logger_kwargs: Optional[Dict[str, str]] = None,
) -> None:
    """Проставляет status=rolledback для всех транзакций, у которых превышено время таймаута

    Предполагается, что эта функция будет выполняться в отдельном треде тредпула.
    """
    with get_session() as session:
        # NOTE: Мы никак не проверяем, не выполняется ли всё ещё вью-функция
        # эндпойнта /commit или /try. Делаем допущение, что к моменту таймаута
        # уже она уже точно завершится.
        stmt = text(
            """
            UPDATE metamodel.service_transaction_status
            SET service_transaction_status_rk = :rolledback_status
            WHERE service_transaction_status_rk IN (:opened_status, :in_progress_status)
              AND timezone('UTC', now()) > (
                request_dttm + (transaction_timeout_duration || ' sec')::interval
              )
            RETURNING service_transaction_uid
            """
        )
        result = session.execute(
            statement=stmt,
            params={
                "rolledback_status": models.TransactionStatus.ROLLEDBACK.value,
                "opened_status": models.TransactionStatus.OPENED.value,
                "in_progress_status": models.TransactionStatus.IN_PROGRESS.value,
            },
        )
        session.commit()
        affected_rows_count = result.rowcount
    # TO-DO: убрать повторение логики переименования лог-аргументов
    kwargs = {
        "metamodel.tx_count": affected_rows_count,
    }
    if logger_kwargs:
        kwargs.update((f"metamodel.{k}", v) for k, v in logger_kwargs.items())  # type: ignore
    logger.info(
        f"Background task rolledback {affected_rows_count} transactions by timeout"
        if affected_rows_count
        else "No timedout transactions found",
        **kwargs,
    )


def create_resource_actions_staging_table(
    session: Session,
    tx_uid: UUID,
) -> None:
    repo = ResourceStageSequentialRepository(session=session, load_id=tx_uid)
    repo.drop_table()
    repo.create_table()


def send_task(
    name: str,
    params: BaseModel,
    queue: str,
    celery_client: celery.Celery,
    db_session: Session,
    log: BoundLogger,
) -> UUID:
    log = log.bind(action="send_task", name=name)
    log.info("begin", params=params)
    with init_tx_manager(
        db_session,
        log,
        name,
        params,
    ) as (load_id, effective_date):
        log = log.bind(load_id=load_id, effective_date=effective_date)
        log.info("send", queue=queue)
        celery_client.send_task(
            name=name,
            kwargs={
                "load_id": load_id,
                "effective_date": effective_date,
                "params": params,
            },
            queue=queue,
        )
    log.info("end")

    return load_id


def get_tx(
    db_session: Session,
    tx_id: UUID,
) -> Optional[schemas.TransactionInfo]:
    if tx := db_session.get(models.TaskTransaction, tx_id):
        return schemas.TransactionInfo(
            status=_TRANSACTION_STATUS_M2S[tx.status],
            **tx.request,
            **tx.response,
        )
    return None


@contextmanager
def tx_manager(
    db_session: Session,
    log: BoundLogger,
    task_name: str,
    task_params: BaseModel,
) -> Iterator[Tuple[UUID, datetime]]:
    description = None
    exception = None
    log = log.bind(action="manage_tx")
    log.info("begin")
    tx_id, effective_date = create_tx(
        db_session,
        log,
        task_name,
        task_params.model_dump(mode="json", exclude_defaults=True),
    )
    log = log.bind(tx_id=tx_id, effective_date=effective_date)
    try:
        process_tx(db_session, log, tx_id)
        yield tx_id, effective_date
    except Exception as exception_:
        log.exception("fail")
        description = format_exc()
        exception = exception_
    finally:
        close_tx(db_session, log, tx_id, description is not None, description)
        if exception:
            raise exception
        log.info("end")


@contextmanager
def init_tx_manager(
    db_session: Session,
    log: BoundLogger,
    task_name: str,
    task_params: BaseModel,
) -> Iterator[Tuple[UUID, datetime]]:
    log = log.bind(action="init_tx")
    log.info("begin")
    tx_id, effective_date = create_tx(
        db_session,
        log,
        task_name,
        task_params.model_dump(mode="json", exclude_defaults=True),
    )
    log = log.bind(tx_id=tx_id, effective_date=effective_date)
    try:
        yield tx_id, effective_date
    except Exception:
        log.exception("fail")
        close_tx(db_session, log, tx_id, True, format_exc())
        raise


@contextmanager
def process_tx_manager(
    db_session: Session,
    log: BoundLogger,
    tx_id: UUID,
) -> Iterator[None]:
    description = None
    exception = None
    log = log.bind(action="process_tx", tx_id=tx_id)
    log.info("begin")
    try:
        process_tx(db_session, log, tx_id)
        yield None
    except Exception as exception_:
        log.exception("fail")
        description = format_exc()
        exception = exception_
    finally:
        close_tx(db_session, log, tx_id, description is not None, description)
        if exception:
            raise exception
        log.info("end")


def create_tx(
    db_session: Session,
    log: BoundLogger,
    task: str,
    params: Optional[Mapping[str, Any]] = None,
) -> Tuple[UUID, datetime]:
    tx = models.TaskTransaction(
        status=models.TransactionStatus.OPENED.value,
        request={
            "task": task,
            "params": params,
        },
        response={},
    )
    db_session.add(tx)
    db_session.commit()
    log.info("create_tx", tx_ix=tx.id, effective_date=tx.created_at)

    return tx.id, tx.created_at


def close_tx(
    db_session: Session,
    log: BoundLogger,
    tx_id: UUID,
    rollback: bool = False,
    description: Optional[str] = None,
    actual_date: Optional[datetime] = None,
) -> None:
    set_tx_state(
        db_session=db_session,
        tx_id=tx_id,
        status=(
            models.TransactionStatus.ROLLEDBACK
            if rollback
            else models.TransactionStatus.COMMITTED
        ),
        description=description,
        actual_date=actual_date,
    )
    log.info("close_tx", rollback=rollback, message=description)


def process_tx(
    db_session: Session,
    log: BoundLogger,
    tx_id: UUID,
    actual_date: Optional[datetime] = None,
) -> None:
    set_tx_state(
        db_session=db_session,
        tx_id=tx_id,
        status=models.TransactionStatus.IN_PROGRESS,
        actual_date=actual_date,
    )
    log.info("process_tx")


def set_tx_state(
    db_session: Session,
    tx_id: UUID,
    status: models.TransactionStatus,
    description: Optional[str] = None,
    actual_date: Optional[datetime] = None,
) -> None:
    if actual_date is None:
        actual_date = datetime.now(tz=timezone.utc)

    (
        db_session.query(models.TaskTransaction)
        .filter(models.TaskTransaction.id == tx_id)
        .update(
            {
                models.TaskTransaction.status: status.value,
                models.TaskTransaction.status_changed_at: actual_date,
                models.TaskTransaction.response: {"description": description},
            }
        )
    )
    db_session.commit()


def check_celery_health(celery_client: Celery) -> bool:
    try:
        inspect = celery_client.control.inspect(timeout=1.0)  # noqa
        active_queues = inspect.active_queues()

        if active_queues is None:
            return False

        all_queues = {
            queue_info.get("name")
            for worker_queues in active_queues.values()
            if worker_queues
            for queue_info in worker_queues
        }
        return (
            CELERY_QUEUE_SEQUENTIAL in all_queues
            and CELERY_QUEUE_PARALLEL in all_queues
        )

    except Exception:
        return False
