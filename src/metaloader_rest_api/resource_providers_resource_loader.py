import itertools
from datetime import datetime
from typing import Iterator, Literal, Optional
from uuid import UUID

from pydantic import BaseModel
from sqlalchemy.orm import Session
from structlog.stdlib import BoundLogger, get_logger

from metaloader_rest_api.common_param import (
    DB_COMMIT,
    DB_FETCH_SIZE_DEFAULT,
    DB_PAGE_SIZE_DEFAULT,
    FAILSAFE_THRESHOLD_DEFAULT,
    HTTP_PAGE_SIZE_DEFAULT,
    HTTP_RETRIES_DEFAULT,
    HTTP_TIMEOUT_DEFAULT,
    DbOptions,
    DeploymentInfo,
    FailsafeOptions,
    HttpOptions,
)
from metaloader_rest_api.common_repository import SessionResource
from metaloader_rest_api.deployment import get_deployment
from metaloader_rest_api.helpers import <PERSON>son<PERSON><PERSON>
from metaloader_rest_api.models import ResourceAction
from metaloader_rest_api.resource_provider_client import ResourceProviderClient
from metaloader_rest_api.resource_repository import (
    ResourceRepository,
    ResourceStageBatchRepository,
)
from metaloader_rest_api.resource_repository._repository import StageResource

_DEFAULT_LOGGER = get_logger(__name__)


class ResourceProvidersResourceLoader:
    def __init__(
        self,
        uni_client: ResourceProviderClient,
        ceh_client: ResourceProviderClient,
        session: Session,
        session_resource: SessionResource,
        resource_stage_repository: ResourceStageBatchRepository,
        resource_repository: ResourceRepository,
        log: BoundLogger = _DEFAULT_LOGGER,
    ):
        self._log = log

        self._uni_client = uni_client
        self._ceh_client = ceh_client
        self._session = session
        self._session_resource = session_resource
        self._resource_stage_repository = resource_stage_repository
        self._resource_repository = resource_repository

    def load(
        self,
        module: str,
        version: str,
        effective_date: datetime,
        limit: Optional[int] = None,
    ) -> None:
        module_id, _, version_id = get_deployment(
            session=self._session,
            module=module,
            service=None,
            release=version,
            effective_date=effective_date,
            log=self._log,
        )
        self._log.info(
            "get_deployment",
            module_id=module_id,
            version_id=version_id,
            effective_date=effective_date,
        )

        # NOTE: БД-транзакция будет держаться открытой всё время с момента загрузки первого батча
        #  строк внутри resource_stage_repository и до конца загрузки всех ресурсов. Это значит, что
        #  мы надолго займём соединение в постгресе -- здесь потенциальная проблема с БД в будущем.
        with (
            self._session_resource("load_resources_stage"),
            self._resource_stage_repository as resource_stage_repository,
            self._uni_client,
            self._ceh_client,
        ):
            # NOTE: кол-во загруженных ресурсов будет на самом деле limit * 2, т.к. из каждого
            #  провайдера загрузим по limit ресурсов
            for resource_type, resource_cd, definition in _all_resources_iter(
                ceh_client=self._ceh_client,
                uni_client=self._uni_client,
                limit=limit,
            ):
                if definition is None:
                    self._log.warning(
                        "skip_deleted_resource",
                        resource_cd=resource_cd,
                        resource_type=resource_type,
                    )
                    continue

                stage_record = StageResource(
                    resource_cd=resource_cd,
                    action=ResourceAction.ADD.value,
                    resource_type=resource_type,
                    definition=definition,
                )
                resource_stage_repository.load_one(stage_record)

        with self._session_resource("load_resources"):
            self._resource_repository.load(
                version_id=version_id,
                effective_date=effective_date,
                stage_table=resource_stage_repository.table,
            )


class LoadResourcesFromProvidersParams(BaseModel):
    deployment: DeploymentInfo = DeploymentInfo()
    uni_provider_base_url: str
    ceh_provider_base_url: str
    limit: Optional[int] = None
    http_options: HttpOptions = HttpOptions()
    db_options: DbOptions = DbOptions()
    failsafe_options: FailsafeOptions = FailsafeOptions()


def load_resources_from_providers(
    session: Session,
    load_id: UUID,
    effective_date: datetime,
    params: LoadResourcesFromProvidersParams,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    return load_resources_from_resource_providers(
        session=session,
        load_id=load_id,
        module=params.deployment.module,
        release=params.deployment.release,
        effective_date=effective_date,
        uni_provider_base_url=params.uni_provider_base_url,
        ceh_provider_base_url=params.ceh_provider_base_url,
        limit=params.limit,
        log=log,
    )


def load_resources_from_resource_providers(
    session: Session,
    load_id: UUID,
    module: str,
    release: str,
    effective_date: datetime,
    uni_provider_base_url: str,
    ceh_provider_base_url: str,
    limit: Optional[int] = None,
    http_timeout: float = HTTP_TIMEOUT_DEFAULT,
    http_retries: int = HTTP_RETRIES_DEFAULT,
    http_page_size: int = HTTP_PAGE_SIZE_DEFAULT,
    db_fetch_size: int = DB_FETCH_SIZE_DEFAULT,
    db_page_size: int = DB_PAGE_SIZE_DEFAULT,
    db_commit: Optional[bool] = DB_COMMIT,
    fail_threshold: int = FAILSAFE_THRESHOLD_DEFAULT,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    log = log.bind(
        action="load_resources_from_providers",
        load_id=load_id,
        module=module,
        release=release,
        effective_date=effective_date,
    )
    log.info(
        "begin",
        http_timeout=http_timeout,
        http_retries=http_retries,
        http_page_size=http_page_size,
        db_fetch_size=db_fetch_size,
        db_page_size=db_page_size,
        db_commit=db_commit,
        fail_threshold=fail_threshold,
    )
    ResourceProvidersResourceLoader(
        uni_client=ResourceProviderClient(
            base_url=uni_provider_base_url,
            timeout=http_timeout,
            retries=http_retries,
            page_size=http_page_size,
            log=log,
        ),
        ceh_client=ResourceProviderClient(
            base_url=ceh_provider_base_url,
            timeout=http_timeout,
            retries=http_retries,
            page_size=http_page_size,
            log=log,
        ),
        session=session,
        session_resource=SessionResource(
            session=session,
            commit=db_commit,
            log=log,
        ),
        resource_stage_repository=ResourceStageBatchRepository(
            session=session,
            table_id=load_id.hex,
            page_size=db_page_size,
            log=log,
        ),
        resource_repository=ResourceRepository(
            session=session,
            log=log,
        ),
        log=log,
    ).load(
        module=module,
        version=release,
        effective_date=effective_date,
        limit=limit,
    )
    log.info("end")


def _all_resources_iter(
    uni_client: ResourceProviderClient,
    ceh_client: ResourceProviderClient,
    limit: Optional[int] = None,
) -> Iterator[tuple[Literal["uni", "ceh"], str, Optional[JsonDict]]]:
    ceh_iter, uni_iter = (
        itertools.islice(client.get_all_resources(), limit)
        for client in (ceh_client, uni_client)
    )
    for resource_cd, definition in ceh_iter:
        yield "ceh", resource_cd, definition
    for resource_cd, definition in uni_iter:
        yield "uni", resource_cd, definition
