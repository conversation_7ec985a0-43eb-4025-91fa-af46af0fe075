from typing import Any, <PERSON>era<PERSON>, Map<PERSON>, <PERSON><PERSON>

from structlog.stdlib import <PERSON><PERSON><PERSON><PERSON><PERSON>, get_logger

from metaloader_rest_api.yaml.yaml_loader import Yaml

_DEFAULT_LOGGER = get_logger(__name__)


def load_flow_param(
    flow_name: str,
    flow: Yaml,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> Iterator[Tuple[str, str, Any]]:
    if flow.get("type") == "WORK_FLOW":
        return

    schema_version = flow["schema_version"]
    if schema_version not in {"2.0", 2.0}:
        log.warning("schema_version", schema_version=schema_version, flow=flow_name)

    metadata = flow.get("metadata")
    if metadata:
        main_flows = {*flow.get("main_flows", ())}
    else:
        main_flows = None

    flows = flow["flows"]

    if main_flows:
        flows = (flow for flow in flows if flow["id"] in main_flows)
    else:
        flows = (flows[0],)

    for flow_ in flows:
        metadata = flow_.get("metadata")
        if not metadata:
            continue

        for metadata_ in metadata:
            if metadata_.get("type", "in") != "in":
                continue

            name = metadata_["name"]
            datatype = metadata_.get("datatype")
            default = metadata_.get("default")

            yield name, datatype, default


def load_flow_param_pass(
    flow_name: str,
    flow: Yaml,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> Iterator[Tuple[str, str, Any]]:
    if flow.get("type") == "WORK_FLOW":
        return

    schema_version = flow["schema_version"]
    if schema_version not in {"2.0", 2.0}:
        log.warning("schema_version", schema_version=schema_version, flow=flow_name)

    flows = flow["flows"]
    for flow_ in flows:
        tasks = flow_.get("tasks")
        if not tasks:
            tasks = flow_["steps"]
        for task in tasks:
            type = task.get("type")
            if type:
                if type in {
                    "ceh_core_idl.app.operators.core.trigger_dag_operator",
                    "ceh_core_idl.app.operators.core.sequential_trigger_dag_operator",
                }:
                    properties = task["properties"]
                    trigger_dag_id = properties["trigger_dag_id"]
                    conf = properties.get("conf")
                    if conf and isinstance(conf, Mapping):
                        for name, value in conf.items():
                            yield trigger_dag_id, name, value
                elif type in {
                    "ceh_core_libs.steps.core.trigger_job_step",
                    "ceh_core_libs.steps.core.sequential_trigger_dag_step",
                }:
                    properties = task["properties"]
                    job_id = properties["job_id"]
                    conf = properties.get("conf")
                    if conf and isinstance(conf, Mapping):
                        for name, value in conf.items():
                            yield job_id, name, value
            else:
                builder = task.get("builder")
                if builder == "ceh_core_idl.app.builders.include_flow_builder":
                    properties = task["properties"]
                    ref = properties["ref"]
                    if ref == "cf_ceh_template":
                        properties = properties["properties"]
                        work_flow_id = properties["work_flow_id"]
                        algorithm_uid = properties["algorithm_uid"]
                        yield work_flow_id, "init_load_flg", False
                        yield work_flow_id, "algorithm_uid", algorithm_uid
