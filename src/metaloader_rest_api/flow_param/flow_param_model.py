from enum import IntEnum
from typing import Any, Optional, TypedDict


class FlowParamDataType(IntEnum):
    UNDEFINED = -1
    BOOLEAN = 1
    INTEGER = 3
    FLOAT = 7
    STRING = 16
    LIST = 20
    DICT = 21


class FlowParam(TypedDict):
    flow_id: int
    name: str
    data_type: Optional[FlowParamDataType]
    value: Any


class FlowParamPass(TypedDict):
    control_flow_id: int
    work_flow_name: str
    name: str
    data_type: Optional[FlowParamDataType]
    value: Any
