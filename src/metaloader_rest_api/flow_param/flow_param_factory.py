from typing import Any, Optional

from metaloader_rest_api.flow_param.flow_param_model import (
    FlowParam,
    FlowParamDataType,
    FlowParamPass,
)

_FLOW_PARAM_DATA_TYPE_MAP = {
    "bool": FlowParamDataType.BOOLEAN,
    "int": FlowParamDataType.INTEGER,
    "float": FlowParamDataType.FLOAT,
    "str": FlowParamDataType.STRING,
    "list": FlowParamDataType.LIST,
    "dict": FlowParamDataType.DICT,
}


def get_flow_param(
    flow_id: int,
    name: str,
    data_type: Optional[str],
    value: Any,
) -> FlowParam:
    data_type = _FLOW_PARAM_DATA_TYPE_MAP.get(
        data_type or type(value).__name__,
        FlowParamDataType.UNDEFINED,
    )

    return FlowParam(
        flow_id=flow_id,
        name=name,
        data_type=data_type,
        value=value,
    )


def get_flow_param_pass(
    flow_id: int,
    work_flow_name: str,
    name: str,
    value: Any,
) -> FlowParamPass:
    data_type = _FLOW_PARAM_DATA_TYPE_MAP.get(
        type(value).__name__,
        FlowParamDataType.UNDEFINED,
    )

    flow_param_pass = FlowParamPass(
        control_flow_id=flow_id,
        work_flow_name=work_flow_name,
        name=name,
        data_type=data_type,
        value=value,
    )

    return flow_param_pass
