from datetime import datetime

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, bindparam, text
from sqlalchemy.dialects.postgresql import JSON<PERSON>
from sqlalchemy.orm import Session
from structlog.stdlib import Bo<PERSON><PERSON>ogger, get_logger

from metaloader_rest_api import etl
from metaloader_rest_api.common_model import LAST_DATE
from metaloader_rest_api.common_repository import (
    BufferedStageBatchRepository,
    StageBatchRepository,
)
from metaloader_rest_api.flow_param.flow_param_model import (
    FlowParam,
    FlowParamDataType,
    FlowParamPass,
)

_DEFAULT_LOGGER = get_logger(__name__)


class FlowParamRepository(BufferedStageBatchRepository[FlowParam]):
    def __init__(
        self,
        session: Session,
        module_id: int,
        version_id: int,
        effective_date: datetime,
        table_id: str,
        page_size: int = StageBatchRepository.PAGE_SIZE_DEFAULT,
        log: BoundLogger = _DEFAULT_LOGGER,
    ):
        table = "mart_flow_parameter"
        super().__init__(
            session,
            table,
            table_id,
            self._create_statement,
            self._load_statement,
            page_size,
            log,
        )
        self._module_id = module_id
        self._version_id = version_id
        self._effective_date = effective_date

    @property
    def module_id(self) -> int:
        return self._module_id

    @module_id.setter
    def module_id(self, value: int) -> None:
        self._module_id = value

    @property
    def version_id(self) -> int:
        return self._version_id

    @version_id.setter
    def version_id(self, value: int) -> None:
        self._version_id = value

    @property
    def effective_date(self) -> datetime:
        return self._effective_date

    @effective_date.setter
    def effective_date(self, value: datetime) -> None:
        self._effective_date = value

    def __exit__(self, exc_type, exc_value, traceback):
        super().__exit__(exc_type, exc_value, traceback)

        if exc_type is None:
            stage = self.reload_stage()
            self.load_bridge(stage)
            self.load_link(stage)

    def _prepare_load_statement(self) -> TextClause:
        statement = super()._prepare_load_statement()
        statement = statement.bindparams(
            bindparam("value", type_=JSONB(none_as_null=True)),
        )
        return statement

    @staticmethod
    def _create_statement(table: str) -> str:
        return f"""
            CREATE TABLE {table} (
                flow_rk            BIGINT
              , flow_parameter_cd  TEXT
              , data_type_rk       SMALLINT
              , default_value_json JSONB
            )
        """

    @staticmethod
    def _load_statement(table: str) -> str:
        return f"""
            INSERT INTO {table} (flow_rk
                               , flow_parameter_cd
                               , data_type_rk
                               , default_value_json)
            VALUES (:flow_id
                  , :name
                  , :data_type
                  , :value)
        """

    def reload_stage(self) -> str:
        log = self._log.bind(
            action="reload_stage",
            table="mart_flow_parameter_0",
        )
        log.info("begin", table_id=self._table_id)

        stage = f"stg.mart_flow_parameter_0_{self._table_id}"

        log.info("create stage")
        self._session.execute(text(f"DROP TABLE IF EXISTS {stage}"))
        self._session.execute(
            text(f"""
                CREATE TABLE {stage} (
                    flow_rk            BIGINT
                  , flow_parameter_cd  TEXT
                  , data_type_rk       SMALLINT
                  , default_value_json JSONB
                )
            """)
        )

        self._session.execute(
            statement=text(f"""
                INSERT INTO {stage} (flow_rk
                                   , flow_parameter_cd
                                   , data_type_rk
                                   , default_value_json)
                     SELECT
                DISTINCT ON (flow_rk
                           , flow_parameter_cd)
                            flow_rk
                          , flow_parameter_cd
                          , data_type_rk
                          , default_value_json
                       FROM {self.table}
                   ORDER BY flow_rk
                          , flow_parameter_cd
                          , data_type_rk DESC
            """),
            params={
                "effective_to_dttm": LAST_DATE,
                "code_delivery_rk": self._module_id,
            },
        )

        return stage

    def load_bridge(
        self,
        stage: str,
    ) -> None:
        log = self._log.bind(table="bridge_flow_parameter")
        log.info("begin")
        self._session.execute(
            statement=text(f"""
                INSERT INTO metamodel.bridge_flow_parameter(flow_parameter_cd
                                                          , flow_parameter_name
                                                          , flow_parameter_desc
                                                          , data_type_rk
                                                          , default_value_json
                                                          , is_global_flg)
                SELECT flow_parameter_cd flow_parameter_cd
                     , flow_parameter_cd flow_parameter_name
                     , NULL              flow_parameter_desc
                     , data_type_rk      data_type_rk
                     , NULL              default_value_json
                     , FALSE             is_global_flg 
                  FROM (SELECT
                      DISTINCT flow_parameter_cd
                             , data_type_rk
                          FROM {stage}) p
                 WHERE NOT EXISTS (SELECT NULL
                                     FROM metamodel.bridge_flow_parameter
                                    WHERE flow_parameter_cd = p.flow_parameter_cd
                                      AND data_type_rk      = p.data_type_rk)
            """),
            params={
                "version_rk": self._version_id,
                "effective_from_dttm": self._effective_date,
                "effective_to_dttm": LAST_DATE,
            },
        )
        log.info("end")

    def load_link(
        self,
        stage: str,
    ) -> None:
        log = self._log.bind(
            action="load_link",
            table="link_flow_parameter",
        )
        log.info("begin", table_id=self._table_id)

        merge_stage = f"stg.link_flow_parameter_{self._table_id}"

        log.info("create merge_stage")
        self._session.execute(text(f"DROP TABLE IF EXISTS {merge_stage}"))
        self._session.execute(
            text(f"""
                CREATE TABLE {merge_stage} (
                    flow_rk            BIGINT
                  , flow_parameter_rk  BIGINT
                  , default_value_json JSONB
                  , is_required_flg    BOOL
                  , is_in_flg          BOOL
                  , is_out_flg         BOOL
                  , code_delivery_rk   SMALLINT
                )
            """)
        )
        self._session.execute(
            statement=text(f"""
                INSERT INTO {merge_stage} (flow_rk
                                         , flow_parameter_rk
                                         , default_value_json
                                         , is_required_flg
                                         , is_in_flg
                                         , is_out_flg
                                         , code_delivery_rk)
                     SELECT s.flow_rk
                          , p.flow_parameter_rk
                          , s.default_value_json
                          , s.default_value_json IS NULL is_required_flg
                          , TRUE                         is_in_flg
                          , FALSE                        is_out_flg
                          , :code_delivery_rk            code_delivery_rk
                       FROM {stage}                                   s
                       JOIN metamodel.bridge_flow_parameter           p
                         ON p.flow_parameter_cd = s.flow_parameter_cd
                        AND p.data_type_rk      = s.data_type_rk
            """),
            params={
                "effective_to_dttm": LAST_DATE,
                "code_delivery_rk": self._module_id,
            },
        )

        log.info("merge")
        etl.merge(
            session=self._session,
            version_id=self._version_id,
            effective_date=self._effective_date,
            table="metamodel.link_flow_parameter",
            stage=merge_stage,
            primary_key=None,
            keys=[
                "flow_rk",
                "flow_parameter_rk",
            ],
            values=[
                "default_value_json",
                "is_required_flg",
                "is_in_flg",
                "is_out_flg",
            ],
            others=[
                "code_delivery_rk",
            ],
            filter_expression="code_delivery_rk = :code_delivery_rk",
            params={"code_delivery_rk": self._module_id},
            log=log,
        )

        log.info("end")


class FlowTParamPassRepository(BufferedStageBatchRepository[FlowParamPass]):
    def __init__(
        self,
        session: Session,
        module_id: int,
        version_id: int,
        effective_date: datetime,
        table_id: str,
        page_size: int = StageBatchRepository.PAGE_SIZE_DEFAULT,
        log: BoundLogger = _DEFAULT_LOGGER,
    ):
        table = "mart_flow_parameter_pass"
        super().__init__(
            session,
            table,
            table_id,
            self._create_statement,
            self._load_statement,
            page_size,
            log,
        )
        self._module_id = module_id
        self._version_id = version_id
        self._effective_date = effective_date

    @property
    def module_id(self) -> int:
        return self._module_id

    @module_id.setter
    def module_id(self, value: int) -> None:
        self._module_id = value

    @property
    def version_id(self) -> int:
        return self._version_id

    @version_id.setter
    def version_id(self, value: int) -> None:
        self._version_id = value

    @property
    def effective_date(self) -> datetime:
        return self._effective_date

    @effective_date.setter
    def effective_date(self, value: datetime) -> None:
        self._effective_date = value

    def __exit__(self, exc_type, exc_value, traceback):
        try:
            return super().__exit__(exc_type, exc_value, traceback)
        except Exception as exception:
            exc_type = type(exception)
            raise exception
        finally:
            if exc_type is None:
                self.load_link()

    def _prepare_load_statement(self) -> TextClause:
        statement = super()._prepare_load_statement()
        statement = statement.bindparams(
            bindparam("value", type_=JSONB(none_as_null=True)),
        )
        return statement

    @staticmethod
    def _create_statement(table: str) -> str:
        return f"""
            CREATE TABLE {table} (
                control_flow_rk             BIGINT
              , work_flow_name              TEXT
              , flow_parameter_cd           TEXT
              , flow_parameter_data_type_rk SMALLINT
              , flow_parameter_json         JSONB
            )
        """

    @staticmethod
    def _load_statement(table: str) -> str:
        return f"""
            INSERT INTO {table} (control_flow_rk
                               , work_flow_name
                               , flow_parameter_cd
                               , flow_parameter_data_type_rk
                               , flow_parameter_json)
            VALUES (:control_flow_id
                  , :work_flow_name
                  , :name
                  , :data_type
                  , :value)
        """

    def load_link(self) -> None:
        log = self._log.bind(
            action="load_link",
            table="link_flow_parameter_pass",
        )
        log.info("begin", table_id=self._table_id)

        merge_stage = f"stg.link_flow_parameter_pass_{self._table_id}"

        log.info("create merge_stage")
        self._session.execute(text(f"DROP TABLE IF EXISTS {merge_stage}"))
        self._session.execute(
            text(f"""
                CREATE TABLE {merge_stage} (
                    control_flow_rk     BIGINT
                  , work_flow_rk        BIGINT
                  , flow_parameter_rk   BIGINT
                  , flow_parameter_json JSONB
                  , is_value_flg        BOOL
                  , code_delivery_rk    SMALLINT
                )
            """)
        )
        self._session.execute(
            statement=text(f"""
                INSERT INTO {merge_stage} (control_flow_rk
                                         , work_flow_rk
                                         , flow_parameter_rk
                                         , flow_parameter_json
                                         , is_value_flg
                                         , code_delivery_rk)
                     SELECT s.control_flow_rk                                               control_flow_rk
                          , COALESCE(f_w.flow_rk, -1)                                       work_flow_rk
                          , p_w.flow_parameter_rk                                           flow_parameter_rk
                          , CASE
                            WHEN p_v.flow_parameter_rk IS NULL
                            THEN s.flow_parameter_json
                            ELSE p_v.default_value_json
                             END                                                            flow_parameter_json
                          , CASE
                            WHEN s.flow_parameter_data_type_rk = {FlowParamDataType.STRING}
                             AND s.flow_parameter_json ->> 0 LIKE '${{%}}'
                            THEN p_v.flow_parameter_rk IS NOT NULL
                            ELSE TRUE
                             END                                                            is_value_flg
                          , :code_delivery_rk                                               code_delivery_rk
                       FROM {self.table}                                                    s
                  LEFT JOIN metamodel.bridge_flow                                           f_w
                         ON f_w.flow_name = s.work_flow_name
                        AND f_w.code_delivery_rk = :code_delivery_rk
                        AND f_w.effective_to_dttm = :effective_to_dttm
                        AND f_w.deleted_flg IS FALSE
                       JOIN metamodel.bridge_flow_parameter                                 p_w
                         ON p_w.flow_parameter_cd = s.flow_parameter_cd
                        AND p_w.data_type_rk      = s.flow_parameter_data_type_rk /*FIXME: FlowParamDataType.UNDEFINED*/
                  LEFT JOIN metamodel.bridge_flow_parameter                                 p_p
                         ON p_p.flow_parameter_cd
                          = CASE
                            WHEN s.flow_parameter_data_type_rk = {FlowParamDataType.STRING}
                             AND s.flow_parameter_json ->> 0 LIKE '${{%}}'
                            THEN SUBSTRING(s.flow_parameter_json ->> 0
                                      FROM 3
                                       FOR LENGTH(s.flow_parameter_json ->> 0) - 3) 
                             END
                  LEFT JOIN metamodel.link_flow_parameter                                   p_v
                         ON p_v.flow_rk           = s.control_flow_rk
                        AND p_v.flow_parameter_rk = p_p.flow_parameter_rk
                        AND p_v.effective_to_dttm = :effective_to_dttm
                        AND p_v.deleted_flg IS FALSE
                      WHERE p_p.flow_parameter_rk IS NOT DISTINCT FROM p_v.flow_parameter_rk
            """),
            params={
                "effective_to_dttm": LAST_DATE,
                "code_delivery_rk": self._module_id,
            },
        )

        log.info("merge")
        etl.merge(
            session=self._session,
            version_id=self._version_id,
            effective_date=self._effective_date,
            table="metamodel.link_flow_parameter_pass",
            stage=merge_stage,
            primary_key=None,
            keys=[
                "control_flow_rk",
                "work_flow_rk",
                "flow_parameter_rk",
            ],
            values=[
                "flow_parameter_json",
                "is_value_flg",
            ],
            others=[
                "code_delivery_rk",
            ],
            filter_expression="code_delivery_rk = :code_delivery_rk",
            params={"code_delivery_rk": self._module_id},
            log=log,
        )

        log.info("end")
