import logging
import sys
from typing import Any, Literal, Mapping

import structlog
from fastapi import Request
from structlog.types import <PERSON>D<PERSON>, Processor

from metaloader_rest_api.helpers import EMPTY_MAPPING

TOP_LEVEL_PACKAGE_NAME = str.partition(__name__, ".")[0]
LogFormat = Literal["pretty", "json", "logfmt"]
LogLevel = Literal["CRITICAL", "ERROR", "WARNING", "INFO", "DEBUG"]

DEFAULT_LOG_FORMAT = "json"
DEFAULT_LOG_LEVEL = "INFO"


def setup_logging(
    log_format: LogFormat,
    log_level: LogLevel = "INFO",
) -> None:
    shared_processors: list[Processor] = [
        structlog.contextvars.merge_contextvars,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.processors.CallsiteParameterAdder(
            {
                structlog.processors.CallsiteParameter.THREAD,
                structlog.processors.CallsiteParameter.THREAD_NAME,
                structlog.processors.CallsiteParameter.PROCESS,
            }
        ),
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.stdlib.ExtraAdder(),
        _drop_color_message_key,
        structlog.processors.TimeStamper(fmt="iso"),
    ]

    if log_format != "pretty":
        shared_processors.append(structlog.processors.format_exc_info)

    structlog.configure(
        processors=shared_processors
        + [
            structlog.stdlib.ProcessorFormatter.wrap_for_formatter,
        ],
        logger_factory=structlog.stdlib.LoggerFactory(),
        cache_logger_on_first_use=True,
    )

    log_renderer: structlog.types.Processor = {
        "logfmt": structlog.processors.LogfmtRenderer(),
        "json": structlog.processors.JSONRenderer(),
        "pretty": structlog.dev.ConsoleRenderer(),
    }[log_format]

    formatter = structlog.stdlib.ProcessorFormatter(
        # Это запроцессить лог-сообщения от стандартного `logging`
        foreign_pre_chain=shared_processors,
        # Это запроцессит сообщения и от `logging` и от `structglog`
        processors=[
            structlog.stdlib.ProcessorFormatter.remove_processors_meta,
            log_renderer,
        ],
    )

    handler = logging.StreamHandler()
    handler.setFormatter(formatter)
    root_logger = logging.getLogger()
    root_logger.addHandler(handler)
    root_logger.setLevel(log_level.upper())

    # Access logs ювикорна выключаем совсем, т.к. будем писать эту инфу сами
    logging.getLogger("uvicorn.access").handlers.clear()
    logging.getLogger("uvicorn.access").propagate = False
    # У остальных логов ювикорна выключаем дефолтные обработчики, чтобы они обработались через `structlog`
    for name in ["uvicorn", "uvicorn.error"]:
        logging.getLogger(name).handlers.clear()
        logging.getLogger(name).propagate = True

    def handle_exception(exc_type, exc_value, exc_traceback):
        """
        Log any uncaught exception instead of letting it be printed by Python
        (but leave KeyboardInterrupt untouched to allow users to Ctrl+C to stop)
        See https://stackoverflow.com/a/16993115/3641865
        """
        if issubclass(exc_type, KeyboardInterrupt):
            return sys.__excepthook__(exc_type, exc_value, exc_traceback)
        root_logger.error(
            "Uncaught exception", exc_info=(exc_type, exc_value, exc_traceback)
        )

    sys.excepthook = handle_exception


def pass_metamodel_log_kwargs(request: Request, **kwargs: str) -> None:
    """Записывает произвольные пары ключ-значение в объект хттп-запроса, чтобы потом залогировать
    их в единственном соообщении в конце выполнения эндпойнта (это называется Canonical Log Lines)

    Приходится передавать их явно через объект Request, а не через `structlog.bind_contextvars`,
    т.к. поскольку FastAPI построен на асинхронном питоне, то наши sync-функции эндпойнтов исполняются
    в отдельных тредах, а контекстные переменные по определению существуют только в границах одного
    треда.
    """
    try:
        request.state.metamodel_kwargs.update(
            (f"metamodel.{k}", v) for k, v in kwargs.items()
        )
    except AttributeError:
        request.state.metamodel_kwargs = {
            f"metamodel.{k}": v for k, v in kwargs.items()
        }


def get_metamodel_log_kwargs(request: Request) -> Mapping[str, Any]:
    return getattr(request.state, "metamodel_kwargs", EMPTY_MAPPING)


def _drop_color_message_key(_1, _2, event_dict: EventDict) -> EventDict:
    """Uvicorn повторно логирует сообщение в дополнительном поле `color_message`, но нам оно не нужно"""
    event_dict.pop("color_message", None)
    return event_dict
