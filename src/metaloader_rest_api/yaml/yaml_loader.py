from datetime import date, datetime, time
from functools import partial
from typing import (
    Any,
    Callable,
    List,
    Literal,
    Mapping,
    Union,
)
from uuid import UUID

from yaml import (
    CLoader,
    Loader,
    load,
)

from metaloader_rest_api.yaml.yaml_stream import YamlStream

YamlLoader = Literal[
    "python",
    "c",
]


YAML_LOADER_DEFAULT: YamlLoader = "c"


_YAML_LOADERS = {
    "python": Loader,
    "c": CLoader,
}


def _get_yaml_loader(loader: YamlLoader = YAML_LOADER_DEFAULT):
    return _YAML_LOADERS[loader]


_YAML_LOADER_DEFAULT = _get_yaml_loader()


Yaml = Union[
    Mapping[str, "Yaml"],
    List["Yaml"],
    bool,
    int,
    float,
    str,
    date,
    time,
    datetime,
    UUID,
    Any,
]


def load_yaml(
    stream: YamlStream,
    loader=_YAML_LOADER_DEFAULT,
) -> Yaml:
    return load(stream, loader)


def get_yaml_loader(
    loader: YamlLoader = YAML_LOADER_DEFAULT,
) -> Callable[[YamlStream], Yaml]:
    return partial(
        load_yaml,
        loader=_get_yaml_loader(loader),
    )
