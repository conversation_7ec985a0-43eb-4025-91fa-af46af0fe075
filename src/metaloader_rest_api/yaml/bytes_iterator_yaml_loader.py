from typing import Callable, Iterator

from metaloader_rest_api.yaml.bytes_iterator_yaml_stream import (
    BytesIteratorYamlStream,
)
from metaloader_rest_api.yaml.yaml_loader import (
    YAML_LOADER_DEFAULT,
    Yaml,
    YamlLoader,
    get_yaml_loader,
)

BytesIteratorYamlLoader = Callable[[str, Iterator[bytes]], Yaml]


def bytes_iterator_yaml_loader(
    loader: YamlLoader = YAML_LOADER_DEFAULT,
) -> BytesIteratorYamlLoader:
    load_yaml = get_yaml_loader(loader)

    def load(
        name: str,
        iterator: Iterator[bytes],
    ) -> Yaml:
        return load_yaml(BytesIteratorYamlStream(name, iterator))

    return load
