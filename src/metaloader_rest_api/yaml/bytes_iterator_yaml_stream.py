from typing import Iterator

from metaloader_rest_api.yaml.yaml_stream import ReadableYamlStream


class BytesIteratorYamlStream(ReadableYamlStream):
    def __init__(
        self,
        name: str,
        iterator: Iterator[bytes],
    ):
        self.name = name
        self._iterator = iterator

    def read(self, size: int) -> bytes:
        try:
            return next(self._iterator)
        except StopIteration:
            return bytes()


def get_bytes_iterator_yaml_stream(
    name: str,
    iterator: Iterator[bytes],
) -> BytesIteratorYamlStream:
    return BytesIteratorYamlStream(name, iterator)
