from datetime import datetime
from typing import (
    Any,
    Callable,
    ContextManager,
    Optional,
)
from uuid import UUID

from sqlalchemy.orm import Session
from structlog.stdlib import Bo<PERSON><PERSON>ogger, get_logger

from metaloader_rest_api.ceh_module.ceh_module_provider import (
    provide_ceh_module_master_flow_layout_path,
)
from metaloader_rest_api.ceh_service.ceh_service_provider import (
    provide_ceh_service_urls,
)
from metaloader_rest_api.common_param import (
    DB_COMMIT,
    DB_FETCH_SIZE_DEFAULT,
    DB_PAGE_SIZE_DEFAULT,
    FAILSAFE_THRESHOLD_DEFAULT,
    HTTP_PAGE_SIZE_DEFAULT,
    HTTP_RETRIES_DEFAULT,
    HTTP_TIMEOUT_DEFAULT,
)
from metaloader_rest_api.common_repository import SessionResource
from metaloader_rest_api.deployment import get_deployment
from metaloader_rest_api.http.http_client import http_client
from metaloader_rest_api.http.http_loader import http_yaml_loader
from metaloader_rest_api.master_flow.master_flow_item_factory import (
    master_flow_item_factory,
)
from metaloader_rest_api.master_flow.master_flow_item_model import MasterFlowItem
from metaloader_rest_api.master_flow.master_flow_item_provider import (
    MasterFlowItemProvider,
    master_flow_item_provider,
)
from metaloader_rest_api.master_flow.master_flow_item_repository import (
    MasterFlowItemRepository,
)
from metaloader_rest_api.yaml.yaml_loader import (
    YAML_LOADER_DEFAULT,
    YamlLoader,
)

_DEFAULT_LOGGER = get_logger(__name__)


def load_master_flow_items(
    session: Session,
    load_id: UUID,
    module: str,
    service: str,
    release: str,
    effective_date: datetime,
    incremental: bool = False,
    http_timeout: float = HTTP_TIMEOUT_DEFAULT,
    http_retries: int = HTTP_RETRIES_DEFAULT,
    http_page_size: int = HTTP_PAGE_SIZE_DEFAULT,
    db_fetch_size: int = DB_FETCH_SIZE_DEFAULT,
    db_page_size: int = DB_PAGE_SIZE_DEFAULT,
    db_commit: Optional[bool] = DB_COMMIT,
    yaml_loader: YamlLoader = YAML_LOADER_DEFAULT,
    fail_threshold: int = FAILSAFE_THRESHOLD_DEFAULT,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    log = log.bind(
        action="load_master_flows",
        load_id=load_id,
        module=module,
        service=service,
        release=release,
        effective_date=effective_date,
    )
    log.info(
        "begin",
        incremental=incremental,
        http_timeout=http_timeout,
        http_retries=http_retries,
        http_page_size=http_page_size,
        db_fetch_size=db_fetch_size,
        db_page_size=db_page_size,
        db_commit=db_commit,
        yaml_loader=yaml_loader,
        fail_threshold=fail_threshold,
    )
    try:
        module_id, service_id, version_id = get_deployment(
            session,
            module,
            service,
            release,
            effective_date,
            log,
        )
        _, flow_layout_url = provide_ceh_service_urls(session, service_id)
        master_flow_layout_path = provide_ceh_module_master_flow_layout_path(
            session, module_id, log
        )
        log.info(
            "init",
            module_id=module_id,
            version_id=version_id,
            flow_layout_url=flow_layout_url,
            master_flow_layout_path=master_flow_layout_path,
        )
        with SessionResource(session, db_commit, log):
            _load_master_flows(
                master_flow_repository=MasterFlowItemRepository(
                    session=session,
                    module_id=module_id,
                    version_id=version_id,
                    effective_date=effective_date,
                    table_id=load_id.hex,
                    incremental=incremental,
                    page_size=db_page_size,
                    log=log,
                ),
                master_flow_item_loader=http_yaml_loader(
                    client=http_client(
                        url=flow_layout_url,
                        timeout=http_timeout,
                        retries=http_retries,
                    ),
                    url=master_flow_layout_path,
                    yaml_loader=yaml_loader,
                    log=log,
                ),
                master_flow_item_provider=master_flow_item_provider(
                    session=session,
                    module_id=module_id,
                    fetch_size=db_fetch_size,
                    log=log,
                ),
                fail_threshold=fail_threshold,
                log=log,
            )
    except Exception:
        log.exception("fail")
        raise
    log.info("end")

    return None


def _load_master_flows(
    master_flow_repository: ContextManager[Callable[[MasterFlowItem], None]],
    master_flow_item_loader: ContextManager[Callable[[str], Any]],
    master_flow_item_provider: MasterFlowItemProvider,
    fail_threshold: int = FAILSAFE_THRESHOLD_DEFAULT,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    with (
        master_flow_item_provider as master_flow_items,
        master_flow_item_loader as load_master_flow_item,
        master_flow_repository as store_master_flow,
    ):
        if fail_threshold == 0:
            log.warning("fail_threshold_off")
        fails = fail_threshold
        for (master_flow_name,) in master_flow_items:
            try:
                for flow in master_flow_item_factory(
                    name=master_flow_name,
                    flow=load_master_flow_item(master_flow_name),
                ):
                    store_master_flow(flow)
                fails = fail_threshold
            except Exception:
                fails -= 1
                log.exception("fail", master_flow=master_flow_name)
                if fails == 0:
                    log.error("fail_threshold_reached")
                    raise RuntimeError(f"Fail threshold {fail_threshold} reached")
