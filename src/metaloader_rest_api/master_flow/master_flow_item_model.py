from enum import IntEnum
from typing import Any, Dict, TypedDict

MASTER_FLOW_ITEM_SEPARATOR = ":"


class MasterFlowItemType(IntEnum):
    UNDEFINED = -1
    FLOW = 1
    GROUP = 2
    STEP = 3
    UNIT = 4
    DEPENDENCY = 5


class MasterFlowItem(TypedDict):
    type: MasterFlowItemType
    name: str
    path: str
    index: int
    value: Dict[str, Any]


def master_flow_item(
    name: str,
    flow: Dict[str, Any],
) -> MasterFlowItem:
    return MasterFlowItem(
        type=MasterFlowItemType.FLOW,
        name=name,
        path="",
        index=-1,
        value=flow,
    )


def master_flow_group_item(
    name: str,
    path: str,
    index: int,
    group: Dict[str, Any],
) -> MasterFlowItem:
    return MasterFlowItem(
        type=MasterFlowItemType.GROUP,
        name=name,
        path=path,
        index=index,
        value=group,
    )


def master_flow_step_item(
    name: str,
    path: str,
    index: int,
) -> MasterFlowItem:
    return MasterFlowItem(
        type=MasterFlowItemType.STEP,
        name=name,
        path=path,
        index=index,
        value={},
    )


def master_flow_unit_item(
    name: str,
    path: str,
    unit: Dict[str, Any],
) -> MasterFlowItem:
    return MasterFlowItem(
        type=MasterFlowItemType.UNIT,
        name=name,
        path=path,
        index=-1,
        value=unit,
    )


def master_flow_dependency_item(
    dependency: str,
    path: str,
    index: int,
) -> MasterFlowItem:
    return MasterFlowItem(
        type=MasterFlowItemType.DEPENDENCY,
        name=dependency,
        path=path,
        index=index,
        value={},
    )
