from typing import <PERSON><PERSON><PERSON><PERSON><PERSON>, Iterator, <PERSON><PERSON>, <PERSON><PERSON>

from sqlalchemy.orm import Session
from structlog.stdlib import <PERSON><PERSON><PERSON><PERSON><PERSON>, get_logger

from metaloader_rest_api.common_model import LAST_DATE
from metaloader_rest_api.db.db_provider import db_provider
from metaloader_rest_api.flow.flow_model import FlowType

_DEFAULT_LOGGER = get_logger(__name__)


MasterFlowProvider = ContextManager[Iterator[Tuple[int, str, str, str]]]


def master_flow_provider(
    session: Session,
    module_id: int,
    path: str,
    fetch_size: Optional[int] = None,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> MasterFlowProvider:
    return db_provider(
        session=session,
        query=f"""
            SELECT flow_rk
                 , flow_name
                 , dag_json->>'fileloc'    dag_file_path_name
                 , dag_json->>'file_token' dag_file_token_hash
              FROM metamodel.bridge_flow
             WHERE STRPOS(dag_json->>'fileloc', :fileloc) > 0 /*FIXME flow_type_rk = {FlowType.MASTER}*/
               AND code_delivery_rk = :code_delivery_rk
               AND effective_to_dttm = :effective_to_dttm
               AND deleted_flg IS FALSE
        """,
        params={
            "fileloc": f"/{path}/",
            "code_delivery_rk": module_id,
            "effective_to_dttm": LAST_DATE,
        },
        fetch_size=fetch_size,
        log=log.bind(subject="master_flow"),
    )
