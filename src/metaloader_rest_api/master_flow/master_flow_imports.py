class Path:
    def __init__(
        self,
        *args,
        **kwargs,
    ):
        super().__init__()

        self._stem = args[0]

    @property
    def stem(self):
        return self._stem


_dag_control_flow, _dag_kwargs = None, None


def dag(*args, **kwargs):
    def decorator(function):
        def wrapper(*args, **kwargs):
            function(*args, **kwargs)

            return _dag_control_flow, _dag_kwargs

        return wrapper

    return decorator


def days_ago(*args, **kwargs):
    pass


class ControlFlowLoader:
    def __init__(
        self,
        cf_filename,
        *args,
        **kwargs,
    ):
        super().__init__()

        self._cf_filename = cf_filename

    def load_from_yaml(self):
        return self._cf_filename


class ControlFLowBuilder:
    def __init__(
        self,
        control_flow,
        *args,
        **kwargs,
    ):
        super().__init__()

        self._control_flow = control_flow
        self._kwargs = kwargs

    def build(self):
        global _dag_control_flow, _dag_kwargs
        _dag_control_flow = self._control_flow
        _dag_kwargs = self._kwargs
