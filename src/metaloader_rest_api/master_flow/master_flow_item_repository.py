from datetime import datetime

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, bindparam, text
from sqlalchemy.dialects.postgresql import J<PERSON>N<PERSON>
from sqlalchemy.orm import Session
from sqlalchemy.sql.sqltypes import SMALLINT, TEXT
from structlog.stdlib import <PERSON><PERSON><PERSON><PERSON><PERSON>, get_logger

from metaloader_rest_api import etl
from metaloader_rest_api.common_model import LAST_DATE
from metaloader_rest_api.common_repository import (
    BufferedStageBatchRepository,
    StageBatchRepository,
)
from metaloader_rest_api.flow.flow_model import FlowLinkType, FlowStatus, FlowType
from metaloader_rest_api.master_flow.master_flow_item_model import (
    MASTER_FLOW_ITEM_SEPARATOR,
    MasterFlowItem,
    MasterFlowItemType,
)

_DEFAULT_LOGGER = get_logger(__name__)


class MasterFlowItemRepository(BufferedStageBatchRepository[MasterFlowItem]):
    def __init__(
        self,
        session: Session,
        module_id: int,
        version_id: int,
        effective_date: datetime,
        table_id: str,
        incremental: bool = False,
        page_size: int = StageBatchRepository.PAGE_SIZE_DEFAULT,
        log: BoundLogger = _DEFAULT_LOGGER,
    ):
        table = "mart_master_flow_item"
        super().__init__(
            session,
            table,
            table_id,
            self._create_statement,
            self._load_statement,
            page_size,
            log,
        )
        self._module_id = module_id
        self._version_id = version_id
        self._effective_date = effective_date
        self._incremental = incremental

    @property
    def module_id(self) -> int:
        return self._module_id

    @module_id.setter
    def module_id(self, value: int) -> None:
        self._module_id = value

    @property
    def version_id(self) -> int:
        return self._version_id

    @version_id.setter
    def version_id(self, value: int) -> None:
        self._version_id = value

    @property
    def effective_date(self) -> datetime:
        return self._effective_date

    @effective_date.setter
    def effective_date(self, value: datetime) -> None:
        self._effective_date = value

    def __exit__(self, exc_type, exc_value, traceback):
        try:
            return super().__exit__(exc_type, exc_value, traceback)
        except Exception as exception:
            exc_type = type(exception)
            raise exception
        finally:
            if exc_type is None:
                self.load_bridge()
                self.load_link()
                self.load_link_satellite()
                self.load_bridge_parent()
                self.load_link_parent()

    def _prepare_load_statement(self) -> TextClause:
        statement = super()._prepare_load_statement()
        statement = statement.bindparams(
            bindparam("type", type_=SMALLINT()),
            bindparam("name", type_=TEXT()),
            bindparam("path", type_=TEXT()),
            bindparam("index", type_=SMALLINT()),
            bindparam("value", type_=JSONB()),
        )
        return statement

    @staticmethod
    def _create_statement(table: str) -> str:
        return f"""
            CREATE TABLE {table} (
                master_flow_item_type_rk   SMALLINT
              , master_flow_item_name      TEXT
              , master_flow_item_path_name TEXT
              , item_no                    SMALLINT
              , item_json                  JSONB
            )
        """

    @staticmethod
    def _load_statement(table: str) -> str:
        return f"""
            INSERT INTO {table} (master_flow_item_type_rk
                               , master_flow_item_name
                               , master_flow_item_path_name
                               , item_no
                               , item_json)
            VALUES (:type
                  , :name
                  , :path
                  , :index
                  , :value)
        """

    def load_bridge(self) -> None:
        log = self._log.bind(
            action="load_bridge",
            table="bridge_master_flow_item",
        )
        log.info("begin", table_id=self._table_id)

        merge_stage = f"stg.bridge_master_flow_item_{self._table_id}"

        log.info("create merge_stage")
        self._session.execute(text(f"DROP TABLE IF EXISTS {merge_stage}"))
        self._session.execute(
            text(f"""
                CREATE TABLE {merge_stage} (
                    master_flow_item_type_rk   SMALLINT
                  , master_flow_item_name      TEXT
                  , master_flow_item_path_name TEXT
                  , item_json                  JSONB
                  , item_hash                  BIGINT
                  , code_delivery_rk           SMALLINT
                )
            """)
        )
        self._session.execute(
            statement=text(f"""
                INSERT INTO {merge_stage} (master_flow_item_type_rk
                                         , master_flow_item_name
                                         , master_flow_item_path_name
                                         , item_json
                                         , item_hash
                                         , code_delivery_rk)
                     SELECT master_flow_item_type_rk
                          , master_flow_item_name
                          , master_flow_item_path_name
                          , item_json
                          , JSONB_HASH_EXTENDED(item_json, 0) item_hash
                          , :code_delivery_rk
                       FROM {self.table}
            """),
            params={
                "code_delivery_rk": self._module_id,
            },
        )

        log.info("merge")
        etl.merge(
            session=self._session,
            version_id=self._version_id,
            effective_date=self._effective_date,
            table="metamodel.bridge_master_flow_item",
            stage=merge_stage,
            primary_key="master_flow_item_rk",
            keys=[
                "master_flow_item_name",
                "master_flow_item_path_name",
            ],
            values=["item_hash"],
            others=[
                "master_flow_item_type_rk",
                "item_json",
                "code_delivery_rk",
            ],
            filter_expression="code_delivery_rk = :code_delivery_rk",
            params={"code_delivery_rk": self._module_id},
            log=log,
        )

        log.info("end")

    def load_link(self) -> None:
        log = self._log.bind(
            action="load_link",
            table="link_master_flow_item",
        )
        log.info("begin", table_id=self._table_id)

        merge_stage = f"stg.link_master_flow_item_{self._table_id}"

        log.info("create merge_stage")
        self._session.execute(text(f"DROP TABLE IF EXISTS {merge_stage}"))
        self._session.execute(
            text(f"""
                CREATE TABLE {merge_stage} (
                    master_flow_item_rk        BIGINT
                  , parent_master_flow_item_rk BIGINT
                  , item_no                    SMALLINT
                  , code_delivery_rk           SMALLINT
                )
            """)
        )
        self._session.execute(
            statement=text(f"""
                INSERT INTO {merge_stage} (master_flow_item_rk
                                         , parent_master_flow_item_rk
                                         , item_no
                                         , code_delivery_rk)
                       WITH b
                         AS (SELECT master_flow_item_type_rk
                                  , master_flow_item_rk
                                  , master_flow_item_name
                                  , master_flow_item_path_name
                               FROM metamodel.bridge_master_flow_item 
                              WHERE code_delivery_rk = :code_delivery_rk
                                AND effective_to_dttm = :effective_to_dttm
                                AND deleted_flg IS FALSE)
                     SELECT b.master_flow_item_rk
                          , l.master_flow_item_rk parent_master_flow_item_rk
                          , m.item_no     
                          , :code_delivery_rk
                       FROM {self.table} m
                       JOIN b
                         ON b.master_flow_item_name      = m.master_flow_item_name
                        AND b.master_flow_item_path_name = m.master_flow_item_path_name
                       JOIN b l
                         ON CASE l.master_flow_item_type_rk
                            WHEN {MasterFlowItemType.FLOW}
                            THEN l.master_flow_item_name
                            ELSE CONCAT_WS('{MASTER_FLOW_ITEM_SEPARATOR}'
                                         , l.master_flow_item_path_name
                                         , l.master_flow_item_name)
                             END = b.master_flow_item_path_name 
                        AND b.master_flow_item_type_rk != {MasterFlowItemType.FLOW}
                        AND l.master_flow_item_type_rk != {MasterFlowItemType.DEPENDENCY}
            """),
            params={
                "code_delivery_rk": self._module_id,
                "effective_to_dttm": LAST_DATE,
            },
        )

        log.info("merge")
        etl.merge(
            session=self._session,
            version_id=self._version_id,
            effective_date=self._effective_date,
            table="metamodel.link_master_flow_item",
            stage=merge_stage,
            primary_key=None,
            keys=["master_flow_item_rk"],
            values=[
                "parent_master_flow_item_rk",
                "item_no",
            ],
            others=["code_delivery_rk"],
            filter_expression="code_delivery_rk = :code_delivery_rk",
            params={"code_delivery_rk": self._module_id},
            log=log,
        )

        log.info("end")

    def load_link_satellite(self) -> None:
        log = self._log.bind(
            action="load_link_satellite",
            table="link_master_flow_x_item",
        )
        log.info("begin", table_id=self._table_id)

        merge_stage = f"stg.link_master_flow_x_item_{self._table_id}"

        log.info("create merge_stage")
        self._session.execute(text(f"DROP TABLE IF EXISTS {merge_stage}"))
        self._session.execute(
            text(f"""
                CREATE TABLE {merge_stage} (
                    master_flow_rk      BIGINT
                  , master_flow_item_rk BIGINT
                  , code_delivery_rk    SMALLINT
                )
            """)
        )
        self._session.execute(
            statement=text(f"""
                INSERT INTO {merge_stage} (master_flow_rk     
                                         , master_flow_item_rk
                                         , code_delivery_rk)
                     SELECT f.master_flow_rk
                          , COALESCE(f_i.master_flow_item_rk, -1) master_flow_item_rk
                          , :code_delivery_rk
                       FROM metamodel.bridge_master_flow_item f_i
                 RIGHT JOIN metamodel.bridge_master_flow      f
                         ON f.layout_path_name = f_i.master_flow_item_name || '.yaml'
                        AND f_i.master_flow_item_type_rk = {MasterFlowItemType.FLOW}
                        AND f_i.code_delivery_rk = :code_delivery_rk
                        AND f_i.effective_to_dttm = :effective_to_dttm
                        AND f_i.deleted_flg IS FALSE
                        AND f.code_delivery_rk = :code_delivery_rk
                        AND f.effective_to_dttm = :effective_to_dttm
                        AND f.deleted_flg IS FALSE
            """),
            params={
                "code_delivery_rk": self._module_id,
                "effective_to_dttm": LAST_DATE,
            },
        )

        log.info("merge")
        etl.merge(
            session=self._session,
            version_id=self._version_id,
            effective_date=self._effective_date,
            table="metamodel.link_master_flow_x_item",
            stage=merge_stage,
            primary_key=None,
            keys=["master_flow_rk"],
            values=["master_flow_item_rk"],
            others=["code_delivery_rk"],
            filter_expression="code_delivery_rk = :code_delivery_rk",
            params={"code_delivery_rk": self._module_id},
            log=log,
        )

        log.info("end")

    def load_bridge_parent(self) -> None:
        log = self._log.bind(
            action="load_bridge_parent",
            table="bridge_flow",
        )
        log.info("begin", table_id=self._table_id)

        merge_stage = f"stg.bridge_flow_m_{self._table_id}"

        log.info("create merge_stage")
        self._session.execute(text(f"DROP TABLE IF EXISTS {merge_stage}"))
        self._session.execute(
            text(f"""
                CREATE TABLE {merge_stage} (
                    flow_type_rk           SMALLINT
                  , flow_name              TEXT
                  , flow_release_status_rk SMALLINT
                  , code_delivery_rk       SMALLINT
                )
            """)
        )
        self._session.execute(
            statement=text(f"""
                INSERT INTO {merge_stage} (flow_type_rk
                                         , flow_name
                                         , flow_release_status_rk
                                         , code_delivery_rk)
                     SELECT {FlowType.GROUP}                         flow_type_rk
                          , CONCAT_WS('{MASTER_FLOW_ITEM_SEPARATOR}'
                                    , f.flow_name
                                    , mfi.master_flow_item_name)     flow_name
                          , {FlowStatus.DEPLOYED}                    flow_release_status_rk
                          , :code_delivery_rk                        code_delivery_rk
                       FROM metamodel.bridge_master_flow_item                             mfi
                       JOIN metamodel.link_master_flow_item                               l_mfi
                         ON l_mfi.master_flow_item_rk = mfi.master_flow_item_rk
                        AND l_mfi.code_delivery_rk = :code_delivery_rk
                        AND l_mfi.effective_to_dttm = :effective_to_dttm
                        AND l_mfi.deleted_flg IS FALSE
                        AND mfi.master_flow_item_type_rk = {MasterFlowItemType.GROUP}
                        AND mfi.code_delivery_rk = :code_delivery_rk
                        AND mfi.effective_to_dttm = :effective_to_dttm
                        AND mfi.deleted_flg IS FALSE
                       JOIN metamodel.link_master_flow_x_item                             l_mf_i
                         ON l_mf_i.master_flow_item_rk = l_mfi.parent_master_flow_item_rk
                        AND l_mf_i.code_delivery_rk = :code_delivery_rk
                        AND l_mf_i.effective_to_dttm = :effective_to_dttm
                        AND l_mf_i.deleted_flg IS FALSE
                       JOIN metamodel.bridge_master_flow                                  mf
                         ON mf.master_flow_rk = l_mf_i.master_flow_rk
                        AND mf.code_delivery_rk = :code_delivery_rk
                        AND mf.effective_to_dttm = :effective_to_dttm
                        AND mf.deleted_flg IS FALSE
                       JOIN metamodel.bridge_flow                                         f
                         ON f.flow_rk = mf.master_flow_rk
                        AND f.code_delivery_rk = :code_delivery_rk
                        AND f.effective_to_dttm = :effective_to_dttm
                        AND f.deleted_flg IS FALSE
            """),
            params={
                "code_delivery_rk": self._module_id,
                "effective_to_dttm": LAST_DATE,
            },
        )

        log.info("merge")
        etl.merge(
            session=self._session,
            version_id=self._version_id,
            effective_date=self._effective_date,
            table="metamodel.bridge_flow",
            stage=merge_stage,
            primary_key="flow_rk",
            keys=["flow_name"],
            values=["flow_release_status_rk"],
            others=[
                "flow_type_rk",
                "code_delivery_rk",
            ],
            filter_expression=f"""
                    flow_type_rk = {FlowType.GROUP}
                AND code_delivery_rk = :code_delivery_rk
            """,
            params={"code_delivery_rk": self._module_id},
            log=log,
        )

        log.info("end")

    def load_link_parent(self) -> None:
        log = self._log.bind(
            action="load_link_parent",
            table="link_flow",
        )
        log.info("begin", table_id=self._table_id)

        merge_stage = f"stg.link_flow_m_{self._table_id}"

        log.info("create merge_stage")
        self._session.execute(text(f"DROP TABLE IF EXISTS {merge_stage}"))
        self._session.execute(
            text(f"""
                CREATE TABLE {merge_stage} (
                    flow_link_type_rk            SMALLINT
                  , owner_flow_rk                BIGINT
                  , source_flow_rk               BIGINT
                  , target_flow_rk               BIGINT
                  , flow_timeout_duration        BIGINT
                  , delta_timeout_duration       BIGINT
                  , trigger_timeout_duration     BIGINT
                  , retry_timeout_duration       BIGINT
                  , trigger_cnt                  SMALLINT
                  , retry_cnt                    SMALLINT
                  , is_active_flg                BOOLEAN
                  , is_required_flg              BOOLEAN
                  , is_delta_required_flg        BOOLEAN
                  , is_status_check_flg          BOOLEAN
                  , is_resource_status_check_flg BOOLEAN
                  , code_delivery_rk             SMALLINT
                )
            """)
        )
        self._session.execute(
            statement=text(f"""
                INSERT INTO {merge_stage} (flow_link_type_rk
                                         , owner_flow_rk
                                         , source_flow_rk
                                         , target_flow_rk
                                         , flow_timeout_duration
                                         , delta_timeout_duration
                                         , trigger_timeout_duration
                                         , retry_timeout_duration
                                         , trigger_cnt
                                         , retry_cnt
                                         , is_active_flg
                                         , is_required_flg
                                         , is_delta_required_flg
                                         , is_status_check_flg
                                         , is_resource_status_check_flg
                                         , code_delivery_rk)
                     WITH f
                       AS (SELECT flow_type_rk
                                , flow_rk
                                , flow_name
                             FROM metamodel.bridge_flow
                            WHERE code_delivery_rk = :code_delivery_rk
                              AND effective_to_dttm = :effective_to_dttm
                              AND deleted_flg IS FALSE)
                        , mfi
                       AS (SELECT mfi.master_flow_item_type_rk
                                , mfi.master_flow_item_rk
                                , l_mfi.parent_master_flow_item_rk
                                , mfi.master_flow_item_name
                                , mfi.item_json
                                , l_mfi.item_no
                             FROM metamodel.bridge_master_flow_item mfi
                        LEFT JOIN metamodel.link_master_flow_item   l_mfi
                               ON l_mfi.master_flow_item_rk = mfi.master_flow_item_rk
                              AND l_mfi.code_delivery_rk = :code_delivery_rk
                              AND l_mfi.effective_to_dttm = :effective_to_dttm
                              AND l_mfi.deleted_flg IS FALSE
                              AND mfi.code_delivery_rk = :code_delivery_rk
                              AND mfi.effective_to_dttm = :effective_to_dttm
                              AND mfi.deleted_flg IS FALSE)
                        , mfm
                       AS (SELECT master_flow_item_rk
                                , EXTRACT(EPOCH FROM COALESCE(metadata->>'wrk_dag_timeout_duration', 'PT24H')::INTERVAL) flow_timeout_duration
                                , EXTRACT(EPOCH FROM COALESCE(metadata->>'commit_timeout_duration', 'PT1H')::INTERVAL)   delta_timeout_duration
                             FROM mfi
                     JOIN LATERAL JSONB_TO_RECORD(item_json) mfim (metadata JSONB) 
                               ON master_flow_item_type_rk = {MasterFlowItemType.FLOW})
                        , mfg
                       AS (SELECT parent_master_flow_item_rk master_flow_item_rk
                                , master_flow_item_rk        group_flow_item_rk
                                , master_flow_item_name      group_name
                                , item_no                    group_no
                             FROM mfi
                            WHERE master_flow_item_type_rk = {MasterFlowItemType.GROUP})
                        , mfs
                       AS (SELECT parent_master_flow_item_rk group_flow_item_rk
                                , master_flow_item_rk        step_flow_item_rk
                                , master_flow_item_name      control_flow_name
                             FROM mfi
                            WHERE master_flow_item_type_rk = {MasterFlowItemType.STEP})
                        , mfu
                       AS (SELECT parent_master_flow_item_rk                                       step_flow_item_rk
                                , master_flow_item_name                                            work_flow_name
                                , (item_json->>'timeout_before_trigger')::BIGINT                   trigger_timeout_duration
                                , (item_json->>'retry_timeout')::BIGINT                            retry_timeout_duration
                                , (item_json->>'cf_trigger_cnt')::BIGINT                           trigger_cnt
                                , (item_json->>'retry_cnt')::BIGINT                                retry_cnt
                                , COALESCE((item_json->>'run_flg')::BOOL, TRUE)                    is_active_flg
                                , NOT COALESCE((item_json->>'ignore_task_fail')::BOOL, FALSE)      is_required_flg
                                , NOT COALESCE((item_json->>'ignore_no_delta')::BOOL, TRUE)        is_delta_required_flg
                                , NOT COALESCE((item_json->>'ignore_wrk_fail')::BOOL, FALSE)       is_status_check_flg
                                , NOT COALESCE((item_json->>'ignore_resource_status')::BOOL, TRUE) is_resource_status_check_flg
                             FROM mfi
                            WHERE master_flow_item_type_rk = {MasterFlowItemType.UNIT})
                        , mfd
                       AS (SELECT parent_master_flow_item_rk step_flow_item_rk
                                , master_flow_item_name      control_flow_name
                             FROM mfi
                            WHERE master_flow_item_type_rk = {MasterFlowItemType.DEPENDENCY})
                        , l_mf_g
                       AS (SELECT l_mf_i.master_flow_rk
                                , mfg.master_flow_item_rk
                                , gf.flow_rk group_flow_rk
                                , mfg.group_flow_item_rk
                                , mfg.group_no
                             FROM mfg
                             JOIN metamodel.link_master_flow_x_item l_mf_i
                               ON l_mf_i.master_flow_item_rk = mfg.master_flow_item_rk
                              AND l_mf_i.code_delivery_rk = :code_delivery_rk
                              AND l_mf_i.effective_to_dttm = :effective_to_dttm
                              AND l_mf_i.deleted_flg IS FALSE
                             JOIN f
                               ON f.flow_rk = l_mf_i.master_flow_rk
                             JOIN f gf
                               ON gf.flow_name = CONCAT_WS('{MASTER_FLOW_ITEM_SEPARATOR}'
                                                         , f.flow_name
                                                         , mfg.group_name)
                              AND gf.flow_type_rk = {FlowType.GROUP})
                   SELECT {FlowLinkType.SUBSCRIBER} flow_link_type_rk
                        , master_flow_rk                                        owner_flow_rk
                        , LAG(group_flow_rk
                            , 1
                            , master_flow_rk) OVER (PARTITION BY master_flow_rk
                                                        ORDER BY group_no)      source_flow_rk
                        , group_flow_rk                                         target_flow_rk
                        , NULL::BIGINT flow_timeout_duration
                        , NULL::BIGINT delta_timeout_duration
                        , NULL::BIGINT trigger_timeout_duration
                        , NULL::BIGINT retry_timeout_duration
                        , NULL::SMALLINT trigger_cnt
                        , NULL::SMALLINT retry_cnt
                        , NULL::BOOL is_active_flg
                        , NULL::BOOL is_required_flg
                        , NULL::BOOL is_delta_required_flg
                        , NULL::BOOL is_status_check_flg
                        , NULL::BOOL is_resource_status_check_flg
                        , :code_delivery_rk code_delivery_rk
                     FROM l_mf_g
                    UNION ALL
                   SELECT 
              DISTINCT ON (target_flow_rk
                         , source_flow_rk
                         , owner_flow_rk
                         , flow_link_type_rk)
                          l.flow_link_type_rk
                        , l_mf_g.master_flow_rk            owner_flow_rk
                        , CASE
                          WHEN l.source_flow_name IS NULL
                          THEN l_mf_g.master_flow_rk
                          ELSE COALESCE(sf.flow_rk, -1)
                          END                             source_flow_rk
                        , COALESCE(tf.flow_rk, -1)        target_flow_rk
                        , l.flow_timeout_duration
                        , l.delta_timeout_duration
                        , l.trigger_timeout_duration
                        , l.retry_timeout_duration
                        , l.trigger_cnt
                        , l.retry_cnt
                        , l.is_active_flg
                        , l.is_required_flg
                        , l.is_delta_required_flg
                        , l.is_status_check_flg
                        , l.is_resource_status_check_flg
                        , :code_delivery_rk code_delivery_rk
                     FROM (SELECT {FlowLinkType.SUBSCRIBER} flow_link_type_rk
                                , group_flow_item_rk
                                , NULL::TEXT        source_flow_name
                                , control_flow_name target_flow_name
                                , NULL::BIGINT flow_timeout_duration
                                , NULL::BIGINT delta_timeout_duration
                                , NULL::BIGINT trigger_timeout_duration
                                , NULL::BIGINT retry_timeout_duration
                                , NULL::SMALLINT trigger_cnt
                                , NULL::SMALLINT retry_cnt
                                , NULL::BOOL is_active_flg
                                , NULL::BOOL is_required_flg
                                , NULL::BOOL is_delta_required_flg
                                , NULL::BOOL is_status_check_flg
                                , NULL::BOOL is_resource_status_check_flg
                             FROM mfs
                            UNION ALL
                           SELECT {FlowLinkType.SUBSCRIBER} flow_link_type_rk
                                , mfs.group_flow_item_rk
                                , mfd.control_flow_name source_flow_name
                                , mfs.control_flow_name target_flow_name
                                , NULL::BIGINT flow_timeout_duration
                                , NULL::BIGINT delta_timeout_duration
                                , NULL::BIGINT trigger_timeout_duration
                                , NULL::BIGINT retry_timeout_duration
                                , NULL::SMALLINT trigger_cnt
                                , NULL::SMALLINT retry_cnt
                                , NULL::BOOL is_active_flg
                                , NULL::BOOL is_required_flg
                                , NULL::BOOL is_delta_required_flg
                                , NULL::BOOL is_status_check_flg
                                , NULL::BOOL is_resource_status_check_flg
                             FROM mfd
                             JOIN mfs
                               ON mfd.step_flow_item_rk = mfs.step_flow_item_rk
                            UNION ALL
                           SELECT {FlowLinkType.TRIGGER} flow_link_type_rk
                                , mfg.group_flow_item_rk
                                , mfs.control_flow_name source_flow_name
                                , mfu.work_flow_name    target_flow_name
                                , mfm.flow_timeout_duration
                                , mfm.delta_timeout_duration
                                , mfu.trigger_timeout_duration
                                , mfu.retry_timeout_duration
                                , mfu.trigger_cnt
                                , mfu.retry_cnt
                                , mfu.is_active_flg
                                , mfu.is_required_flg
                                , mfu.is_delta_required_flg
                                , mfu.is_status_check_flg
                                , mfu.is_resource_status_check_flg
                             FROM mfu
                             JOIN mfs
                               ON mfs.step_flow_item_rk = mfu.step_flow_item_rk
                             JOIN mfg
                               ON mfg.group_flow_item_rk = mfs.group_flow_item_rk
                             JOIN mfm
                               ON mfm.master_flow_item_rk = mfg.master_flow_item_rk) l
                     JOIN l_mf_g
                       ON l_mf_g.group_flow_item_rk = l.group_flow_item_rk
                LEFT JOIN f sf
                       ON sf.flow_name = l.source_flow_name
                LEFT JOIN f tf
                       ON tf.flow_name = l.target_flow_name         
            """),
            params={
                "code_delivery_rk": self._module_id,
                "effective_to_dttm": LAST_DATE,
            },
        )

        log.info("merge")
        etl.merge(
            session=self._session,
            version_id=self._version_id,
            effective_date=self._effective_date,
            table="metamodel.link_flow",
            stage=merge_stage,
            primary_key=None,
            keys=[
                "flow_link_type_rk",
                "owner_flow_rk",
                "source_flow_rk",
                "target_flow_rk",
            ],
            values=[
                "flow_timeout_duration",
                "delta_timeout_duration",
                "trigger_timeout_duration",
                "retry_timeout_duration",
                "trigger_cnt",
                "retry_cnt",
                "is_active_flg",
                "is_required_flg",
                "is_delta_required_flg",
                "is_status_check_flg",
                "is_resource_status_check_flg",
            ],
            others=["code_delivery_rk"],
            filter_expression=f"""
                    owner_flow_rk != -1
                AND code_delivery_rk = :code_delivery_rk
            """,
            params={"code_delivery_rk": self._module_id},
            log=log,
        )

        log.info("end")
