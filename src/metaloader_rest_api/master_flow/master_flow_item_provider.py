from typing import Con<PERSON><PERSON><PERSON><PERSON>, Iterator, <PERSON><PERSON>, <PERSON><PERSON>

from sqlalchemy.orm import Session
from structlog.stdlib import <PERSON><PERSON><PERSON><PERSON><PERSON>, get_logger

from metaloader_rest_api.common_model import LAST_DATE
from metaloader_rest_api.db.db_provider import db_provider

_DEFAULT_LOGGER = get_logger(__name__)


MasterFlowItemProvider = ContextManager[Iterator[Tuple[str]]]


def master_flow_item_provider(
    session: Session,
    module_id: int,
    fetch_size: Optional[int] = None,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> MasterFlowItemProvider:
    return db_provider(
        session=session,
        query=f"""
            SELECT DISTINCT 
                   layout_path_name
              FROM metamodel.bridge_master_flow
             WHERE code_delivery_rk = :code_delivery_rk
               AND effective_to_dttm = :effective_to_dttm
               AND deleted_flg IS FALSE
        """,
        params={
            "code_delivery_rk": module_id,
            "effective_to_dttm": LAST_DATE,
        },
        fetch_size=fetch_size,
        log=log.bind(subject="master_flow"),
    )
