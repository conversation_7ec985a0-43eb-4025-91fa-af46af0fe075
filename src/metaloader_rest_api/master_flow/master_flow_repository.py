from datetime import datetime

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, bindparam, text
from sqlalchemy.dialects.postgresql import JSON<PERSON>
from sqlalchemy.orm import Session
from sqlalchemy.sql.sqltypes import BIGINT, TEXT
from structlog.stdlib import <PERSON><PERSON><PERSON><PERSON><PERSON>, get_logger

from metaloader_rest_api import etl
from metaloader_rest_api.common_repository import (
    BufferedStageBatchRepository,
    StageBatchRepository,
)
from metaloader_rest_api.master_flow.master_flow_model import MasterFlow

_DEFAULT_LOGGER = get_logger(__name__)


class MasterFlowRepository(BufferedStageBatchRepository[MasterFlow]):
    def __init__(
        self,
        session: Session,
        module_id: int,
        version_id: int,
        effective_date: datetime,
        table_id: str,
        page_size: int = StageBatchRepository.PAGE_SIZE_DEFAULT,
        log: BoundLogger = _DEFAULT_LOGGER,
    ):
        table = "mart_master_flow"
        super().__init__(
            session,
            table,
            table_id,
            self._create_statement,
            self._load_statement,
            page_size,
            log,
        )
        self._module_id = module_id
        self._version_id = version_id
        self._effective_date = effective_date

    @property
    def module_id(self) -> int:
        return self._module_id

    @module_id.setter
    def module_id(self, value: int) -> None:
        self._module_id = value

    @property
    def version_id(self) -> int:
        return self._version_id

    @version_id.setter
    def version_id(self, value: int) -> None:
        self._version_id = value

    @property
    def effective_date(self) -> datetime:
        return self._effective_date

    @effective_date.setter
    def effective_date(self, value: datetime) -> None:
        self._effective_date = value

    def __exit__(self, exc_type, exc_value, traceback):
        try:
            return super().__exit__(exc_type, exc_value, traceback)
        except Exception as exception:
            exc_type = type(exception)
            raise exception
        finally:
            if exc_type is None:
                self.load_bridge()

    def _prepare_load_statement(self) -> TextClause:
        statement = super()._prepare_load_statement()
        statement = statement.bindparams(
            bindparam("id", type_=BIGINT()),
            bindparam("layout_path", type_=TEXT()),
            bindparam("params", type_=JSONB()),
        )
        return statement

    @staticmethod
    def _create_statement(table: str) -> str:
        return f"""
            CREATE TABLE {table} (
                master_flow_rk   BIGINT
              , layout_path_name TEXT
              , parameters_json  JSONB
            )
        """

    @staticmethod
    def _load_statement(table: str) -> str:
        return f"""
            INSERT INTO {table} (master_flow_rk
                               , layout_path_name
                               , parameters_json)
            VALUES (:id
                  , :layout_path
                  , :params)
        """

    def load_bridge(self) -> None:
        log = self._log.bind(
            action="load_bridge",
            table="bridge_master_flow",
        )
        log.info("begin", table_id=self._table_id)

        merge_stage = f"stg.bridge_master_flow_{self._table_id}"

        log.info("create merge_stage")
        self._session.execute(text(f"DROP TABLE IF EXISTS {merge_stage}"))
        self._session.execute(
            text(f"""
                CREATE TABLE {merge_stage} (
                    master_flow_rk   BIGINT
                  , layout_path_name TEXT
                  , parameters_json  JSONB
                  , parameters_hash  BIGINT
                  , code_delivery_rk SMALLINT
                )
            """)
        )
        self._session.execute(
            statement=text(f"""
                INSERT INTO {merge_stage} (master_flow_rk
                                         , layout_path_name
                                         , parameters_json
                                         , parameters_hash
                                         , code_delivery_rk)
                     SELECT master_flow_rk
                          , layout_path_name
                          , parameters_json
                          , JSONB_HASH_EXTENDED(parameters_json, 0) parameters_hash
                          , :code_delivery_rk
                       FROM {self.table}
            """),
            params={
                "code_delivery_rk": self._module_id,
            },
        )

        log.info("merge")
        etl.merge(
            session=self._session,
            version_id=self._version_id,
            effective_date=self._effective_date,
            table="metamodel.bridge_master_flow",
            stage=merge_stage,
            primary_key=None,
            keys=["master_flow_rk"],
            values=[
                "layout_path_name",
                "parameters_hash",
            ],
            others=[
                "parameters_json",
                "code_delivery_rk",
            ],
            filter_expression="code_delivery_rk = :code_delivery_rk",
            params={"code_delivery_rk": self._module_id},
            log=log,
        )

        log.info("end")
