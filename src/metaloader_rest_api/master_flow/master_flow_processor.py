from pathlib import PurePath
from typing import Any, Dict, Mapping, <PERSON><PERSON>

import metaloader_rest_api.master_flow.master_flow_imports as master_flow_instance_imports


def process_master_flow(
    path: str,
    source: str,
) -> Tuple[str, Mapping[str, Any]]:
    locals_ = {}
    exec(source, _globals(path), locals_)
    return locals_["dag"]


def _globals(path: str) -> Dict[str, Any]:
    return {
        "__builtins__": {"__import__": _import},
        "__file__": PurePath(path).stem,
        "Path": master_flow_instance_imports.Path,
        "ControlFlowLoader": master_flow_instance_imports.ControlFlowLoader,
        "ControlFLowBuilder": master_flow_instance_imports.ControlFLowBuilder,
    }


def _import(*args, **kwargs):
    return master_flow_instance_imports
