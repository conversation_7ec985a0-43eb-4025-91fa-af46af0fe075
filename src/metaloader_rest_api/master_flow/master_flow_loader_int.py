from datetime import datetime
from pathlib import <PERSON>PosixPath
from typing import Callable, Context<PERSON>anager, Optional
from uuid import UUID

from sqlalchemy.orm import Session
from structlog.stdlib import BoundLogger, get_logger

from metaloader_rest_api.airflow.airflow_auth import airflow_auth
from metaloader_rest_api.airflow.airflow_dag_source_loader import (
    airflow_dag_source_loader,
)
from metaloader_rest_api.ceh_module.ceh_module_provider import (
    provide_ceh_module_master_flow_layout_path,
)
from metaloader_rest_api.ceh_service.ceh_service_provider import (
    provide_ceh_service_urls,
)
from metaloader_rest_api.common_param import (
    DB_COMMIT,
    DB_FETCH_SIZE_DEFAULT,
    DB_PAGE_SIZE_DEFAULT,
    FAILSAFE_THRESHOLD_DEFAULT,
    HTTP_PAGE_SIZE_DEFAULT,
    HTTP_RETRIES_DEFAULT,
    HTTP_TIMEOUT_DEFAULT,
)
from metaloader_rest_api.common_repository import SessionResource
from metaloader_rest_api.deployment import get_deployment
from metaloader_rest_api.http.http_client import http_client
from metaloader_rest_api.master_flow.master_flow_factory import master_flow_factory
from metaloader_rest_api.master_flow.master_flow_model import MasterFlow
from metaloader_rest_api.master_flow.master_flow_processor import process_master_flow
from metaloader_rest_api.master_flow.master_flow_provider import (
    MasterFlowProvider,
    master_flow_provider,
)
from metaloader_rest_api.master_flow.master_flow_repository import MasterFlowRepository

_DEFAULT_LOGGER = get_logger(__name__)


def load_master_flows(
    session: Session,
    load_id: UUID,
    module: str,
    service: str,
    release: str,
    effective_date: datetime,
    http_timeout: float = HTTP_TIMEOUT_DEFAULT,
    http_retries: int = HTTP_RETRIES_DEFAULT,
    http_page_size: int = HTTP_PAGE_SIZE_DEFAULT,
    db_fetch_size: int = DB_FETCH_SIZE_DEFAULT,
    db_page_size: int = DB_PAGE_SIZE_DEFAULT,
    db_commit: Optional[bool] = DB_COMMIT,
    fail_threshold: int = FAILSAFE_THRESHOLD_DEFAULT,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    log = log.bind(
        action="load_master_flows",
        load_id=load_id,
        module=module,
        service=service,
        release=release,
        effective_date=effective_date,
    )
    log.info(
        "begin",
        http_timeout=http_timeout,
        http_retries=http_retries,
        http_page_size=http_page_size,
        db_fetch_size=db_fetch_size,
        db_page_size=db_page_size,
        db_commit=db_commit,
        fail_threshold=fail_threshold,
    )
    try:
        module_id, service_id, version_id = get_deployment(
            session,
            module,
            service,
            release,
            effective_date,
            log,
        )
        airflow_url, _ = provide_ceh_service_urls(session, service_id)
        master_flow_path = _master_flow_path(
            provide_ceh_module_master_flow_layout_path(session, module_id, log)
        )
        log.info(
            "init",
            module_id=module_id,
            version_id=version_id,
            airflow_url=airflow_url,
            master_flow_path=master_flow_path,
        )
        with SessionResource(session, db_commit, log):
            _load_master_flows(
                master_flow_repository=MasterFlowRepository(
                    session=session,
                    module_id=module_id,
                    version_id=version_id,
                    effective_date=effective_date,
                    table_id=load_id.hex,
                    page_size=db_page_size,
                    log=log,
                ),
                master_flow_loader=airflow_dag_source_loader(
                    http_client=http_client(
                        url=airflow_url,
                        auth=airflow_auth(),
                        timeout=http_timeout,
                        retries=http_retries,
                    ),
                    log=log,
                ),
                master_flow_provider=master_flow_provider(
                    session=session,
                    module_id=module_id,
                    path=master_flow_path,
                    fetch_size=db_fetch_size,
                    log=log,
                ),
                fail_threshold=fail_threshold,
                log=log,
            )
    except Exception:
        log.exception("fail")
        raise
    log.info("end")

    return None


def _load_master_flows(
    master_flow_repository: ContextManager[Callable[[MasterFlow], None]],
    master_flow_loader: ContextManager[Callable[[str], str]],
    master_flow_provider: MasterFlowProvider,
    fail_threshold: int = FAILSAFE_THRESHOLD_DEFAULT,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    with (
        master_flow_provider as master_flows,
        master_flow_loader as load_master_flow,
        master_flow_repository as store_master_flow,
    ):
        if fail_threshold == 0:
            log.warning("fail_threshold_off")
        fails = fail_threshold
        for (
            master_flow_id,
            master_flow_name,
            master_flow_path,
            master_flow_token,
        ) in master_flows:
            try:
                store_master_flow(
                    master_flow_factory(
                        master_flow_id,
                        *process_master_flow(
                            master_flow_path,
                            load_master_flow(master_flow_token),
                        ),
                    )
                )
                fails = fail_threshold
            except Exception:
                fails -= 1
                log.exception("fail", master_flow=master_flow_name)
                if fails == 0:
                    log.error("fail_threshold_reached")
                    raise RuntimeError(f"Fail threshold {fail_threshold} reached")


def _master_flow_path(path: str) -> str:
    return str(PurePosixPath(path).parent)
