from datetime import datetime
from uuid import UUID

from pydantic import BaseModel
from sqlalchemy.orm import Session
from structlog.stdlib import Bo<PERSON><PERSON>ogger, get_logger

from metaloader_rest_api.common_param import (
    DbOptions,
    DeploymentInfo,
    FailsafeOptions,
    HttpOptions,
)
from metaloader_rest_api.master_flow import master_flow_item_loader_int
from metaloader_rest_api.yaml.yaml_loader import YAML_LOADER_DEFAULT, YamlLoader

_DEFAULT_LOGGER = get_logger(__name__)


class LoadMasterFlowItemsParams(BaseModel):
    deployment: DeploymentInfo = DeploymentInfo()
    incremental: bool = True
    http_options: HttpOptions = HttpOptions()
    db_options: DbOptions = DbOptions()
    yaml_loader: YamlLoader = YAML_LOADER_DEFAULT
    failsafe_options: FailsafeOptions = FailsafeOptions()


def load_master_flow_items(
    session: Session,
    load_id: UUID,
    effective_date: datetime,
    params: LoadMasterFlowItemsParams,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    return master_flow_item_loader_int.load_master_flow_items(
        session=session,
        load_id=load_id,
        module=params.deployment.module,
        service=params.deployment.service,
        release=params.deployment.release,
        effective_date=effective_date,  # params.deployment.effective_date
        incremental=params.incremental,
        http_timeout=params.http_options.timeout,
        http_retries=params.http_options.retries,
        http_page_size=params.http_options.page_size,
        db_fetch_size=params.db_options.page_size,
        db_page_size=params.db_options.page_size,
        db_commit=params.db_options.commit,
        yaml_loader=params.yaml_loader,
        fail_threshold=params.failsafe_options.threshold,
        log=log,
    )
