from pathlib import PurePath
from typing import Any, Dict, Iterator, List

from metaloader_rest_api.master_flow.master_flow_item_model import (
    MASTER_FLOW_ITEM_SEPARATOR,
    MasterFlowItem,
    master_flow_dependency_item,
    master_flow_group_item,
    master_flow_item,
    master_flow_step_item,
    master_flow_unit_item,
)


def master_flow_item_factory(
    name: str,
    flow: Dict[str, Any],
) -> Iterator[MasterFlowItem]:
    name = _flow(name)
    groups = _next(flow, "groups")

    yield master_flow_item(name, flow)
    yield from _groups(name, groups)


def _flow(name: str) -> str:
    return PurePath(name).stem


def _groups(
    path: str,
    groups: List[Dict[str, Any]],
) -> Iterator[MasterFlowItem]:
    for index, group in enumerate(groups):
        name = _group(group)
        steps = _next(group, "steps")

        yield master_flow_group_item(name, path, index, group)
        yield from _steps(_path(path, name), steps)


def _group(group: Dict[str, Any]) -> str:
    return group.pop("group_id")


def _steps(
    path: str,
    steps: List[Dict[str, Any]],
) -> Iterator[MasterFlowItem]:
    for index, step in enumerate(steps):
        name = _step(step, "cf_flow_name", "cf_flow_id")
        path_ = _path(path, name)
        dependencies = _next(step, "dependencies")

        yield master_flow_step_item(name, path, index)
        yield _unit(path_, step)
        yield from _dependencies(path_, dependencies)


def _unit(
    path: str,
    unit: Dict[str, Any],
) -> MasterFlowItem:
    name = _step(unit, "wrk_flow_name", "wrk_flow_id")

    return master_flow_unit_item(name, path, unit)


def _step(
    steps: Dict[str, Any],
    name: str,
    another_name: str,
) -> str:
    return steps.pop(name, None) or steps.pop(another_name)


def _dependencies(
    path: str,
    dependencies: List[str],
) -> Iterator[MasterFlowItem]:
    for index, dependency in enumerate(dependencies):
        yield master_flow_dependency_item(dependency, path, index)


def _next(
    value: Dict[str, Any],
    key: str,
) -> List[Any]:
    return value.pop(key, [])


def _path(
    path: str,
    name: str,
) -> str:
    return f"{path}{MASTER_FLOW_ITEM_SEPARATOR}{name}"
