from datetime import datetime, timezone
from functools import partial
from typing import Optional

from pydantic import BaseModel, Field

DEPLOYMENT_MODULE_DEFAULT = "ETL-SCALE-core"
DEPLOYMENT_SERVICE_DEFAULT = "prod1"
DEPLOYMENT_RELEASE_DEFAULT = "999.999.999"

RELEASE_PATTERN = r"\d+\.\d+\.\d+"
EFFECTIVE_DATE_FACTORY = partial(datetime.now, tz=timezone.utc)


class DeploymentInfo(BaseModel):
    module: str = DEPLOYMENT_MODULE_DEFAULT
    service: Optional[str] = DEPLOYMENT_SERVICE_DEFAULT
    release: str = Field(pattern=RELEASE_PATTERN, default=DEPLOYMENT_RELEASE_DEFAULT)
    effective_date: datetime = Field(default_factory=EFFECTIVE_DATE_FACTORY)


DB_FETCH_SIZE_DEFAULT = 100
DB_PAGE_SIZE_DEFAULT = 100
DB_COMMIT = None


class DbOptions(BaseModel):
    fetch_size: Optional[int] = Field(ge=0, default=DB_FETCH_SIZE_DEFAULT)
    page_size: Optional[int] = Field(ge=0, default=DB_PAGE_SIZE_DEFAULT)
    commit: Optional[bool] = DB_COMMIT


HTTP_TIMEOUT_DEFAULT = 5.0
HTTP_RETRIES_DEFAULT = 3
HTTP_PAGE_SIZE_DEFAULT = 100


class HttpOptions(BaseModel):
    timeout: float = Field(ge=0, default=HTTP_TIMEOUT_DEFAULT)
    retries: int = Field(ge=0, default=HTTP_RETRIES_DEFAULT)
    page_size: Optional[int] = Field(ge=0, default=HTTP_PAGE_SIZE_DEFAULT)


FAILSAFE_THRESHOLD_DEFAULT = 0


class FailsafeOptions(BaseModel):
    threshold: int = Field(ge=0, default=FAILSAFE_THRESHOLD_DEFAULT)
