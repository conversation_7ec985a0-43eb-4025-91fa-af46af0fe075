"""
Переработанная пидантик-модель (с расплющенным наследованием) взята из
ceh-services/dwh-services-utils/src/dwh_services_utils/schemas/ceh_provider/v0_6/schemas.py
"""

from enum import Enum
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field


class ConnectionUsage(Enum):
    METRICS = "metrics"
    DATA = "data"

    def __str__(self):
        return "data, metrics"


class Status(BaseModel):
    """Статус Ресурса"""

    is_readonly: Optional[bool] = Field(
        None, description="Может ли провайдер менять Состояние Ресурса."
    )
    is_maintenance: Optional[bool] = Field(
        None,
        description="Может ли не-администратор менять Состояние Ресурса или сам Ресурс.",
    )
    is_deleted: Optional[bool] = Field(False, description="Признак софт-удаления.")


class Location(BaseModel):
    """Расположение данных"""

    profile: str = Field(
        None, description="Имя профиля (например, jdbc или hdfs:avro)."
    )
    server: str = Field(
        None, description="Имя сервера в jdbc-site.xml (или аналогичном)."
    )
    options: Optional[Dict[str, Any]] = Field(
        None, description="Настройки подключения."
    )


class Type(Enum):
    text = "text"
    csv = "csv"
    custom = "custom"


class FieldType(Enum):
    bigint = "bigint"
    boolean = "boolean"
    char = "char"
    date = "date"
    decimal = "decimal"
    double = "double"
    integer = "integer"
    numeric = "numeric"
    smallint = "smallint"
    time = "time"
    timestamp = "timestamp"
    varchar = "varchar"


class Format(BaseModel):
    """Представление данных"""

    type: Type = Field(..., description="Формат данных.")
    options: Optional[Dict[str, Any]] = Field(None, description="Настройки формата.")


class LocationFDW(BaseModel):
    """Описание FDW сервера, хранящего данные."""

    wrapper: str = Field(None, description="Имя FDW wrapper.")
    server: Optional[str] = Field(
        None, description="Имя сервера (если он создан через CREATE SERVER)."
    )
    kid: Optional[str] = Field(
        None,
        description="Имя кластера (KID). Задается только для соединений с GPDB (wrapper=greenplum_fdw).",
    )
    options: Optional[Dict[str, Any]] = Field(
        None, description="Настройки подключения."
    )


class BindParam(BaseModel):
    name: str = Field(None, description="Имя подставляемого в sql запрос параметра")
    value: Any = Field(..., description="Значение параметра.")
    sqltype: FieldType = Field(..., description="Тип параметра.")


class UniConnectionODBC(BaseModel):
    """Определения соединений"""

    type: str = Field(..., description="Тип Соединения (odbc).")
    usage: ConnectionUsage = Field(
        ConnectionUsage.METRICS, description="Назначение Соединения."
    )
    dsn: str = Field(..., description="Имя DSN (data source name).")
    options: Optional[Dict[str, Any]] = Field(
        None, description="Настройки ODBC-драйвера."
    )


class UniConnectionPXF(BaseModel):
    """Определение Соединения по PXF"""

    type: str = Field(..., description="Тип Соединения (pxf).")
    usage: ConnectionUsage = Field(
        ConnectionUsage.DATA, description="Назначение Соединения."
    )
    location: Location = Field(..., description="Расположение данных.")
    format: Format = Field(..., description="Представление данных.")


class UniConnectionFDW(BaseModel):
    """Определение Соединения по PXF"""

    type: str = Field(..., description="Тип Соединения (fdw).")
    usage: ConnectionUsage = Field(
        ConnectionUsage.DATA, description="Назначение Соединения."
    )
    location: LocationFDW = Field(
        ..., description="Описание FDW сервера, хранящего данные."
    )


class UniMetricDefinition(BaseModel):
    """Определение расчетной Метрики"""

    id: str = Field(
        None, description="Ид Метрики.  Должен быть уникальным. PK, не меняется."
    )
    connection: Optional[str] = Field(
        None,
        description="Имя Соединения, из которого провайдер получит значение Метрики. "
        "Сейчас провайдер умеет подключаться только к соединениям ODBC. "
        "Если провайдер не сможет подключиться, значением метрики будет значение default.",
    )
    query: Optional[str] = Field(
        None,
        description="Запрос, возвращающий значение Метрики. Если запрос будет неуспешен или ничего не вернет, "
        "значением метрики будет значение default.",
    )
    query_parameters: Optional[List[BindParam]] = Field(
        None,
        description="Параметры для подстановки в sql-запрос",
    )
    query_exec_timeout: Optional[int] = Field(
        None,
        description="Максимальное время выполнения запроса (в секундах)",
    )
    default: Any = Field(
        ...,
        description="Значение, которое будет выведено в случае сбоя получения значения Метрики.",
    )
    refresh: Optional[str] = Field(
        "PT0S",
        description="Длительность, в течение которой значение Метрики не будет перезапрашиваться "
        "(относительно as_of_dttm).",
    )
    alias: Optional[str] = Field(
        "",
        description="Выражение по которому считается метрика (jq или ссылка на другую метрику)",
    )


class DatasetDefinition(BaseModel):
    """Определение Набора данных."""

    name: str = Field(
        ..., description="Наименование Набора данных. Обычно имя таблицы или вью."
    )
    schema_name: Optional[str] = Field(
        None,
        description="Имя схемы, в которой находится Набор данных. Или аналога - каталога, библиотеки и т.п.",
    )
    filter: Optional[str] = Field(
        None,
        description="Дополнительный фильтр, если Ресурс покрывает только часть Набора данных. "
        "Может быть использован при отборе данных из Соединения data. "
        "Записывается в формате where-условия, соответствующего типу Соединения.",
    )
    physical_options: Optional[Dict[str, Any]] = None


class ResourceUniRequest(BaseModel):
    """
    Запрос создания/обновления Ресурса.
    """

    resource_cd: str = Field(None, description="Код ресурса. PK, не меняется.")
    resource_desc: Optional[str] = Field(None, description="Описание Ресурса")
    tags: Optional[List[str]] = Field(None, description="Тэги")
    # Расширен тип для поддержки числовых и булевых значений в JSON файлах
    features: Optional[Dict[str, Any]] = Field(None, description="Свойства")
    status: Optional[Status] = Field(None, description="Статус Ресурса.")
    # Расширен тип для поддержки строковых шаблонов ("{{ jdbc_conn | ... }}") и нестандартных структур в JSON файлах
    connections: Optional[
        Union[
            Dict[str, Union[UniConnectionODBC, UniConnectionPXF, UniConnectionFDW]],
            Dict[str, Any],
            str,
        ]
    ] = Field(None, description="Сведения о подключении к Ресурсу.")
    datasets: Optional[List[DatasetDefinition]] = Field(
        None, description="Определения Наборов Данных, которые покрывает Ресурс."
    )
    # Расширен тип для поддержки строковых шаблонов ("{{ metrics.mac_last_update_date | ... }}") в полях метрик и субполях метрик
    metrics: Optional[Dict[str, Any]] = Field(
        None,
        description="Определения расчетных Метрик (состояния) Ресурса. Каждый Ресурс должен, как минимум, "
        "иметь Метрики is_locked и locked_by. Имя as_of_dttm зарезервировано и не может использоваться.",
    )
