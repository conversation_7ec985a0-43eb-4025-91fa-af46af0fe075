"""
Команда `ceh-services` использует сильно другую версию пидантика, поэтому просто
импортировать проблематично. Аналогичный подход со схемами мастер-потоков на март 2025
не создал проблем, поэтому пока сойдёт и для ресурсов.

Модули с пидантик-моделями скопировал из их репо не as-is, т.к. там классы сильно вложены друг в друга
через наследование из-за чего их трудно читать + есть не нужные нам зависимости.
"""

from ._ceh_provider import Resource as CehResource
from ._uni_provider import ResourceUniRequest as UniResource

__all__ = [CehResource, UniResource]
