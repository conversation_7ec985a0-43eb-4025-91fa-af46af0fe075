"""
Переработанная пидантик-модель (с расплющенным наследованием) взята из
ceh-services/dwh-services-utils/src/dwh_services_utils/schemas/ceh_provider/v0_6/schemas.py
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field


class VersionSummary(BaseModel):
    """Краткое представление Версии."""

    version_id: int


class ResourceCEHStateSummary(BaseModel):
    """Краткое представление состояния Ресурса."""

    version: Optional[VersionSummary] = None


class ResourceODSState(BaseModel):
    """Состояние ресурса ODS."""

    max_effective_to_csn: Optional[int] = None
    max_effective_to_dt: Optional[datetime] = None
    max_processed_dt: Optional[datetime] = None
    as_of_dttm: Optional[datetime] = None
    is_locked: Optional[bool] = None
    locked_by: Optional[int] = None


class MetricDefinition(BaseModel):
    """Определение метрики."""

    id: Optional[str] = Field(None, description="Ид Метрики.")
    query: Optional[str] = Field(
        None,
        description="Запрос к РесурсСостоянию, возвращающий значение Метрики.\n"
        "Если запрос будет неуспешен, значением Метрики будет значение default.\n",
    )
    default: Any = Field(
        ...,
        description="Значение, которое будет выведено, если значение Метрики не установлено иначе.\n",
    )


class ColumnDefinition(BaseModel):
    """Определение поля Набора данных"""

    name: str = Field(
        None,
        description="Имя поля Набора данных.",
        examples=["customer_rk"],
    )
    type: Optional[str] = Field(
        None,
        description="Тип поля Набора данных.",
        examples=["text"],
    )
    primary_key: Optional[bool] = Field(
        None,
        description="Входит ли поле в PK",
        examples=[True],
    )
    nullable: Optional[bool] = Field(
        None,
        description="Может ли поле содержать null",
        examples=[True],
    )


class DatasetDefinition(BaseModel):
    """Определение Набора данных"""

    name: str = Field(
        None,
        description="Наименование Набора данных.",
        examples=["hsat_customer"],
    )
    schema_name: Optional[str] = Field(
        None,
        description="Имя схемы, в которой находится Набор данных. Или аналога - каталога, библиотеки и т.п.",
        examples=["rdv"],
    )
    filter: Optional[str] = Field(
        None,
        description="Дополнительный фильтр, если Ресурс покрывает только часть Набора данных.\n"
        "Может быть использован при отборе данных из Соединения data.\n"
        "Записывается в формате where-условия, соответствующего типу Соединения.",
        examples=["status_code in (0, 1)"],
    )
    columns: Optional[List[ColumnDefinition]] = Field(
        None,
        description="Описание полей Набора данных",
        examples=["[col1, col2, col3]"],
    )
    physical_options: Optional[Any] = Field(
        None,
        description="Дополнительные настройки при создании таблицы",
        examples=["distribute_type=column"],
    )
    actual_dttm: Optional[datetime] = Field(
        None,
        description="Дата последней успешной валидации датасета",
    )


class Resource(BaseModel):
    """
    Полное представление Ресурса.
    """

    resource_cd: str
    resource_desc: str
    tags: Optional[List[str]] = None
    # Расширен тип для поддержки числовых (p_mortgage_days_amt: 90) и булевых (p_skip_doubles_pk_flg: true) значений в JSON файлах
    features: Optional[Dict[str, Any]] = None
    # Расширен тип для поддержки пустых строк ("") в JSON файлах
    metrics: Optional[Union[str, Dict[str, Any]]] = Field(
        None,
        description="Определения Метрик, т.е. показателей (Состояния) Ресурса.\n"
        "Имена as_of_dttm, is_locked и locked_by зарезервированы и не могут использоваться.\n",
        examples=[
            {
                "cftm_actual_dttm": {
                    "id": "ceh.rdv.hub_customer.customer_inn:cftm_actual_dttm",
                    "query": '[.last_sources[].conf.by_src  | select(. != null) | .[] | to_entries | .[] | select((.key | endswith("_actual_dttm")) and .value != "default_value" and .value != null).value] | min',
                    "default": "default_value",
                }
            }
        ],
    )
    is_readonly: bool = False
    is_deleted: Optional[bool] = False
    datasets: Optional[List[DatasetDefinition]] = Field(
        None,
        description="Определения Наборов Данных, которые покрывает Ресурс.",
        examples=[
            {
                "name": "hsat_customer",
                "schema_name": "rdv",
                "filter": "status_code in (0, 1)",
                "columns": [
                    {
                        "name": "field1",
                        "type": "text",
                        "primary_key": True,
                        "nullable": True,
                    }
                ],
            }
        ],
    )
    # Расширен тип для обработки null значений и нестандартных структур state в JSON файлах
    state: Union[ResourceCEHStateSummary, ResourceODSState, Dict[str, Any], None] = None
