from typing import Iterator, <PERSON><PERSON>

from sqlalchemy import Sequence, text
from sqlalchemy.orm import Session
from structlog import <PERSON><PERSON><PERSON><PERSON><PERSON>
from structlog.stdlib import get_logger

from metaloader_rest_api.common_model import LAST_DATE
from metaloader_rest_api.flow.flow_model import FlowType
from metaloader_rest_api.flow_processor.flow_provider import FlowProvider

_DEFAULT_LOGGER = get_logger(__name__)


class FlowDBProvider(FlowProvider):
    default_fetch_size = 100

    def __init__(
        self,
        session: Session,
        module_id: int,
        fetch_size: int = default_fetch_size,
        log: BoundLogger = _DEFAULT_LOGGER,
    ):
        self._session = session
        self._module_id = module_id
        self._fetch_size = fetch_size
        self._log = log.bind(actor="flow_db_provider", module_id=module_id)

    def __enter__(self):
        self._log.info("prepare", fetch_size=self._fetch_size)
        self._result = self._session.execute(
            statement=text(f"""
                SELECT flow_rk
                     , flow_name
                  FROM metamodel.bridge_flow
                 WHERE effective_to_dttm = :effective_to_dttm
                   AND deleted_flg IS FALSE
                   AND code_delivery_rk = :code_delivery_rk
                   AND flow_type_rk IN ({FlowType.CONTROL}
                                      , {FlowType.WORK}
                                      , {FlowType.SERVICE})
            """),
            params={
                "code_delivery_rk": self._module_id,
                "effective_to_dttm": LAST_DATE,
            },
            execution_options={
                "stream_results": True,
                "yield_per": self._fetch_size,
            },
        )

        return self

    def __exit__(self, exc_type, exc_value, traceback):
        pass

    def get_flows(self) -> Iterator[Tuple[int, str]]:
        self._log.info("get_flows")
        for partition in self._result.partitions():
            for row in partition:
                yield row[0], row[1]

    def get_flows_paths(self) -> Sequence[str]:
        paths = self._session.scalars(
            statement=text(f"""
                SELECT code_folder_path
                  FROM dict.dict_code_folder
                 WHERE code_folder_rk IN (SELECT code_folder_rk
                                            FROM dict.link_code_delivery_folder
                                           WHERE code_delivery_rk = :code_delivery_rk
                                             AND is_flow_flg IS TRUE)
            """),
            params={
                "code_delivery_rk": self._module_id,
            },
        ).all()

        if not paths:
            raise RuntimeError("Flow paths are empty")

        return paths
