from datetime import datetime
from typing import Any, Callable, List, Mapping, Optional, Sequence, Type
from uuid import UUID

from sqlalchemy.orm import Session
from structlog.stdlib import Bo<PERSON><PERSON>ogger, get_logger

from metaloader_rest_api.ceh_service.ceh_service_provider import (
    provide_ceh_service_urls,
)
from metaloader_rest_api.common_param import (
    DB_COMMIT,
    DB_FETCH_SIZE_DEFAULT,
    DB_PAGE_SIZE_DEFAULT,
    FAILSAFE_THRESHOLD_DEFAULT,
    HTTP_RETRIES_DEFAULT,
    HTTP_TIMEOUT_DEFAULT,
)
from metaloader_rest_api.common_repository import SessionResource
from metaloader_rest_api.deployment import get_deployment
from metaloader_rest_api.file_provider.file_provider import (
    FileProvider,
    FileProviderCheckError,
)
from metaloader_rest_api.flow_processor.flow_db_provider import FlowDBProvider
from metaloader_rest_api.flow_processor.flow_path_factory import get_flow_paths_factory
from metaloader_rest_api.flow_processor.flow_processor import FlowProcessor
from metaloader_rest_api.flow_processor.flow_processor_registry import (
    FlowProcessorRegistry,
)
from metaloader_rest_api.flow_processor.flow_provider import FlowProvider
from metaloader_rest_api.http_file_provider.http_file_provider import HttpFileProvider
from metaloader_rest_api.yaml.bytes_iterator_yaml_stream import (
    get_bytes_iterator_yaml_stream,
)
from metaloader_rest_api.yaml.yaml_loader import (
    YAML_LOADER_DEFAULT,
    Yaml,
    YamlLoader,
    get_yaml_loader,
)
from metaloader_rest_api.yaml.yaml_stream import ReadableYamlStream, YamlStream

_DEFAULT_LOGGER = get_logger(__name__)


FLOW_PROCESSORS_IGNORE_MISSING_FLOWS = True


def process_flows(
    session: Session,
    load_id: UUID,
    module: str,
    service: str,
    release: str,
    effective_date: datetime,
    flow_processors: List[str],
    yaml_loader: YamlLoader = YAML_LOADER_DEFAULT,
    ignore_missing_flows: bool = FLOW_PROCESSORS_IGNORE_MISSING_FLOWS,
    db_fetch_size: int = DB_FETCH_SIZE_DEFAULT,
    db_page_size: int = DB_PAGE_SIZE_DEFAULT,
    db_commit: Optional[bool] = DB_COMMIT,
    http_timeout: float = HTTP_TIMEOUT_DEFAULT,
    http_retries: int = HTTP_RETRIES_DEFAULT,
    failsafe_threshold: int = FAILSAFE_THRESHOLD_DEFAULT,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> Mapping[str, Any]:
    log = log.bind(
        action="process_flows",
        load_id=load_id,
        module=module,
        service=service,
        release=release,
        effective_date=effective_date,
    )
    log.info(
        "begin",
        fetch_size=db_fetch_size,
        page_size=db_page_size,
        yaml_loader=yaml_loader,
        timeout=http_timeout,
        retries=http_retries,
        fail_threshold=failsafe_threshold,
    )

    try:
        module_id, service_id, version_id = get_deployment(
            session,
            module,
            service,
            release,
            effective_date,
            log,
        )

        _, flow_layout_url = provide_ceh_service_urls(session, service_id)

        flow_db_provider = FlowDBProvider(
            session,
            module_id,
            db_fetch_size,
            log,
        )
        flows_paths = flow_db_provider.get_flows_paths()

        log.info(
            "init",
            module_id=module_id,
            version_id=version_id,
            flow_layout_url=flow_layout_url,
            flows_paths=flows_paths,
        )

        chunk_size = ReadableYamlStream.buffer_size

        http_file_provider = HttpFileProvider(
            flow_layout_url,
            http_timeout,
            http_retries,
            chunk_size,
            log,
        )

        yaml_loader = get_yaml_loader(yaml_loader)

        flow_processor = FlowProcessorRegistry(
            session,
            http_file_provider,
            load_id,
            module_id,
            version_id,
            effective_date,
            db_page_size,
            flow_processors,
            log,
        )

        session_resource = SessionResource(
            session,
            db_commit,
            log,
        )
    except Exception:
        log.exception("fail")
        raise

    _process_flows(
        session_resource,
        flow_db_provider,
        flows_paths,
        http_file_provider,
        yaml_loader,
        flow_processor,
        ignore_missing_flows,
        failsafe_threshold,
        log,
    )

    log.info("end")

    return flow_processor.result


def _process_flows(
    session_resource: SessionResource,
    flow_provider: FlowProvider,
    flows_paths: Sequence[str],
    file_provider: FileProvider,
    yaml_loader: Callable[[YamlStream], Yaml],
    flow_processor: FlowProcessor,
    ignore_missing_flows: bool = FLOW_PROCESSORS_IGNORE_MISSING_FLOWS,
    fail_threshold: int = FAILSAFE_THRESHOLD_DEFAULT,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    try:
        with session_resource, flow_provider, file_provider, flow_processor:
            file_provider.check()
            valid_flows_paths = []
            for flows_path in flows_paths:
                try:
                    file_provider.check(f"{flows_path}/")
                    valid_flows_paths.append(flows_path)
                except FileProviderCheckError:
                    log.error("flows_path_does_not_exists", flows_path=flows_path)
            if not valid_flows_paths:
                raise RuntimeError(f"Valid flows paths are empty; {flows_paths=}")
            log.info(
                "flows_paths",
                flows_paths=flows_paths,
                valid_flows_path=valid_flows_paths,
            )

            flow_paths_factory = get_flow_paths_factory(valid_flows_paths)

            if fail_threshold == 0:
                log.warning("fail_threshold_off")
            fails = fail_threshold
            for flow_id, flow_name in flow_provider.get_flows():
                try:
                    flow_bytes = None
                    for flow_path in flow_paths_factory(flow_name):
                        flow_bytes = file_provider.get(flow_path)
                        if flow_bytes:
                            break
                    if flow_bytes is None:
                        if ignore_missing_flows:
                            log.warning("flow_file_not_found", flow=flow_name)
                            continue
                        else:
                            raise RuntimeError(f"File for flow {flow_name} not found")
                    flow_stream = get_bytes_iterator_yaml_stream(flow_name, flow_bytes)
                    flow = yaml_loader(flow_stream)
                    flow_processor.process(flow_id, flow_name, flow)
                    fails = fail_threshold
                except Exception:
                    fails -= 1
                    log.exception("fail", flow_name=flow_name)
                    if fails == 0:
                        log.error("fail_threshold_reached")
                        raise RuntimeError(f"Fail threshold {fail_threshold} reached")
    except Exception:
        log.exception("fail")
        raise


def registry_flow_processors(processors: Mapping[str, Type[FlowProcessor]]) -> None:
    for processor in processors.items():
        FlowProcessorRegistry.register(*processor)
