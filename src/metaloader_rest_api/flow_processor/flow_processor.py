from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Generic, TypeVar
from uuid import UUID

from sqlalchemy.orm import Session
from structlog.stdlib import Bound<PERSON>ogger, get_logger

from metaloader_rest_api.file_provider.file_provider import FileProvider
from metaloader_rest_api.yaml.yaml_loader import Yaml

_DEFAULT_LOGGER = get_logger(__name__)


FlowProcessResult = TypeVar("FlowProcessResult")


class FlowProcessorUnit(ABC, Generic[FlowProcessResult]):
    page_size = 100

    def __init__(
        self,
        file_provider: FileProvider,
        log: BoundLogger = _DEFAULT_LOGGER,
    ):
        self._http_loader = file_provider
        self._log = log

    @abstractmethod
    def process(
        self,
        flow_id: int,
        flow_name: str,
        flow: Yaml,
    ) -> FlowProcessResult: ...


class FlowProcessor(FlowProcessorUnit[FlowProcessResult]):
    page_size = 100

    def __init__(
        self,
        session: Session,
        file_provider: FileProvider,
        load_id: UUID,
        module_id: int,
        version_id: int,
        effective_date: datetime,
        page_size: int = page_size,
        log: BoundLogger = _DEFAULT_LOGGER,
    ):
        super().__init__(
            file_provider,
            log,
        )
        self._session = session
        self._load_id = load_id
        self._module_id = module_id
        self._version_id = version_id
        self._effective_date = effective_date
        self._page_size = page_size

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        pass

    @abstractmethod
    def process(
        self,
        flow_id: int,
        flow_name: str,
        flow: Yaml,
    ) -> FlowProcessResult: ...

    @property
    def result(self) -> Any:
        return None
