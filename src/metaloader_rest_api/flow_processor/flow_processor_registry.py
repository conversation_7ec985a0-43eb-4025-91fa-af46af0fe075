from datetime import datetime
from typing import Any, Dict, Sequence, Type
from uuid import UUID

from sqlalchemy.cyextension.util import Mapping
from sqlalchemy.orm import Session
from structlog.stdlib import BoundLogger, get_logger

from metaloader_rest_api.file_provider.file_provider import FileProvider
from metaloader_rest_api.flow_processor.flow_processor import FlowProcessor
from metaloader_rest_api.yaml.yaml_loader import Yaml

_DEFAULT_LOGGER = get_logger(__name__)


class FlowProcessorRegistry(FlowProcessor[None]):
    _PROCESSORS: Dict[str, Type[FlowProcessor]] = {}

    def __init__(
        self,
        session: Session,
        file_provider: FileProvider,
        load_id: UUID,
        module_id: int,
        version_id: int,
        effective_date: datetime,
        page_size: int = FlowProcessor.page_size,
        processors: Sequence[str] = _PROCESSORS.keys(),
        log: BoundLogger = _DEFAULT_LOGGER,
    ):
        super().__init__(
            session,
            file_provider,
            load_id,
            module_id,
            version_id,
            effective_date,
            page_size,
            log,
        )

        self._processors = {
            processor: self._PROCESSORS[processor](
                session,
                file_provider,
                load_id,
                module_id,
                version_id,
                effective_date,
                page_size,
                log,
            )
            for processor in processors
        }

        self._result = {}

    def __enter__(self):
        super().__enter__()

        for processor in self._processors.values():
            processor.__enter__()

        return self

    def __exit__(self, exc_type, exc_value, traceback):
        try:
            errors = {}
            for processor_name, processor in self._processors.items():
                try:
                    processor.__exit__(exc_type, exc_value, traceback)
                    self._result[processor_name] = processor.result
                except Exception as exception:
                    self._log.exception("fail_processor_exit", processor=processor_name)
                    errors[processor_name] = str(exception)
            if errors:
                raise RuntimeError(f"Processors' exit fail {errors}")
        except Exception as exception:
            if exc_type is None:
                exc_type = RuntimeError
                exc_value = exception
                traceback = exception.__traceback__
            raise exception
        finally:
            return super().__exit__(exc_type, exc_value, traceback)

    def process(
        self,
        flow_id: int,
        flow_name: str,
        flow: Yaml,
    ) -> None:
        for processor in self._processors.values():
            try:
                processor.process(flow_id, flow_name, flow)
            except Exception:
                self._log.exception(
                    "fail_process_flow",
                    flow=flow_name,
                    flow_id=flow_id,
                )

    @property
    def result(self) -> Mapping[str, Any]:
        return self._result

    @classmethod
    def register(
        cls,
        name: str,
        processor: Type[FlowProcessor],
    ) -> None:
        cls._PROCESSORS[name] = processor
