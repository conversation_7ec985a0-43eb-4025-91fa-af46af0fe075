from datetime import datetime
from typing import Any, Optional, Sequence

from sqlalchemy.orm import Session

from metaloader_rest_api.ceh_resource.ceh_resource_legacy_model import (
    ResourceOrder,
    ResourceType,
)
from metaloader_rest_api.common_model import LAST_DATE
from metaloader_rest_api.db.db_provider import db_provide, db_provider

DEFAULT_ORDER = ResourceOrder.ID
DEFAULT_LIMIT = 100

RESOURCE_ORDERS = {
    ResourceOrder.ID: "resource_rk",
    ResourceOrder.NAME: "resource_cd",
}

RESOURCE_TYPE_FILTERS = {
    ResourceType.ALL: "TRUE",
    ResourceType.CEH: "NOT (resource_json ? 'connections')",
    ResourceType.UNI: "resource_json @@ 'exists($.connections) && !(exists($.ods_source_object) || exists($.ods_object))'",
    ResourceType.KAFKA: "resource_json ?& '{connections, ods_source_object, ods_object}'",
    ResourceType.FILE: "FALSE",
}


def names(
    session: Session,
    type: ResourceType,
    effective_date: datetime,
    offset: int,
    limit: int = DEFAULT_LIMIT,
    order: ResourceOrder = DEFAULT_ORDER,
) -> Sequence[str]:
    with db_provider(
        session=session,
        query=f"""
              SELECT resource_cd
                FROM metamodel.bridge_resource
               WHERE {RESOURCE_TYPE_FILTERS[type]}
                 AND effective_from_dttm > :effective_from_date
                 AND effective_to_dttm = :effective_to_date 
            ORDER BY {RESOURCE_ORDERS[order]}
              OFFSET :offset
               LIMIT :limit
        """,
        params={
            "limit": limit,
            "offset": offset,
            "effective_to_date": LAST_DATE,
            "effective_from_date": effective_date,
        },
        fetch_size=limit,
    ) as rows:
        return [row[0] for row in rows]


def resource(
    session: Session,
    name: str,
) -> Any:
    return db_provide(
        session=session,
        query=f"""
            SELECT CASE WHEN deleted_flg
                   THEN NULL
                   ELSE resource_json
                    END
              FROM metamodel.bridge_resource
             WHERE resource_cd = :resource_cd
               AND effective_to_dttm = :effective_to_date
        """,
        params={
            "resource_cd": name,
            "effective_to_date": LAST_DATE,
        },
    )[0]


def effective_date(
    session: Session,
    type: ResourceType,
) -> Optional[datetime]:
    return db_provide(
        session=session,
        query=f"""
            SELECT MAX(effective_from_dttm) 
              FROM metamodel.bridge_resource
             WHERE {RESOURCE_TYPE_FILTERS[type]} 
        """,
        params={},
    )[0]
