from collections.abc import Sequence
from datetime import datetime
from enum import Enum
from typing import Any, Mapping, Optional, Set

from pydantic import BaseModel, Field


class OrderBy(str, Enum):
    """
    Варианты сортировки
    """

    ID = "id"
    NAME = "name"


class ResourceType(str, Enum):
    """
    Тип ресурса
    """

    CEH = "ceh"
    EXTERNAL = "external"

    FILE = "file"
    KAFKA = "kafka"
    DATABASE = "database"


class TableType(str, Enum):
    """
    Тип таблицы
    """

    VIEW = "view"
    TABLE = "table"
    EXTERNAL = "external"  # for pxf
    FUNCTION = "function"
    STRUCTURED_OBJECT = "structured object"


class ConnectionType(str, Enum):
    """
    Тип подключения
    """

    PXF = "pxf"
    ODBC = "odbc"
    FDW = "fdw"
    KAFKA = "kafka"
    SFTP = "sftp"
    HDFS = "hdfs"
    S3API = "s3api"
    LOCAL = "local"


class LinkType(str, Enum):
    """
    Тип связи
    """

    INHERITANCE = "inheritance"
    LATERAL = "lateral"
    DEPENDENCY = "dependency"


class ConnectionUsage(str, Enum):
    """
    Назначение соединения
    """

    DATA = "data"
    METRICS = "metrics"


class Distribution(str, Enum):
    """
    Тип распределения таблиц в хранилище данных
    """

    UNDEFINED = "undefined"
    REPLICATED = "replicated"
    PARTITIONED = "partitioned"


class BaseNamedEntity(BaseModel):
    id: int = Field(..., description="Идентификатор объекта")
    name: str = Field(..., description="Наименование объекта")

    class Config:
        title = "Именованная сущность"
        description = "Определение базовой модели с идентификатором и наименованием"


class BaseEntity(BaseNamedEntity):
    description: Optional[str] = Field(None, description="Описание объекта")

    created_at: datetime = Field(..., description="Дата создания записи")
    updated_at: datetime = Field(..., description="Дата последнего обновления записи")
    deprecated: bool = Field(..., description="Признак устаревшей записи")
    deleted: bool = Field(..., description="Признак удалённой записи")

    # TODO: Согласовать добавление поля deprecated_message?
    # deprecated_message: Optional[str]

    class Config:
        title = "Базовая модель"
        description = "Определение базовой модели описывающей сущность из метамодели"


class LinkConnection(BaseNamedEntity):
    usage: ConnectionUsage = Field(..., description="Назначение соединения")
    properties: Mapping[str, Any] = Field(
        {}, description="Дополнительные атрибуты подключения"
    )

    class Config:
        title = "Связь с подключением"
        description = "Модель, описывающая связь с подключением"


class Source(BaseEntity):
    connections: Sequence[LinkConnection] = Field(
        [], description="Информация о связях с подключениями"
    )

    class Config:
        title = "Источник"
        description = "Модель, описывающая источник"


class TableLink(BaseNamedEntity):
    source: Source = Field(..., description="Источник")
    schema: Optional[str] = Field(None, description="Наименование схемы")
    filter: Optional[str] = Field(
        None, description="Фильтр, если ресурс покрывает только часть набора данных"
    )

    class Config:
        title = "Связь с таблицей"
        description = "Модель, описывающая связь с таблицей"


class ResourceLink(BaseNamedEntity):
    type: LinkType = Field(..., description="Тип связи ресурсов")

    class Config:
        title = "Связь с ресурсом"
        description = "Модель, описывающая связь между ресурсами"


class Resource(BaseEntity):
    type: ResourceType = Field(..., description="Тип ресурса")
    tags: Set[str] = Field([], description="Теги ресурса")
    properties: Mapping[str, Any] = Field(
        {}, description="Дополнительные атрибуты ресурса"
    )

    tables: Sequence[TableLink] = Field(
        None, description="Информация о связях с таблицами"
    )
    linked_resources: Sequence[ResourceLink] = Field(
        None, description="Информация о связях с ресурсами"
    )

    class Config:
        title = "Ресурс"
        description = "Модель, описывающая ресурс"


class Attribute(BaseModel):
    type: str = Field(..., description="Тип данных")
    nullable: bool = Field(True, description="Может принимать NULL значение")

    class Config:
        title = "Атрибут"
        description = "Модель, описывающая столбец таблицы"


class Table(BaseEntity):
    type: TableType = Field(..., description="Тип таблицы")
    schema: Optional[str] = Field(None, description="Наименование схемы")
    source: Source = Field(..., description="Источник")
    layer: Optional[str] = Field(None, description="Наименование слоя")

    type_name: Optional[str] = Field(
        None, description="Наименование класса объекта ЕМД"
    )
    attributes: Mapping[str, Attribute] = Field({}, description="Атрибуты")
    primary_key: Sequence[str] = Field([], description="Атрибуты первичного ключа")

    distribution: Distribution = Field(
        ..., description="Наименование типа распределения"
    )
    distribution_key: Sequence[str] = Field(
        [], description="Атрибуты ключа распределения"
    )

    options: Mapping[str, Any] = Field(
        {},
        description="Дополнительные настройки при создании таблицы + location, format PXF",
    )

    class Config:
        title = "Таблица"
        description = "Модель, описывающая метаданные таблицы"


class ConnectionValue(BaseModel):
    properties: Mapping[str, Any] = Field(
        {}, description="Основные атрибуты подключения"
    )

    class Config:
        title = "Основные атрибуты подключения"
        description = "Модель, описывающая основные атрибуты подключения"


class Connection(BaseEntity):
    type: ConnectionType = Field(..., description="Имп подключения")
    value: ConnectionValue = Field(..., description="Основные атрибуты подключения")
    properties: Mapping[str, Any] = Field(
        {}, description="Дополнительные атрибуты подключения"
    )

    class Config:
        title = "Подключение"
        description = "Модель, описывающая подключение"


class DatabaseConnection(ConnectionValue):
    class Config:
        title = "Подключение к базе данных"
        description = "Модель, описывающая основные атрибуты подключения к базе данных"


class FileConnection(ConnectionValue):
    # TODO: пока нигде не используется

    class Config:
        title = "Файловое подключение"
        description = "Модель, описывающая основные атрибуты файлового подключения"


class ConnectionODBC(DatabaseConnection):
    dsn: str

    class Config:
        title = "ODBC подключение"
        description = "Модель, описывающая основные атрибуты ODBC подключения"


class ConnectionPXF(DatabaseConnection):
    class Config:
        title = "PXF подключение"
        description = "Модель, описывающая основные атрибуты PXF подключения"


class KafkaConnection(ConnectionValue):
    class Config:
        title = "Подключение к Kafka"
        description = "Модель, описывающая основные атрибуты подключения к Kafka"
