from calendar import timegm
from dataclasses import dataclass
from datetime import datetime
from typing import Callable, Collection, FrozenSet, Iterable, Optional

from fastapi import Depends, HTTPException, Request, status
from fastapi.security import OAuth2<PERSON>assword<PERSON>earer
from jose import JWTError, jwt
from pydantic import BaseModel


class AuthError(Exception):
    pass


class TokenData(BaseModel):
    """OAuth2 access token data"""

    username: str
    roles: FrozenSet[str]
    expired_at: Optional[int]


@dataclass
class Authorization:
    oauth_token_url: str
    rs256_public_key: str
    public_roles: Collection[str]

    def with_roles(self, *roles: str) -> Callable[[Request, str], TokenData]:
        def auth_dependency(
            request: Request,
            credentials: str = Depends(
                OAuth2PasswordBearer(tokenUrl=self.oauth_token_url),
            ),
        ) -> TokenData:
            try:
                token: TokenData = decode_jwt(
                    payload=credentials,
                    rs256_public_key=self.rs256_public_key,
                )
                validate(token, required_roles=roles, public_roles=self.public_roles)
                return token
            except AuthError as err:
                setattr(request.state, "auth_err_msg", str(err))
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail=str(err),
                ) from err

        return auth_dependency


def is_token_expired(token: TokenData) -> bool:
    if token.expired_at is None:
        return False

    now = timegm(datetime.utcnow().utctimetuple())

    return token.expired_at < now


def decode_jwt(
    payload: str,
    rs256_public_key: str,
) -> Optional[TokenData]:
    """Декодирование токена доступа

    :param payload:             Содержимое токена
    :param rs256_public_key:    Публичный ключ для декодирования токена
    """
    try:
        token = jwt.decode(
            token=payload,  # type: ignore
            key=rs256_public_key,
            algorithms=["RS256"],
            options={"verify_aud": False, "verify_exp": False},
        )

        username = (
            token.get("name")
            or token.get("preferred_username")
            or token.get("clientId")
        )
        roles = frozenset(token.get("realm_access", {}).get("roles") or ())

        return TokenData(
            username=username,
            roles=roles,
            expired_at=token.get("exp"),
        )

    except (ValueError, JWTError):
        raise AuthError("Invalid token")


def validate(
    token: TokenData,
    required_roles: Optional[Iterable[str]] = None,
    public_roles: Optional[Iterable[str]] = None,
) -> None:
    required_roles = frozenset(required_roles or ())

    if not required_roles:
        return

    public_roles = frozenset(public_roles or ())

    # Завершаем, если хотя бы одна из требуемых ролей является публичной
    if not required_roles.isdisjoint(public_roles):
        return

    if is_token_expired(token):
        raise AuthError("Token has expired")

    if not (required_roles.issubset(token.roles)):
        raise AuthError("Not enough permissions")
