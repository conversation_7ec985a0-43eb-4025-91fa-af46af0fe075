from abc import abstractmethod
from functools import wraps
from typing import Iterator

from metaloader_rest_api.flow_processor.flow_processor import FlowProcessorUnit
from metaloader_rest_api.flow_resource.flow_resource_model import FlowResource
from metaloader_rest_api.yaml.yaml_loader import Yaml


class FlowResourceProcessorUnit(FlowProcessorUnit[Iterator[FlowResource]]):
    @abstractmethod
    def process(
        self,
        flow_id: int,
        flow_name: str,
        flow: Yaml,
    ) -> Iterator[FlowResource]: ...


def bind_flow_context(method):
    """Декоратор для метода `FlowResourceProcessorUnit.process`, прозрачно добавляющий инфо о текущем
    потоке в логер
    """

    @wraps(method)
    def iter_wrapper(self, flow_id: int, flow_name: str, *args, **kwargs):
        original_logger = self._log
        try:
            self._log = original_logger.bind(flow_id=flow_id, flow_name=flow_name)
            yield from method(self, flow_id, flow_name, *args, **kwargs)
        finally:
            self._log = original_logger

    return iter_wrapper
