"""
Требования к формату метрик от команды CEH: http://gitlab.dtpl.corp.dev.vtb/data-platform/goflu/-/issues/7
"""

from uuid import UUID

from celery import Celery
from prometheus_client import Gauge
from sqlalchemy import func
from sqlalchemy.orm import Session

from metaloader_rest_api.logic import check_celery_health
from metaloader_rest_api.models import (
    NON_RESOLVED_STATUSES,
    BaseTransaction,
    TransactionEntity,
)
from metaloader_rest_api.resource_repository import count_staging_rows

_PREFIX = _APP_NAME = "ceh_metamodel"

non_resolved_transactions = Gauge(
    f"{_PREFIX}__non_resolved_transactions",
    "Number of transactions in non-resolved statuses (opened, in_progress)",
    ["srv__appname", "tx_entity"],
)
transaction_resource_actions = Gauge(
    f"{_PREFIX}__transaction_resource_actions",
    "Number of resource actions in the transaction",
    ["tx_uid", "endpoint", "srv__appname"],
)
heartbit = Gauge(
    f"{_PREFIX}__heartbit",
    "Service health status (1=healthy, 0=unhealthy)",
    ["srv__appname"],
)


def set_non_resolved_transactions(session: Session) -> None:
    counts = (
        session.query(
            BaseTransaction.entity_type, func.count(BaseTransaction.id).label("count")
        )
        .filter(BaseTransaction.status.in_(NON_RESOLVED_STATUSES))
        .group_by(BaseTransaction.entity_type)
        .all()
    )
    for entity_type, count in counts:
        non_resolved_transactions.labels(
            srv__appname=_APP_NAME,
            tx_entity=TransactionEntity(entity_type).name.lower(),
        ).set(count)


def set_transaction_resource_actions(
    session: Session,
    tx_uid: UUID,
    endpoint_name: str,
) -> None:
    count = count_staging_rows(session, tx_uid)
    transaction_resource_actions.labels(
        tx_uid=str(tx_uid), endpoint=endpoint_name, srv__appname=_APP_NAME
    ).set(count)


def set_heartbit(celery_client: Celery) -> None:
    health_flag = int(check_celery_health(celery_client))
    heartbit.labels(srv__appname=_APP_NAME).set(health_flag)
