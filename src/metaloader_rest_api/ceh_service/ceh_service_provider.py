from typing import Optional, <PERSON><PERSON>

from sqlalchemy.orm import Session
from structlog.stdlib import <PERSON><PERSON><PERSON><PERSON><PERSON>, get_logger

from metaloader_rest_api.common_model import LAST_DATE
from metaloader_rest_api.db.db_provider import db_provide

_DEFAULT_LOGGER = get_logger(__name__)


def provide_ceh_service(
    session: Session,
    name: str,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> int:
    return db_provide(
        session=session,
        query="""
            SELECT service_rk
              FROM metamodel.bridge_service
             WHERE service_cd = :service_cd
               AND effective_to_dttm = :effective_to_dttm
               AND deleted_flg IS FALSE
        """,
        params={
            "service_cd": name,
            "effective_to_dttm": LAST_DATE,
        },
        log=log,
    )[0]


def provide_ceh_service_urls(
    session: Session,
    id: int,
) -> Tuple[Optional[str], Optional[str]]:
    return db_provide(
        session=session,
        query="""
            SELECT service_url
                 , service_alt_url
              FROM metamodel.bridge_service
             WHERE service_rk = :service_rk
               AND effective_to_dttm = :effective_to_dttm
               AND deleted_flg IS FALSE
        """,
        params={
            "service_rk": id,
            "effective_to_dttm": LAST_DATE,
        },
    )
