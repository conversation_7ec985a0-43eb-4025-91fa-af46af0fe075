from enum import IntEnum
from typing import Optional

from typing_extensions import TypedDict


class CehServiceType(IntEnum):
    AIRFLOW = 1
    ADGP = 2


class CehService(TypedDict):
    service_cd: str
    service_type_cd: str
    service_name: str
    service_desc: Optional[str]
    service_host_name: str
    service_url: str
    service_alt_url: Optional[str]
    environment_cd: str
    code_delivery_cd: str
    ris_src_id: str
    ris_src_code: str
