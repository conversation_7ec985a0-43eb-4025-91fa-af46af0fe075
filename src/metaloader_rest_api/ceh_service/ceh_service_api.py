from datetime import datetime
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, RootModel
from sqlalchemy.orm import Session
from structlog.stdlib import Bo<PERSON><PERSON>og<PERSON>, get_logger

from metaloader_rest_api.ceh_service import ceh_serivce_int

_DEFAULT_LOGGER = get_logger(__name__)


class LoadServicesParam(BaseModel):
    service_cd: str
    service_type_cd: str
    service_name: str
    service_desc: Optional[str] = None
    service_host_name: str
    service_url: str
    service_alt_url: Optional[str] = None
    environment_cd: str
    code_delivery_cd: str
    ris_src_id: str
    ris_src_code: str


class LoadServicesParams(RootModel):
    root: List[LoadServicesParam]


def load_services(
    session: Session,
    load_id: UUID,
    effective_date: datetime,
    params: LoadServicesParams,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    ceh_serivce_int.load_service(
        session=session,
        load_id=load_id,
        effective_date=effective_date,
        services=iter(params.model_dump()),
        log=log,
    )
