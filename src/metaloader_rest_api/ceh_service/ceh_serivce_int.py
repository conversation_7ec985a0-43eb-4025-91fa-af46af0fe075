from datetime import datetime
from typing import Iterator
from uuid import UUID

from sqlalchemy.orm import Session
from structlog.stdlib import Bo<PERSON><PERSON><PERSON><PERSON>, get_logger

from metaloader_rest_api.ceh_service.ceh_service_model import CehService
from metaloader_rest_api.ceh_service.ceh_service_repository import (
    StageServiceRepository,
)
from metaloader_rest_api.common_repository import SessionResource, get_table_id

_DEFAULT_LOGGER = get_logger(__name__)


def load_service(
    session: Session,
    load_id: UUID,
    effective_date: datetime,
    services: Iterator[CehService],
    log: BoundLogger = _DEFAULT_LOGGER,
):
    log = log.bind(action="load_services")
    log.info("begin")

    table_id = get_table_id(load_id)

    repository = StageServiceRepository(
        session=session,
        effective_date=effective_date,
        table_id=table_id,
        log=log,
    )

    session_resource = SessionResource(session)

    with repository, session_resource:
        repository.load_all(services)

    log.info("end")
