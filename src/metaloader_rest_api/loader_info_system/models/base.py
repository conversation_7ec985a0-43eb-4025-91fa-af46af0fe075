from typing import Any, Callable, Generic, Mapping, Optional, Sequence, TypeVar

from sqlalchemy import TextClause, text
from sqlalchemy.orm import Session
from structlog.stdlib import get_logger

Record = TypeVar("Record", bound=Mapping[str, Any])
TableStatement = Callable[[str], str]

logger = get_logger(__name__)


class SystemInfoLoadRepository(Generic[Record]):
    def __init__(
        self,
        session: Session,
        table: str,
        load_statement: TableStatement,
        update_statement: Optional[TableStatement] = None,
    ):
        self._log = logger.bind(
            action="load_table",
            table=table,
        )
        self._session = session
        self._table = table
        self._load_statement = load_statement(table)
        self._update_statement = update_statement(table) if update_statement else None

    def _prepare_load_statement(self) -> TextClause:
        self._log.info("prepare_load_statement")
        return text(self._load_statement)

    def _prepare_update_statement(self) -> TextClause:
        self._log.info("prepare_update_statement")
        return text(self._update_statement)

    def __enter__(self):
        self._log.info("begin")
        self._prepared_load_statement = self._prepare_load_statement()
        self._prepared_update_statement = self._prepare_update_statement()
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        self._log.info(
            "end",
            status="fail" if exc_type else "success",
        )

    @property
    def table(self) -> str:
        return self._table

    def load(self, records: Sequence[Record]) -> None:
        log = self._log.bind(stage="load")

        log.info("begin_load")
        self._session.execute(
            statement=self._prepared_load_statement,
            params=records,
        )
        log.info("end_load")

    def update(self, records: Sequence[Record]) -> None:
        log = self._log.bind(stage="update")

        log.info("begin_update")
        self._session.execute(
            statement=self._prepared_update_statement,
            params=records,
        )
        log.info("end_update")


class SubSystemLoadBatchRepository(SystemInfoLoadRepository[Record]):
    def __enter__(self):
        self._log.info("begin")
        self._delete_from_table()
        self._prepared_load_statement = self._prepare_load_statement()
        return self

    def _delete_from_table(self) -> None:
        self._log.info("delete_from_table")
        self._session.execute(text(f"DELETE FROM {self._table}"))
