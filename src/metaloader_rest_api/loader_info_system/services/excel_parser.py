import io

import pandas as pd
from openpyxl.utils.exceptions import InvalidFileException
from structlog.stdlib import get_logger

logger = get_logger(__name__)


def parse_excel(file: bytes) -> pd.DataFrame:
    """Parses an Excel file and returns its content as a pandas DataFrame."""
    df = None
    try:
        excel_file = io.BytesIO(file)
        df = pd.read_excel(excel_file, sheet_name=0, keep_default_na=False)
        df = df.applymap(lambda x: x if x != "" else None)

    except InvalidFileException as e:
        error_msg = "The file is not a valid Excel (XLSX)."
        logger.error(error_msg, exc_info=e)

    except ValueError as e:
        error_msg = "Error reading Excel file. The file may be corrupted."
        logger.error(error_msg, exc_info=e)

    except Exception as e:
        error_msg = "An unexpected error occurred while processing the Excel file."
        logger.error(error_msg, exc_info=e)

    if df is None:
        raise ValueError("Failed to process Excel file.")

    return df


def get_data_bridge_source(file: bytes) -> list[dict]:
    """Parses an Excel and returns stg_bridge_source table data as a list of dicts."""
    df_default = parse_excel(file)
    return df_default.to_dict(orient="records")


def get_data_for_code_delivery(file: bytes) -> list[dict]:
    """Parses an Excel file and returns stg_code_delivery table data as a list of dicts."""
    df = parse_excel(file)
    return df.to_dict(orient="records")
