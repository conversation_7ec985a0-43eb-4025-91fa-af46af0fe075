from datetime import datetime
from uuid import UUID

from sqlalchemy.orm import Session
from structlog.stdlib import get_logger

from metaloader_rest_api.loader_info_system.models.metamodel import (
    BridgeSourceRepository,
)
from metaloader_rest_api.loader_info_system.models.stg import (
    StageBridgeSourceRepository,
)
from metaloader_rest_api.loader_info_system.services.db_worker import (
    execute_query_text,
    load_stage_data,
    load_system_data,
    prepare_query_bridge_source,
    update_system_data,
)
from metaloader_rest_api.loader_info_system.services.excel_parser import (
    get_data_bridge_source,
)
from metaloader_rest_api.logic import close_tx, create_tx, process_tx

logger = get_logger(__name__)


def process_xlsx_files(
    session: Session,
    load_id: UUID,
    file_name: str,
    loaded_file: bytes,
    effective_from_dttm: datetime,
) -> str:
    """Processes Excel files and inserts/updates data into appropriate tables."""

    def process_bridge_source(with_parent: bool) -> None:
        query = prepare_query_bridge_source(
            load_id=load_id.hex, without_parent=not with_parent
        )
        data = execute_query_text(
            session=session, query=query, effective_from_dttm=effective_from_dttm
        )

        if data:
            update_system_data(session, data, BridgeSourceRepository)
            load_system_data(session, data, BridgeSourceRepository)

    messages = []
    logger.info(f"Processing file: {file_name}")

    if "stg_bridge_source" in file_name:
        logger.info("Processing stg_bridge_source file.")
        data_stg_bridge_source = get_data_bridge_source(loaded_file)
        load_stage_data(
            session, load_id, data_stg_bridge_source, StageBridgeSourceRepository
        )

        # at first - load bridge_source without parent_source_rk
        process_bridge_source(with_parent=False)

        # at second - load bridge_source with parent_source_rk
        process_bridge_source(with_parent=True)

        messages.append(f"stg_bridge_source_{load_id.hex} and bridge_source loaded")

    return " , ".join(messages)


def create_tx_for_load_system_info(
    db_session: Session, file_name: str, loaded_file: bytes
) -> tuple:
    """Creates a transaction for loading system info."""
    log = logger.bind(action="load system info")

    load_id, _ = create_tx(
        db_session=db_session,
        log=log,
        task="create_tx_for_load_system_info",
    )

    return (
        load_id,
        begin_load_system_info,
        [db_session, load_id, file_name, loaded_file],
    )


def begin_load_system_info(
    db_session: Session,
    load_id: UUID,
    file_name: str,
    loaded_file: bytes,
    effective_from_dttm: datetime,
) -> None:
    """Begins loading system info, handles transaction lifecycle."""
    logger.info("begin load system info")
    rollback = False
    message = "Error on load system info"
    try:
        process_tx(db_session, logger, load_id)
        result = process_xlsx_files(
            session=db_session,
            load_id=load_id,
            file_name=file_name,
            loaded_file=loaded_file,
            effective_from_dttm=effective_from_dttm,
        )
        message = str(result)
    except Exception as exception:
        logger.exception("fail load system info")
        rollback = True
        message = str(exception)
    finally:
        try:
            close_tx(db_session, logger, load_id, rollback, message)
        except Exception:
            logger.exception("fail close tx with load system info", stage="close")
        finally:
            logger.info("end load system info")
