from contextlib import contextmanager
from itertools import chain
from typing import Any, Iterator, Mapping, Optional, Tuple, Union

from sqlalchemy import text
from sqlalchemy.orm import Session
from structlog.stdlib import BoundLogger, get_logger

_DEFAULT_LOGGER = get_logger(__name__)


@contextmanager
def db_provider(
    session: Session,
    query: str,
    params: Mapping[str, Any],
    fetch_size: Optional[int] = None,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> Iterator[Iterator[Tuple]]:
    log = log.bind(actor="db_provider")

    with session.execute(
        statement=text(query),
        params=params,
        execution_options={
            "stream_results": True,
            "yield_per": fetch_size,
        },
    ) as result:
        log.info("fetch", fetch_size=fetch_size)

        yield (row._tuple() for row in chain.from_iterable(result.partitions()))

    log.info("close")


def db_provide(
    session: Session,
    query: str,
    params: Mapping[str, Any],
    quiet: bool = False,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> Union[Tuple, Optional[Tuple]]:
    result = session.execute(
        statement=text(query),
        params=params,
    )

    return result.one_or_none() if quiet else result.one()
