from datetime import datetime
from typing import Sequence
from uuid import UUID

import structlog
from celery import signals

from metaloader_rest_api import resource_providers_resource_loader
from metaloader_rest_api.ceh_module import ceh_module_api
from metaloader_rest_api.ceh_service import ceh_service_api
from metaloader_rest_api.celery_tasks import logic
from metaloader_rest_api.celery_tasks.app_factory import get_celery_app
from metaloader_rest_api.celery_tasks.database_connectivity import (
    AbstractBoundSessionTask,
    create_bound_session_task_class,
    create_sqlalchemy_engine,
)
from metaloader_rest_api.celery_tasks.logging import get_logger, setup_logging
from metaloader_rest_api.celery_tasks.settings import Settings
from metaloader_rest_api.data_model import data_model_api
from metaloader_rest_api.flow import flow_loader_api
from metaloader_rest_api.flow_processor import flow_processor_api
from metaloader_rest_api.helpers import read_settings_from_env
from metaloader_rest_api.logic import process_tx_manager
from metaloader_rest_api.master_flow import (
    master_flow_item_loader_api,
    master_flow_loader_api,
)

logger = get_logger(__name__)


settings = read_settings_from_env(Settings)


@signals.setup_logging.connect
def on_setup_logging(**kwargs):  # noqa
    # Методика настройки страктлога в селери взята отсюда:
    # https://github.com/hynek/structlog/issues/287
    setup_logging(log_format=settings.log_format, log_level=settings.log_level)


@signals.task_prerun.connect
def on_task_prerun(sender, task_id, task, args, kwargs, **_):  # noqa
    structlog.contextvars.bind_contextvars(task_id=task_id, task_name=task.name)


@signals.task_postrun.connect
def on_task_postrun(sender, task_id, task, args, kwargs, **_):  # noqa
    structlog.contextvars.clear_contextvars()


app = get_celery_app(settings)
BoundSessionTask = create_bound_session_task_class(
    engine=create_sqlalchemy_engine(database_url=settings.database_url.unicode_string())
)


@app.task(
    bind=True,
    base=BoundSessionTask,
    name="run_resources_integration_etl",
)
def run_resources_integration_etl(
    self: AbstractBoundSessionTask,
    tx_uid: str,
    **kwargs,  # noqa
) -> None:
    logic.run_resources_integration_etl(
        session=self.session,
        tx_uid=UUID(tx_uid),
        retry_task=lambda exc: self.retry(exc=exc),
        retries_limit_reached=self.retries_limit_reached,
    )


@app.task(
    bind=True,
    base=BoundSessionTask,
    name="load_resources_from_providers",
)
def load_resources_from_providers(
    self: AbstractBoundSessionTask,
    load_id: UUID,
    effective_date: datetime,
    params: resource_providers_resource_loader.LoadResourcesFromProvidersParams,
    **kwargs,  # noqa
) -> None:
    log = logger.bind(load_id=load_id, effective_date=effective_date)
    log.info("load_resources_from_providers", params=params)
    with process_tx_manager(self.session, log, load_id):
        resource_providers_resource_loader.load_resources_from_providers(
            session=self.session,
            load_id=load_id,
            effective_date=effective_date,
            params=params,
            log=log,
        )


@app.task(
    bind=True,
    base=BoundSessionTask,
    name="check_transactions_statuses",
)
def check_transactions_statuses(
    self: AbstractBoundSessionTask,
    load_id: str,
    transactions_ids: Sequence[str],
    **kwargs,  # noqa
) -> None:
    logic.check_transactions_statuses(
        session=self.session,
        load_id=UUID(load_id),
        transactions_ids=tuple(UUID(uid) for uid in transactions_ids),
    )


@app.task(
    bind=True,
    base=BoundSessionTask,
    name="load_flows",
)
def load_flows(
    self: AbstractBoundSessionTask,
    load_id: UUID,
    effective_date: datetime,
    params: flow_loader_api.LoadFlowsParams,
    **kwargs,
) -> None:
    log = logger.bind(load_id=load_id, effective_date=effective_date)
    log.info("load_flows", params=params)
    with process_tx_manager(self.session, log, load_id):
        flow_loader_api.load_flows(
            self.session,
            load_id,
            effective_date,
            params,
            log,
        )


@app.task(
    bind=True,
    base=BoundSessionTask,
    name="load_all_flows",
)
def load_all_flows(
    self: AbstractBoundSessionTask,
    load_id: UUID,
    effective_date: datetime,
    params: flow_loader_api.LoadFlowsParams,
    **kwargs,
) -> None:
    log = logger.bind(load_id=load_id, effective_date=effective_date)
    log.info("load_all_flows", params=params)
    with process_tx_manager(self.session, log, load_id):
        flow_loader_api.load_flows(
            self.session,
            load_id,
            effective_date,
            params,
            log,
        )
        params = params.model_dump(exclude_defaults=True)
        master_flow_loader_api.load_master_flows(
            self.session,
            load_id,
            effective_date,
            master_flow_loader_api.LoadMasterFlowsParams(**params),
            log,
        )
        master_flow_item_loader_api.load_master_flow_items(
            self.session,
            load_id,
            effective_date,
            master_flow_item_loader_api.LoadMasterFlowItemsParams(
                incremental=False, **params
            ),
            log,
        )
        flow_processor_api.process_flows(
            self.session,
            load_id,
            effective_date,
            flow_processor_api.ProcessFlowsParams(**params),
            log,
        )


@app.task(
    bind=True,
    base=BoundSessionTask,
    name="load_master_flows",
)
def load_master_flows(
    self: AbstractBoundSessionTask,
    load_id: UUID,
    effective_date: datetime,
    params: master_flow_loader_api.LoadMasterFlowsParams,
    **kwargs,
) -> None:
    log = logger.bind(load_id=load_id, effective_date=effective_date)
    log.info("load_master_flows", params=params)
    with process_tx_manager(self.session, log, load_id):
        master_flow_loader_api.load_master_flows(
            self.session,
            load_id,
            effective_date,
            params,
            log,
        )
        params = params.model_dump(exclude_defaults=True)
        master_flow_item_loader_api.load_master_flow_items(
            self.session,
            load_id,
            effective_date,
            master_flow_item_loader_api.LoadMasterFlowItemsParams(**params),
            log,
        )


@app.task(
    bind=True,
    base=BoundSessionTask,
    name="load_all_master_flows",
)
def load_all_master_flows(
    self: AbstractBoundSessionTask,
    load_id: UUID,
    effective_date: datetime,
    params: master_flow_loader_api.LoadMasterFlowsParams,
    **kwargs,
) -> None:
    log = logger.bind(load_id=load_id, effective_date=effective_date)
    log.info("load_all_master_flows", params=params)
    with process_tx_manager(self.session, log, load_id):
        master_flow_loader_api.load_master_flows(
            self.session,
            load_id,
            effective_date,
            params,
            log,
        )


@app.task(
    bind=True,
    base=BoundSessionTask,
    name="load_master_flow_items",
)
def load_master_flow_items(
    self: AbstractBoundSessionTask,
    load_id: UUID,
    effective_date: datetime,
    params: master_flow_item_loader_api.LoadMasterFlowItemsParams,
    **kwargs,
) -> None:
    log = logger.bind(load_id=load_id, effective_date=effective_date)
    log.info("load_master_flow_items", params=params)
    with process_tx_manager(self.session, log, load_id):
        master_flow_item_loader_api.load_master_flow_items(
            self.session,
            load_id,
            effective_date,
            params,
            log,
        )


@app.task(
    bind=True,
    base=BoundSessionTask,
    name="process_flows",
)
def process_flows(
    self: AbstractBoundSessionTask,
    load_id: UUID,
    effective_date: datetime,
    params: flow_processor_api.ProcessFlowsParams,
    **kwargs,
) -> None:
    log = logger.bind(load_id=load_id, effective_date=effective_date)
    log.info("process_flows", params=params)
    with process_tx_manager(self.session, log, load_id):
        flow_processor_api.process_flows(
            self.session,
            load_id,
            effective_date,
            params,
            log,
        )


@app.task(
    bind=True,
    base=BoundSessionTask,
    name="load_data_model",
)
def load_data_model(
    self: AbstractBoundSessionTask,
    load_id: UUID,
    effective_date: datetime,
    params: data_model_api.LoadDataModelParams,
    **kwargs,
) -> None:
    log = logger.bind(load_id=load_id, effective_date=effective_date)
    log.info("load_data_model", params=params)
    with process_tx_manager(self.session, log, load_id):
        data_model_api.load_data_model(
            self.session,
            load_id,
            effective_date,
            params,
            log,
        )


@app.task(
    bind=True,
    base=BoundSessionTask,
    name="load_logical_data_model",
)
def load_logical_data_model(
    self: AbstractBoundSessionTask,
    load_id: UUID,
    effective_date: datetime,
    params: data_model_api.LoadLogicalDataModelParams,
    **kwargs,
) -> None:
    log = logger.bind(load_id=load_id, effective_date=effective_date)
    log.info("load_logical_data_model", params=params)
    with process_tx_manager(self.session, log, load_id):
        data_model_api.load_logical_data_model(
            self.session,
            load_id,
            effective_date,
            params,
            log,
        )


@app.task(
    bind=True,
    base=BoundSessionTask,
    name="load_services",
)
def load_services(
    self: AbstractBoundSessionTask,
    load_id: UUID,
    effective_date: datetime,
    params: ceh_service_api.LoadServicesParams,
    **kwargs,
) -> None:
    log = logger.bind(load_id=load_id, effective_date=effective_date)
    log.info("load_services", params=params)
    with process_tx_manager(self.session, log, load_id):
        ceh_service_api.load_services(
            session=self.session,
            load_id=load_id,
            effective_date=effective_date,
            params=params,
            log=log,
        )


@app.task(
    bind=True,
    base=BoundSessionTask,
    name="load_modules",
)
def load_modules(
    self: AbstractBoundSessionTask,
    load_id: UUID,
    effective_date: datetime,
    params: ceh_module_api.LoadModulesParams,
    **kwargs,
) -> None:
    log = logger.bind(load_id=load_id, effective_date=effective_date)
    log.info("load_modules", params=params)
    with process_tx_manager(self.session, log, load_id):
        ceh_module_api.load_modules(
            session=self.session,
            load_id=load_id,
            effective_date=effective_date,
            params=params,
            log=log,
        )
