import logging.config

import structlog
from celery.utils.log import get_task_logger

from metaloader_rest_api.logging import TOP_LEVEL_PACKAGE_NAME, LogFormat, LogLevel


def setup_logging(log_format: LogFormat, log_level: LogLevel = "INFO") -> None:
    """
    Настраивает structlog специально под условия работы логирования в Celery

    Селери использует логирование из стандартной библиотеки и сама кастомизирует его настройки,
    поэтому логи приходится выводить через машинерию стдлибы. Чтобы получить пользу страктлога в
    такой ситуации, приходится конфигурировать в двух местах -- и его и логинг стдлибы.
    """
    shared_processors = [
        structlog.contextvars.merge_contextvars,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.processors.CallsiteParameterAdder(
            {
                structlog.processors.CallsiteParameter.PROCESS,
                structlog.processors.CallsiteParameter.PROCESS_NAME,
                structlog.processors.CallsiteParameter.PATHNAME,
                structlog.processors.CallsiteParameter.FUNC_NAME,
            }
        ),
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.stdlib.ExtraAdder(),
        structlog.processors.TimeStamper(fmt="iso"),
    ]
    if log_format != "pretty":
        shared_processors.append(structlog.processors.format_exc_info)

    log_renderer: structlog.types.Processor = {
        "logfmt": structlog.processors.LogfmtRenderer(),
        "json": structlog.processors.JSONRenderer(),
        "pretty": structlog.dev.ConsoleRenderer(),
    }[log_format]

    stdlib_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "structlog": {
                "()": structlog.stdlib.ProcessorFormatter,
                "foreign_pre_chain": shared_processors,
                "processors": [
                    structlog.stdlib.ProcessorFormatter.remove_processors_meta,
                    log_renderer,
                ],
            },
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "formatter": "structlog",
            },
        },
        "loggers": {
            TOP_LEVEL_PACKAGE_NAME: {
                "level": log_level,
            },
            "celery": {
                "level": log_level,
            },
        },
        "root": {
            "level": "WARNING",
            "handlers": ["console"],
        },
    }
    logging.config.dictConfig(stdlib_config)
    structlog.configure(
        processors=[
            *shared_processors,
            structlog.stdlib.ProcessorFormatter.wrap_for_formatter,
        ],
        wrapper_class=structlog.stdlib.BoundLogger,  # type: ignore
        logger_factory=structlog.stdlib.LoggerFactory(),
        cache_logger_on_first_use=True,
    )


def get_logger(name: str) -> structlog.stdlib.BoundLogger:
    return structlog.wrap_logger(get_task_logger(name))
