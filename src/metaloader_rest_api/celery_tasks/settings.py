from pydantic import BaseModel, Field, PostgresDsn, RedisDsn

from metaloader_rest_api.logging import (
    LogFormat,
    LogLevel,
)


class Settings(BaseModel):
    database_url: PostgresDsn = Field(alias="DATABASE_URL")
    celery_broker_url: RedisDsn = Field(alias="CELERY_BROKER_URL")
    log_format: LogFormat = Field(alias="LOG_FORMAT", default="json")
    log_level: LogLevel = Field(alias="LOG_LEVEL", default="INFO")
    airflow_username: str = Field(alias="AF_USERNAME")
    airflow_password: str = Field(alias="AF_PASSWORD")

    class Config:
        populate_by_name = True
