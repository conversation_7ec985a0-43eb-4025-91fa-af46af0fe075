from contextlib import suppress
from typing import Callable, Mapping, Optional, Tuple
from uuid import UUID

from celery.exceptions import Retry
from sqlalchemy import text
from sqlalchemy.exc import NoResultFound, OperationalError, SQLAlchemyError
from sqlalchemy.orm import Session

from metaloader_rest_api import models
from metaloader_rest_api.celery_tasks.logging import get_logger
from metaloader_rest_api.deployment import get_deployment
from metaloader_rest_api.logic import close_tx
from metaloader_rest_api.resource_repository import (
    ResourceRepository,
    ResourceStageSequentialRepository,
)
from metaloader_rest_api.schemas import ReleaseMetadata

logger = get_logger(__name__)


def run_resources_integration_etl(
    session: Session,
    tx_uid: UUID,
    retry_task: Callable[[Optional[Exception]], Retry],
    retries_limit_reached: bool = False,
) -> None:
    # TODO: вынести получение транзакции и try-except в отдельную функцию
    try:
        # noinspection PyTypeChecker
        transaction: models.ResourceTransaction = (
            session.query(models.ResourceTransaction)
            .filter(models.ResourceTransaction.id == tx_uid)
            .one()
        )
    except NoResultFound:
        logger.warning("Transaction not found", tx_uid=tx_uid)
        return

    # noinspection PyTypeChecker
    if not _ensure_transaction_is_opened(transaction):
        return

    try:
        transaction.status = models.TransactionStatus.IN_PROGRESS.value
        session.commit()
    except Exception:  # noqa
        logger.exception("DB connection lost, transaction %s left intact", str(tx_uid))
        # Если повезло потерять соединение до того, как поменяли статус, то ничего не делаем,
        # даём возможность пользователю апи ещё раз позвать /commit для этой же транзакции
        return

    resource_repository = ResourceRepository(session)
    # noinspection PyArgumentList
    release = ReleaseMetadata(**transaction.request)

    try:
        _, _, version_id = get_deployment(
            session=session,
            module=release.release_module,
            service=None,
            release=release.release_num,
            effective_date=transaction.created_at,
            log=logger,
        )
        resource_repository.load(
            version_id=version_id,
            effective_date=transaction.created_at,
            stage_table=ResourceStageSequentialRepository.get_staging_table_name(
                load_id=tx_uid,
            ),
        )
        transaction.status = models.TransactionStatus.COMMITTED.value
        session.commit()
    except OperationalError as err:
        # Класс ошибок, которые обычно можно заретраить. Если будем ловить что-то ненужное, нужно
        # будет специфицировать более конкретные ошибки по тексту эксепшена.
        # Точно нужно ретраить ошибки сериализации транзакции и потерю коннекта к БД.
        # https://docs.sqlalchemy.org/en/20/errors.html#operationalerror
        session.rollback()
        logger.warning("Database error during ETL", tx_uid=tx_uid, error=str(err))
        if not retries_limit_reached:
            raise retry_task(err)
        with suppress(SQLAlchemyError):
            transaction.status = models.TransactionStatus.ROLLEDBACK.value
            session.commit()
            return
    except Exception:
        session.rollback()
        logger.exception("ETL failed with unexpected error")
        with suppress(Exception):
            transaction.status = models.TransactionStatus.ROLLEDBACK.value
            session.commit()
        raise


def check_transactions_statuses(
    session: Session,
    load_id: UUID,
    transactions_ids: Tuple[UUID, ...],
) -> None:
    rollback = False
    message = None
    try:
        transactions_statuses = get_non_commited_transactions_statuses(
            db_session=session,
            transactions_ids=transactions_ids,
        )
        if transactions_statuses:
            rollback = True
            transactions_ids = [str(uid) for uid in transactions_ids]
            transactions_statuses = {
                str(uid): status for uid, status in transactions_statuses.items()
            }
            message = (
                f"Non-commited transactions {transactions_statuses} "
                f"out of {transactions_ids}"
            )
    except Exception as exception:
        logger.exception("fail")
        rollback = True
        message = str(exception)
    finally:
        try:
            close_tx(session, logger, load_id, rollback, message)
        except Exception:
            logger.exception("fail", stage="close")
        finally:
            logger.info("end")


def get_non_commited_transactions_statuses(
    db_session: Session,
    transactions_ids: Tuple[UUID, ...],
) -> Mapping[UUID, str]:
    rows = db_session.execute(
        statement=text("""
              SELECT t.service_transaction_uid
                   , s.service_transaction_status_cd
                FROM metamodel.service_transaction_status t
                JOIN dict.dict_service_transaction_status s
                  ON s.service_transaction_status_rk = t.service_transaction_status_rk 
               WHERE t.service_transaction_uid       IN :service_transaction_uids
                 AND t.service_transaction_status_rk != :service_transaction_status_rk
            ORDER BY t.request_dttm
        """),
        params={
            "service_transaction_uids": transactions_ids,
            "service_transaction_status_rk": models.TransactionStatus.COMMITTED,
        },
    ).fetchall()

    return {row[0]: row[1] for row in rows}


def _ensure_transaction_is_opened(transaction: models.BaseTransaction) -> bool:
    """
    NOTE: подразумевается, что селери-задача, зовущая эту функцию исполняется на воркере,
    который выполняет задачи строго одну за одной. Соответственно, если транзакция не
    "opened", то либо другой инстанс этой задачи её уже зарезолвил, либо воркер упал,
    не успев обновить её статус и она зависла в in_progress до таймаута  ¯\_(ツ)_/¯.
    При этом ETL для неё мог как выполнится, так и упасть с ошибкой (но их мы всегда ловим).
    """
    if transaction.status == models.TransactionStatus.OPENED.value:
        return True

    try:
        tx_status = models.TransactionStatus(transaction.status).name.lower()
    except ValueError:
        tx_status = str(transaction.status)

    logger.warning(
        "Attempting to work on transaction that has already been started processing",
        tx_status=tx_status,
    )
    return False
