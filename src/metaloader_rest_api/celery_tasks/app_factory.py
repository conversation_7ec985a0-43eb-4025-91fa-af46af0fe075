from functools import partial
from typing import Any, Callable, Mapping, Protocol, Sequence, Type, TypeVar
from uuid import UUID

from celery import Celery
from kombu.utils.json import register_type
from pydantic import BaseModel, RedisDsn, RootModel

from metaloader_rest_api import resource_providers_resource_loader
from metaloader_rest_api.ceh_module import ceh_module_api
from metaloader_rest_api.ceh_service import ceh_service_api
from metaloader_rest_api.data_model import data_model_api
from metaloader_rest_api.flow import flow_loader_api
from metaloader_rest_api.flow_processor import flow_processor_api
from metaloader_rest_api.master_flow import (
    master_flow_item_loader_api,
    master_flow_loader_api,
)


class CelerySettings(Protocol):
    celery_broker_url: RedisDsn


def get_celery_app(settings: CelerySettings, name: str = "tasks") -> Celery:
    from . import celeryconfig

    app = Celery(main=name, broker=settings.celery_broker_url.unicode_string())
    app.config_from_object(celeryconfig, force=True)

    register_types()

    flow_processor_api.registry_flow_processors()

    return app


def register_types() -> None:
    register_uuid_type()
    register_pydantic_model_types(
        _sort_by_inheritance_depth(PYDANTIC_MODEL_TYPES),
        pydantic_model_decoder,
    )
    register_pydantic_model_types(
        _sort_by_inheritance_depth(PYDANTIC_ROOT_MODEL_TYPES),
        pydantic_root_model_decoder,
    )


def register_uuid_type() -> None:
    register_type(
        UUID,
        marker="UUID",
        encoder=lambda value: str(value),
        decoder=lambda value: UUID(value),
    )


PYDANTIC_MODEL_TYPES = (
    data_model_api.LoadDataModelParams,
    data_model_api.LoadLogicalDataModelParams,
    flow_loader_api.LoadFlowsParams,
    master_flow_loader_api.LoadMasterFlowsParams,
    master_flow_item_loader_api.LoadMasterFlowItemsParams,
    ceh_service_api.LoadServicesParams,
    resource_providers_resource_loader.LoadResourcesFromProvidersParams,
)

PYDANTIC_ROOT_MODEL_TYPES = (
    ceh_service_api.LoadServicesParams,
    ceh_module_api.LoadModulesParams,
)


_T = TypeVar("_T", BaseModel, RootModel)


def _sort_by_inheritance_depth(models: Sequence[Type[BaseModel]]) -> Sequence[Type[BaseModel]]:
    return sorted(
        models,
        key=lambda cls: len(cls.__mro__),
        reverse=True,
    )


def register_pydantic_model_types(
    model_types: Sequence[Type[_T]],
    model_decoder: Callable[[Type[_T], Any], _T],
) -> None:
    for model_type in model_types:
        register_type(
            model_type,
            marker=pydantic_model_marker(model_type),
            encoder=pydantic_model_encoder,
            decoder=partial(model_decoder, model_type),
        )


def pydantic_model_marker(model_type: Type[BaseModel]) -> str:
    return f"{model_type.__module__}.{model_type.__qualname__}"


def pydantic_model_encoder(
    model: BaseModel,
) -> Mapping[str, Any]:
    return model.model_dump(mode="json")


def pydantic_model_decoder(
    model_type: Type[BaseModel],
    data: Mapping[str, Any],
) -> BaseModel:
    return model_type(**data)


def pydantic_root_model_decoder(
    model_type: Type[RootModel],
    data: Any,
) -> BaseModel:
    return model_type(data)
