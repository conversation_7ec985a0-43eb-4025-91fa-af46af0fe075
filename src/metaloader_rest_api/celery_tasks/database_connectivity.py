import json
import os
from abc import ABC, abstractmethod
from functools import partial
from typing import Type

import celery
from sqlalchemy import Engine, create_engine, event
from sqlalchemy.exc import DisconnectionError
from sqlalchemy.orm import Session, sessionmaker
from sqlalchemy.pool import NullPool


class AbstractBoundSessionTask(celery.Task, ABC):
    @property
    @abstractmethod
    def session(self) -> Session:
        pass

    @property
    @abstractmethod
    def retries_limit_reached(self) -> bool:
        pass


def create_bound_session_task_class(engine: Engine) -> Type[AbstractBoundSessionTask]:
    class Task(AbstractBoundSessionTask):
        """
        Селери-задача, автоматически управляющая сессиями SQLAlchemy

        Рецепт взят отсюда: https://celery.school/sqlalchemy-session-celery-tasks
        """

        _make_session = sessionmaker(autocommit=False, autoflush=False, bind=engine)

        def __init__(self):
            self.sessions = {}

        def before_start(self, task_id, args, kwargs):
            self.sessions[task_id] = self._make_session()
            super().before_start(task_id, args, kwargs)

        def after_return(self, status, retval, task_id, args, kwargs, einfo):
            session = self.sessions.pop(task_id)
            session.close()
            super().after_return(status, retval, task_id, args, kwargs, einfo)

        @property
        def session(self):
            return self.sessions[self.request.id]

        @property
        def retries_limit_reached(self):
            return self.request.retries >= self.max_retries

    return Task


def create_sqlalchemy_engine(database_url: str) -> Engine:
    """Кастомное создание подключений к БД для селери-воркера, с учётом потенциальных проблем
    в мультпроцессном сценарии

    Подробности см. в доках алхимии:
    https://docs.sqlalchemy.org/en/20/core/pooling.html#using-connection-pools-with-multiprocessing-or-os-fork
    """
    engine = create_engine(
        url=database_url,
        # NullPool гарантирует, что для каждой селери-таски будет создаваться новое подключение
        poolclass=NullPool,
        json_serializer=get_json_serializer(),
        # Проблемы гонок в веб-сервере при создании селери-задач можно решить таким уровнем изоляции,
        # просто переложив на БД ответственность не допускать, чтобы ETL-и, неожиданные запущенные
        # параллельно, испортили нам данные
        isolation_level="SERIALIZABLE",
    )

    @event.listens_for(engine, "connect")
    def connect(_dbapi_connection, connection_record):
        connection_record.info["pid"] = os.getpid()

    @event.listens_for(engine, "checkout")
    def checkout(_dbapi_connection, connection_record, connection_proxy):
        original_connection_pid = connection_record.info["pid"]
        current_pid = os.getpid()
        if current_pid != original_connection_pid:
            connection_record.dbapi_connection = connection_proxy.dbapi_connection = (
                None
            )
            raise DisconnectionError(
                "Connection record belongs to pid %s, "
                "attempting to check out in pid %s"
                % (original_connection_pid, current_pid)
            )

    return engine


def get_json_serializer():
    return partial(json.dumps, default=str)
