WITH
    staging AS (
        SELECT
            resource_cd
          , data_action_type_rk
          , definition ->> 'resource_desc' AS resource_desc
            -- @fmt:off
          , CASE resource_type
                WHEN 'ceh' THEN coalesce(definition ->> 'is_readonly', 'false')::bool
                WHEN 'uni' THEN coalesce(definition -> 'status' ->> 'is_readonly', 'false')::bool
                ELSE FALSE
            END AS is_readonly_flg
          , CASE resource_type
                WHEN 'ceh' THEN FALSE
                WHEN 'uni' THEN coalesce(definition -> 'status' ->> 'is_maintenance', 'false')::bool
                ELSE FALSE
            END AS is_maintenance_flg
          , array(
                SELECT jsonb_array_elements_text(nullif(definition -> 'tags', 'null'))
            ) AS tag_list
          -- @fmt:on
          , definition AS resource_json
        FROM {stage_table}
    )
  , delta AS (
        SELECT
            src.resource_cd AS src__resource_cd
          , src.resource_desc AS src__resource_desc
          , src.is_readonly_flg AS src__is_readonly_flg
          , src.is_maintenance_flg AS src__is_maintenance_flg
          , src.tag_list AS src__tag_list
          , src.resource_json AS src__resource_json
          , tgt.resource_rk AS tgt__resource_rk
          , tgt.resource_cd AS tgt__resource_cd
          , tgt.resource_desc AS tgt__resource_desc
          , tgt.is_readonly_flg AS tgt__is_readonly_flg
          , tgt.is_maintenance_flg AS tgt__is_maintenance_flg
          , tgt.tag_list AS tgt__tag_list
          , tgt.resource_json AS tgt__resource_json
          , CASE
                WHEN src.data_action_type_rk IN (:item_added, :item_modified) THEN
                    CASE
                        WHEN tgt.resource_cd NOTNULL AND
                             tgt.deleted_flg IS FALSE AND
                             jsonb_hash_extended(src.resource_json, 0) !=
                             jsonb_hash_extended(tgt.resource_json, 0) THEN 'U'
                        WHEN tgt.resource_cd NOTNULL AND tgt.deleted_flg IS TRUE THEN 'U'
                        WHEN tgt.resource_cd IS NULL THEN 'I'
                    END
                WHEN src.data_action_type_rk = :item_deleted AND
                     tgt.resource_cd NOTNULL AND
                     tgt.deleted_flg IS FALSE THEN 'D'
            END AS item_action
        FROM
            staging AS src
            LEFT JOIN {bridge_resource} AS tgt ON
                src.resource_cd = tgt.resource_cd AND
                tgt.effective_to_dttm = :effective_to_dttm
    )
  , updates AS (
    -- @fmt:off
        UPDATE {bridge_resource} AS tgt
        SET effective_to_dttm = :effective_from_dttm
        FROM delta
        WHERE
            delta.item_action IN ('U', 'D')
            AND tgt.resource_rk = delta.tgt__resource_rk
            AND tgt.effective_to_dttm = :effective_to_dttm
        -- @fmt:on
    )
INSERT
INTO {bridge_resource}( resource_rk
                      , resource_cd
                      , resource_desc
                      , is_readonly_flg
                      , is_maintenance_flg
                      , tag_list
                      , resource_json
                      , version_rk
                      , effective_from_dttm
                      , effective_to_dttm
                      , deleted_flg)
SELECT
    nextval('{sequence_metamodel_rk}')
  , src__resource_cd
  , src__resource_desc
  , src__is_readonly_flg
  , src__is_maintenance_flg
  , src__tag_list
  , src__resource_json
  , :version_rk
  , :effective_from_dttm
  , :effective_to_dttm
  , FALSE AS deleted_flg
FROM delta
WHERE
    item_action = 'I'
UNION ALL
SELECT
    tgt__resource_rk
  , tgt__resource_cd
  , tgt__resource_desc
  , tgt__is_readonly_flg
  , tgt__is_maintenance_flg
  , tgt__tag_list
  , tgt__resource_json
  , :version_rk
  , :effective_from_dttm
  , :effective_to_dttm
  , TRUE AS deleted_flg
FROM delta
WHERE
    item_action = 'D'
UNION ALL
SELECT
    tgt__resource_rk
  , tgt__resource_cd
  , src__resource_desc
  , src__is_readonly_flg
  , src__is_maintenance_flg
  , src__tag_list
  , src__resource_json
  , :version_rk
  , :effective_from_dttm
  , :effective_to_dttm
  , FALSE AS deleted_flg
FROM delta
WHERE
    item_action = 'U'
