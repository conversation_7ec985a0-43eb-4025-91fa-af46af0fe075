WITH
    staging AS (
        SELECT
            resource_cd
          , (definition -> 'features' ->> 'source_system') AS source_cd
        FROM {stage_table}
    )
  , staging_with_rk AS (
    -- Поскольку нам присылают джсоны ресурсов целиком, то в этой таблице содержатся
    -- актуальные связи ресурсов с источниками (для присланных resource_rk), по сути снапшот.
    -- Поэтому, значение data_action_type_rk не важно -- можно ориентировать только на сравнение
    -- source_rk в стейдже и линке.
    -- Ожидается, что для data_action_type_rk=deleted поле definition всегда null, поэтому такие
    -- записи в стейдже можно опознать по source_rk=null.
        SELECT
            resource.resource_rk
          , source.source_rk
        FROM
            staging
            INNER JOIN {bridge_resource} AS resource ON
                staging.resource_cd = resource.resource_cd AND
                resource.effective_to_dttm = :effective_to_dttm AND
                resource.deleted_flg IS FALSE
    -- Потенциально, source_rk=null может получиться из-за также и из-за LEFT JOIN в случае,
    -- когда в bridge_source нет соответствующего source_cd. Но мы делаем допущение, что тогда и
    -- в линке записей с таким source_rk тоже не будет.
            LEFT JOIN {bridge_source} AS source ON
                staging.source_cd = source.source_cd AND
                source.effective_to_dttm = :effective_to_dttm AND
                source.deleted_flg IS FALSE
    )
  , delta AS (
        SELECT
            stg.resource_rk
          , stg.source_rk AS src__source_rk
          , lnk.source_rk AS tgt__source_rk
          , lnk.deleted_flg AS tgt__deleted_flg
          -- I=insert, U=update, D=delete, R=restore
          , CASE
                -- В линке нет связи для данного ресурса, а в стейдже она появилась.
                -- Нужно вставить в линк одну запись с новым source_rk и deleted_flg=false.
                WHEN lnk.resource_rk IS NULL AND stg.source_rk NOTNULL THEN 'I'
                -- В линке есть, не помеченная удаленной, связь для данного ресурса,
                -- а в стейдже она исчезла.
                -- Нужно вставить в линк одну запись со старым source_rk и deleted_flg=true
                WHEN lnk.resource_rk NOTNULL AND
                     stg.source_rk IS NULL AND
                     lnk.deleted_flg IS FALSE THEN 'D'
                -- И в линке, и в стейдже есть связь для данного ресурса, но с разными источниками,
                -- и при этом...
                WHEN lnk.resource_rk NOTNULL AND
                     stg.source_rk != lnk.source_rk THEN
                    CASE lnk.deleted_flg
                        -- ...в линке связь не помечена удаленной.
                        -- Нужно вставить в линк две записи: со старым source_rk и deleted_flg=true
                        -- и с новым source_rk и deleted_flg=false.
                        WHEN FALSE THEN 'U'
                        -- ...в линке связь помечена удаленной.
                        -- Поступаем аналогично вставке новой связи.
                        WHEN TRUE THEN 'I'
                    END
                -- В линке есть, помеченная удаленной, связь для данного ресурса, а в стейдже она
                -- пришла снова, т.е. надо разудалить.
                -- Нужно вставить в линк одну запись со старым source_rk и deleted_flg=false.
                WHEN lnk.resource_rk NOTNULL AND
                     stg.source_rk = lnk.source_rk AND
                     lnk.deleted_flg IS TRUE THEN 'R'
            END AS item_action
        FROM
            staging_with_rk AS stg
            LEFT JOIN {link_resource_source} AS lnk ON
                stg.resource_rk = lnk.resource_rk AND
                lnk.effective_to_dttm = :effective_to_dttm
    )
  , update AS (
    -- @fmt:off
        UPDATE {link_resource_source} AS link
        SET effective_to_dttm = :effective_from_dttm
        FROM delta
        WHERE
            delta.item_action IN ('U', 'D', 'R')
            AND link.effective_to_dttm = :effective_to_dttm
            AND link.resource_rk = delta.resource_rk
    -- @fmt:on
    )
INSERT
INTO {link_resource_source} ( resource_rk
                            , source_rk
                            , effective_from_dttm
                            , effective_to_dttm
                            , deleted_flg)
SELECT
    resource_rk
  , src__source_rk
  , :effective_from_dttm
  , :effective_to_dttm
  , FALSE AS deleted_flg
FROM delta
WHERE
    item_action IN ('I', 'U', 'R')

UNION ALL
SELECT
    resource_rk
  , tgt__source_rk
  , :effective_from_dttm
  , :effective_to_dttm
  , TRUE AS deleted_flg
FROM delta
WHERE
    item_action IN ('D', 'U')
