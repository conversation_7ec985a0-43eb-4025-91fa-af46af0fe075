-- TODO: Не самый оптимальный способ получения "дельты":
--  distinct src => anti join src, tgt => full join src, tgt
--  Если сразу выделить из src ключи для удаления/(изменения/добавления), может получится попроще
WITH
    staging AS (
        SELECT
            resource_cd AS resource_cd
          , dataset ->> 'name' AS table_name
          , dataset ->> 'schema_name' AS schema_name
        FROM
            {stage_table}
            LEFT JOIN LATERAL jsonb_array_elements(
                nullif(definition -> 'datasets', 'null')
            ) AS _(dataset) ON TRUE
    )
  , staging_with_rk AS (
    -- Поскольку нам присылают джсоны ресурсов целиком, то в этой таблице содержатся
    -- актуальные связи ресурсов с источниками (для присланных resource_rk), по сути снапшот.
    -- Поэтому, значение data_action_type_rk не важно -- можно ориентировать только на сравнение
    -- table_rk в стейдже и линке.
    -- Ожидается, что для data_action_type_rk=deleted поле definition всегда null, поэтому такие
    -- записи в стейдже можно опознать по table_rk=null.
        SELECT DISTINCT
            -- Distinct на случай, если в стейдже придут >1 записи с table_name и schema_name,
            -- которых нет в bridge_table
            resource.resource_rk
          , tabl.table_rk
        FROM
            staging
            INNER JOIN {bridge_resource} AS resource
                ON staging.resource_cd = resource.resource_cd AND
                   resource.effective_to_dttm = :effective_to_dttm
            LEFT JOIN {bridge_table} AS tabl
                ON staging.table_name = tabl.table_name AND
                   staging.schema_name = tabl.schema_name AND
                   tabl.effective_to_dttm = :effective_to_dttm
    )
  , filtered_link AS (
    -- Поскольку связь resource-table = 1-M, то в для каждого resource_rk в стейдже и линке
    -- содержатся множества (в мат. смысле) связей. Для формирования дельты нужно определить
    -- пересечение этих множеств через FULL JOIN, поэтому нужно взять из линка связи только
    -- для тех resource_rk, которые пришли в стейдже.
        SELECT
            resource_rk
          , table_rk
          , effective_from_dttm
          , effective_to_dttm
          , deleted_flg
        FROM {link_resource_table} AS lnk
        WHERE
            lnk.effective_to_dttm = :effective_to_dttm AND
            exists (
                SELECT
                FROM staging_with_rk AS stg
                WHERE stg.resource_rk = lnk.resource_rk
            )
    )
  , delta AS (
        SELECT
            resource_rk
          , stg.table_rk AS src__table_rk
          , lnk.table_rk AS tgt__table_rk
          , lnk.deleted_flg AS tgt__deleted_flg
          , CASE
                WHEN stg.table_rk NOTNULL AND lnk.table_rk IS NULL
                    THEN 'I'
                WHEN stg.table_rk IS NULL AND lnk.table_rk NOTNULL AND lnk.deleted_flg IS FALSE
                    THEN 'D'
                WHEN stg.table_rk NOTNULL AND lnk.table_rk NOTNULL AND lnk.deleted_flg IS TRUE
                    THEN 'U'
            END AS item_action
        FROM
            staging_with_rk AS stg
            FULL JOIN filtered_link AS lnk USING (resource_rk, table_rk)
    )
  , update AS (
    -- @fmt:off
        UPDATE {link_resource_table} AS link
        SET effective_to_dttm = :effective_from_dttm
        FROM delta
        WHERE
            delta.item_action IN ('U', 'D')
            AND link.effective_to_dttm = :effective_to_dttm
            AND link.resource_rk = delta.resource_rk
            AND link.table_rk = delta.tgt__table_rk
    -- @fmt:on
    )
INSERT
INTO {link_resource_table} ( resource_rk
                           , table_rk
                           , effective_from_dttm
                           , effective_to_dttm
                           , deleted_flg)
SELECT
    resource_rk
  , src__table_rk
  , :effective_from_dttm
  , :effective_to_dttm
  , FALSE AS deleted_flg
FROM delta
WHERE
    item_action IN ('I', 'U')

UNION ALL
SELECT
    resource_rk
  , tgt__table_rk
  , :effective_from_dttm
  , :effective_to_dttm
  , TRUE AS deleted_flg
FROM delta
WHERE
    item_action = 'D'
