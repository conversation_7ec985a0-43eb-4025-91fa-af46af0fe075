from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, Field
from sqlalchemy.orm import Session
from structlog.stdlib import Bo<PERSON><PERSON>og<PERSON>, get_logger

from metaloader_rest_api.ceh_version import ceh_version_int
from metaloader_rest_api.common_param import (
    DEPLOYMENT_MODULE_DEFAULT,
    DEPLOYMENT_RELEASE_DEFAULT,
    RELEASE_PATTERN,
)

_DEFAULT_LOGGER = get_logger(__name__)


class VersionParam(BaseModel):
    module: str = DEPLOYMENT_MODULE_DEFAULT
    release: str = Field(pattern=RELEASE_PATTERN, default=DEPLOYMENT_RELEASE_DEFAULT)
    description: Optional[str] = None
    build_date: Optional[datetime] = None


def put_version(
    session: Session,
    load_id: UUID,
    effective_date: datetime,
    params: VersionParam,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> int:
    return ceh_version_int.put_version(
        session=session,
        load_id=load_id,
        effective_date=effective_date,
        module=params.module,
        release=params.release,
        build_date=params.build_date,
        description=params.description,
        log=log,
    )
