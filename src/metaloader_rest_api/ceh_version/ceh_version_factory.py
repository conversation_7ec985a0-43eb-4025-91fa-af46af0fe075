from datetime import datetime
from typing import Optional

from metaloader_rest_api.ceh_version.ceh_version_model import Version


def version_factory(
    release: str,
    description: Optional[str] = None,
    build_date: Optional[datetime] = None,
) -> Version:
    major, minor, fix = release.split(".", 2)

    return Version(
        major=int(major),
        minor=int(minor),
        fix=int(fix),
        description=description,
        build_date=build_date,
    )
