from datetime import datetime
from typing import Optional
from uuid import UUID

from sqlalchemy.orm import Session
from structlog.stdlib import Bo<PERSON><PERSON><PERSON><PERSON>, get_logger

from metaloader_rest_api.ceh_module.ceh_module_provider import provide_ceh_module
from metaloader_rest_api.ceh_version import ceh_version_repository
from metaloader_rest_api.ceh_version.ceh_version_factory import version_factory

_DEFAULT_LOGGER = get_logger(__name__)


def put_version(
    session: Session,
    load_id: UUID,
    effective_date: datetime,
    module: str,
    release: str,
    description: Optional[str] = None,
    build_date: Optional[datetime] = None,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> int:
    return ceh_version_repository.put(
        session=session,
        module_id=provide_ceh_module(
            session=session,
            name=module,
            log=log,
        ),
        effective_date=effective_date,
        version=version_factory(
            release=release,
            description=description,
            build_date=build_date,
        ),
        log=log,
    )
