from datetime import datetime

from sqlalchemy import text
from sqlalchemy.orm import Session
from structlog.stdlib import <PERSON><PERSON><PERSON><PERSON><PERSON>, get_logger

from metaloader_rest_api.ceh_version.ceh_version_model import Version

_DEFAULT_LOGGER = get_logger(__name__)


def put(
    session: Session,
    module_id: int,
    effective_date: datetime,
    version: Version,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> int:
    return session.scalar(
        statement=text("""
            INSERT INTO metamodel.bridge_version AS v (release_major_num
                                                     , release_minor_num
                                                     , release_fix_num
                                                     , release_desc
                                                     , release_build_dttm
                                                     , release_delivery_dttm
                                                     , code_delivery_rk)
                 VALUES (:release_major_num
                       , :release_minor_num
                       , :release_fix_num
                       , :release_desc
                       , :release_build_dttm
                       , :release_delivery_dttm
                       , :code_delivery_rk)
             ON CONFLICT (release_major_num
                        , release_minor_num
                        , release_fix_num
                        , code_delivery_rk)
               DO UPDATE 
                     SET release_desc       = COALESCE(:release_desc,       v.release_desc)
                       , release_build_dttm = COALESCE(:release_build_dttm, v.release_build_dttm)
               RETURNING version_rk
        """),
        params={
            "release_major_num": version.major,
            "release_minor_num": version.minor,
            "release_fix_num": version.fix,
            "release_desc": version.description,
            "release_build_dttm": version.build_date,
            "release_delivery_dttm": effective_date,
            "code_delivery_rk": module_id,
        },
    )
