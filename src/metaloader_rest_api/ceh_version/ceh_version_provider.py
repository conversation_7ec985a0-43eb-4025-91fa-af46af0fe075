from datetime import datetime

from sqlalchemy.orm import Session
from structlog.stdlib import <PERSON><PERSON><PERSON>og<PERSON>, get_logger

from metaloader_rest_api.ceh_version import ceh_version_repository
from metaloader_rest_api.ceh_version.ceh_version_factory import version_factory

_DEFAULT_LOGGER = get_logger(__name__)


def provide_ceh_version(
    session: Session,
    module_id: int,
    effective_date: datetime,
    release: str,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> int:
    return ceh_version_repository.put(
        session=session,
        module_id=module_id,
        effective_date=effective_date,
        version=version_factory(release=release),
        log=log,
    )
