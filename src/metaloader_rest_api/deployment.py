from datetime import datetime
from typing import Optional, <PERSON><PERSON>

from sqlalchemy.orm import Session
from structlog.stdlib import Bo<PERSON><PERSON><PERSON><PERSON>, get_logger

from metaloader_rest_api.ceh_module.ceh_module_provider import provide_ceh_module
from metaloader_rest_api.ceh_service.ceh_service_provider import provide_ceh_service
from metaloader_rest_api.ceh_version.ceh_version_provider import provide_ceh_version

_DEFAULT_LOGGER = get_logger(__name__)


def get_deployment(
    session: Session,
    module: str,
    service: Optional[str],
    release: str,
    effective_date: datetime,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> Tuple[int, int, int]:
    module_id = provide_ceh_module(session, module, log)
    service_id = (
        provide_ceh_service(session, service, log) if service is not None else -1
    )
    version_id = provide_ceh_version(session, module_id, effective_date, release, log)

    return module_id, service_id, version_id
