from typing import Any, Dict, Mapping, Optional, Sequence, Set


class AirflowDag:
    _dag_skip_attributes = {
        "dag_id",
        "description",
        "tags",
        "is_active",
        "is_paused",
        "has_import_errors",
        "scheduler_lock",
        "pickle_id",
        "last_expired",
        "last_parsed_time",
        "last_pickled",
        "next_dagrun",
        "next_dagrun_create_after",
        "next_dagrun_data_interval_end",
        "next_dagrun_data_interval_start",
    }

    _dag_skip_default_attributes = {
        "is_subdag": False,
        "has_task_concurrency_limits": False,
        "default_view": "grid",
        "timetable_description": "Never, external triggers only",
    }

    _task_skip_attributes = {
        "end_date",
        "start_date",
    }

    _task_skip_default_attributes = {}

    def __init__(
        self,
        dag: Dict[str, Any],
        tasks: Optional[Sequence[Any]] = None,
    ) -> None:
        self._dag = dag
        self._tasks = tasks

    @property
    def id(self) -> str:
        return self._dag["dag_id"]

    @property
    def description(self) -> Optional[str]:
        return self._dag.get("description")

    @property
    def tags(self) -> Sequence[str]:
        return tuple(tag["name"] for tag in self._dag.get("tags", ()))

    @property
    def dag(self) -> Dict[str, Any]:
        return self._skip(
            self._dag,
            self._dag_skip_attributes,
            self._dag_skip_default_attributes,
        )

    @property
    def tasks(self) -> Optional[Sequence[Any]]:
        return (
            None
            if self._tasks is None
            else [
                self._skip(
                    task,
                    self._task_skip_attributes,
                    self._task_skip_default_attributes,
                )
                for task in self._tasks
            ]
        )

    @staticmethod
    def _skip(
        items: Dict[str, Any],
        attributes: Set[str],
        default_attributes: Mapping[str, Any],
    ) -> Dict[str, Any]:
        return {
            attribute: value
            for attribute, value in items.items()
            if value is not None
            and attribute not in attributes
            and value != default_attributes.get(attribute, None)
        }
