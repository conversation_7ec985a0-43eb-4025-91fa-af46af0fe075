from typing import Any, Con<PERSON><PERSON>ana<PERSON>, Iterator, Optional, Sequence

from httpx import Client, QueryParams, Response
from sqlalchemy.cyextension.util import Mapping
from structlog.stdlib import BoundLogger, get_logger

from metaloader_rest_api.http.http_loader import load_http_json, load_http_text
from metaloader_rest_api.http.http_page_provider import (
    HTTP_DEFAULT_PAGE_SIZE,
    PageQueryParamsFactory,
    http_page_provider,
)

_DEFAULT_LOGGER = get_logger(__name__)


class AirflowClient(ContextManager["AirflowClient"]):
    def __init__(
        self,
        http_client: Client,
        log: BoundLogger = _DEFAULT_LOGGER,
    ):
        super().__init__()

        self._log = log.bind(actor="airflow_client", url=http_client.base_url)

        self._http_client = http_client
        self._http_client.base_url = f"{self._http_client.base_url}/api/v1"

    def __enter__(self):
        self._http_client.__enter__()

        self._log.info("connect")

        return self

    def __exit__(self, exc_type, exc_value, traceback, /):
        self._log.info("disconnect")

        self._http_client.__exit__(exc_type, exc_value, traceback)

        return None

    def dags(
        self,
        page_size: int = HTTP_DEFAULT_PAGE_SIZE,
        page_limit: Optional[int] = None,
    ) -> Iterator[Sequence[Any]]:
        self._log.info("dags", page_size=page_size, page_limit=page_limit)

        yield from http_page_provider(
            client=self._http_client,
            url="dags",
            query_params_factory=self._query_params_factory(page_size),
            response_processor=self._response_processor,
            size=page_size,
            limit=page_limit,
            log=self._log,
        )

    def dag_details(
        self,
        dag_id: str,
    ) -> Any:
        return load_http_json(self._http_client, f"dags/{dag_id}/details")

    def dag_tasks(
        self,
        dag_id: str,
    ) -> Sequence[Any]:
        return load_http_json(self._http_client, f"dags/{dag_id}/tasks")

    def dag_source(
        self,
        token: str,
    ) -> str:
        return load_http_text(self._http_client, url=f"dagSources/{token}")

    @staticmethod
    def dag_id(dag: Mapping[str, Any]) -> str:
        return dag["dag_id"]

    @staticmethod
    def _query_params_factory(limit: int) -> PageQueryParamsFactory:
        def query_params_factory(offset: int) -> QueryParams:
            return QueryParams(
                {
                    "offset": offset,
                    "limit": limit,
                    "order_by": "dag_id",
                }
            )

        return query_params_factory

    @staticmethod
    def _response_processor(response: Response) -> Sequence[Any]:
        return response.raise_for_status().json().get("dags", [])
