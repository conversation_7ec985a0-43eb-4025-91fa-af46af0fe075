from contextlib import contextmanager
from itertools import chain
from typing import Iterator, Optional

from httpx import Client
from structlog.stdlib import Bo<PERSON><PERSON>og<PERSON>, get_logger

from metaloader_rest_api.airflow.airflow_client import AirflowClient
from metaloader_rest_api.airflow.airflow_dag import AirflowDag
from metaloader_rest_api.http.http_page_provider import HTTP_DEFAULT_PAGE_SIZE

_DEFAULT_LOGGER = get_logger(__name__)


@contextmanager
def airflow_dag_provider(
    http_client: Client,
    page_size: int = HTTP_DEFAULT_PAGE_SIZE,
    page_limit: Optional[int] = None,
    with_tasks: bool = False,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> Iterator[Iterator[AirflowDag]]:
    with AirflowClient(http_client, log) as client:
        yield (
            AirflowDag(
                dag,
                client.dag_tasks(client.dag_id(dag)) if with_tasks else None,
            )
            for dag in chain.from_iterable(client.dags(page_size, page_limit))
        )
