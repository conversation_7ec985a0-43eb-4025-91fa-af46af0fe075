from contextlib import contextmanager
from typing import Callable, Iterator

from httpx import Client
from structlog.stdlib import Bo<PERSON><PERSON>og<PERSON>, get_logger

from metaloader_rest_api.airflow.airflow_client import AirflowClient

_DEFAULT_LOGGER = get_logger(__name__)


@contextmanager
def airflow_dag_source_loader(
    http_client: Client,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> Iterator[Callable[[str], str]]:
    with AirflowClient(http_client, log) as client:
        yield client.dag_source
