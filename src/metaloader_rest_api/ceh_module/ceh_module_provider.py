from sqlalchemy.orm import Session
from structlog.stdlib import <PERSON><PERSON><PERSON><PERSON><PERSON>, get_logger

from metaloader_rest_api.db.db_provider import db_provide

_DEFAULT_LOGGER = get_logger(__name__)


def provide_ceh_module(
    session: Session,
    name: str,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> int:
    return db_provide(
        session=session,
        query="""
            SELECT code_delivery_rk
              FROM dict.dict_code_delivery
             WHERE code_delivery_cd = :code_delivery_cd
        """,
        params={
            "code_delivery_cd": name,
        },
        log=log,
    )[0]


def provide_ceh_module_master_flow_layout_path(
    session: Session,
    id: int,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> str:
    return db_provide(
        session=session,
        query="""
            SELECT code_folder_path
              FROM dict.dict_code_folder
             WHERE code_folder_rk IN (SELECT code_folder_rk
                                        FROM dict.link_code_delivery_folder
                                       WHERE code_delivery_rk = :code_delivery_rk
                                         AND is_master_flow_flg IS TRUE)
        """,
        params={
            "code_delivery_rk": id,
        },
        log=log,
    )[0]
