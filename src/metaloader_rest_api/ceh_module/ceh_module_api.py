from datetime import datetime
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, RootModel
from sqlalchemy.orm import Session
from structlog.stdlib import Bound<PERSON>ogger, get_logger

from metaloader_rest_api.ceh_module import ceh_module_int

_DEFAULT_LOGGER = get_logger(__name__)


class LoadModulesParam(BaseModel):
    code_delivery_cd: str
    code_delivery_name: str
    code_delivery_desc: Optional[str] = None
    ris_src_id: str
    ris_src_code: str
    is_ddl_flg: bool
    is_flow_flg: bool
    is_config_flg: bool


class LoadModulesParams(RootModel):
    root: List[LoadModulesParam]


def load_modules(
    session: Session,
    load_id: UUID,
    effective_date: datetime,
    params: LoadModulesParams,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> None:
    ceh_module_int.load_modules(
        session=session,
        load_id=load_id,
        effective_date=effective_date,
        modules=iter(params.model_dump()),
        log=log,
    )
