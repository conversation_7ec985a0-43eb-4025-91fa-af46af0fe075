from datetime import datetime

from sqlalchemy import text
from sqlalchemy.orm import Session
from structlog.stdlib import <PERSON><PERSON><PERSON><PERSON><PERSON>, get_logger

from metaloader_rest_api import etl
from metaloader_rest_api.ceh_module.ceh_module_model import CehModule
from metaloader_rest_api.common_model import LAST_DATE
from metaloader_rest_api.common_repository import (
    BufferedStageBatchRepository,
)

_DEFAULT_LOGGER = get_logger(__name__)


class StageModuleRepository(BufferedStageBatchRepository[CehModule]):
    def __init__(
        self,
        session: Session,
        effective_date: datetime,
        table_id: str,
        page_size: int = BufferedStageBatchRepository.PAGE_SIZE_DEFAULT,
        log: BoundLogger = _DEFAULT_LOGGER,
    ):
        table = "mart_module"
        super().__init__(
            session,
            table,
            table_id,
            self._create_statement,
            self._load_statement,
            page_size,
            log,
        )
        self._effective_date = effective_date
        self._table_id = table_id

    @property
    def effective_date(self) -> datetime:
        return self._effective_date

    @effective_date.setter
    def effective_date(self, value: datetime) -> None:
        self._effective_date = value

    def __exit__(self, exc_type, exc_value, traceback):
        super().__exit__(exc_type, exc_value, traceback)

        if exc_type is None:
            self.load_dict()
            self.load_link()

    @staticmethod
    def _create_statement(table: str) -> str:
        return f"""
            CREATE TABLE {table} (
                code_delivery_cd   TEXT             
              , code_delivery_name TEXT             
              , code_delivery_desc TEXT             
              , ris_src_id         TEXT 
              , ris_src_code       TEXT         
              , is_ddl_flg         BOOLEAN
              , is_flow_flg        BOOLEAN     
              , is_config_flg      BOOLEAN         
            )
        """

    @staticmethod
    def _load_statement(table: str) -> str:
        return f"""
            INSERT INTO {table} (code_delivery_cd
                               , code_delivery_name
                               , code_delivery_desc
                               , ris_src_id                               
                               , ris_src_code
                               , is_ddl_flg
                               , is_flow_flg
                               , is_config_flg)
                 VALUES (:code_delivery_cd
                       , :code_delivery_name
                       , :code_delivery_desc
                       , :ris_src_id                       
                       , :ris_src_code
                       , :is_ddl_flg
                       , :is_flow_flg
                       , :is_config_flg)
        """

    def load_dict(self) -> None:
        log = self._log.bind(table="dict_code_delivery")
        log.info("begin")

        merge_stage = f"stg.dict_code_delivery_{self._table_id}"

        log.info("create stage", table=merge_stage)
        self._session.execute(text(f"DROP TABLE IF EXISTS {merge_stage}"))
        self._session.execute(
            text(f"""
                CREATE TABLE {merge_stage} (
                    code_delivery_cd    TEXT
                  , code_delivery_name  TEXT
                  , code_delivery_desc  TEXT
                  , is_ddl_flg          BOOLEAN
                  , is_flow_flg         BOOLEAN
                  , is_config_flg       BOOLEAN
                  )
            """)
        )

        self._session.execute(
            text(f"""
                INSERT INTO {merge_stage} (code_delivery_cd
                                         , code_delivery_name
                                         , code_delivery_desc
                                         , is_ddl_flg
                                         , is_flow_flg
                                         , is_config_flg)
                     SELECT code_delivery_cd
                          , code_delivery_name
                          , code_delivery_desc
                          , is_ddl_flg
                          , is_flow_flg
                          , is_config_flg
                       FROM {self.table} 
            """)
        )

        log.info("merge")
        etl.merge_without_history(
            session=self._session,
            table="dict.dict_code_delivery",
            stage=merge_stage,
            primary_key="code_delivery_rk",
            sequence="dict.dict_code_delivery_seq",
            keys=[
                "code_delivery_cd",
            ],
            values=[
                "code_delivery_name",
                "code_delivery_desc",
                "is_ddl_flg",
                "is_flow_flg",
                "is_config_flg",
            ],
            log=log,
        )
        log.info("end")

    def load_link(self) -> None:
        log = self._log.bind(table="link_source_delivery")
        log.info("begin")

        merge_stage = f"stg.link_source_delivery_{self._table_id}"

        log.info("create stage", table=merge_stage)
        self._session.execute(text(f"DROP TABLE IF EXISTS {merge_stage}"))
        self._session.execute(
            text(f"""
                CREATE TABLE {merge_stage} (
                    source_rk        BIGINT
                  , code_delivery_rk SMALLINT
                  )
            """)
        )

        self._session.execute(
            statement=text(f"""
                INSERT INTO {merge_stage} (source_rk
                                         , code_delivery_rk)
                     SELECT s.source_rk         source_rk
                          , cd.code_delivery_rk code_delivery_rk
                       FROM {self.table}                              t
                       JOIN metamodel.bridge_source                   s
                         ON s.ris_src_id        = t.ris_src_id
                        AND s.effective_to_dttm = :effective_to_dttm
                        AND s.deleted_flg IS FALSE                            
                       JOIN dict.dict_code_delivery                   cd
                         ON cd.code_delivery_cd  = t.code_delivery_cd
                        AND cd.deleted_flg IS FALSE                            
            """),
            params={
                "effective_to_dttm": LAST_DATE,
            },
        )

        log.info("merge")
        etl.merge(
            session=self._session,
            version_id=None,
            effective_date=self.effective_date,
            table="metamodel.link_source_delivery",
            stage=merge_stage,
            primary_key=None,
            keys=[
                "source_rk",
                "code_delivery_rk",
            ],
            values=[],
            log=log,
        )
        log.info("end")
