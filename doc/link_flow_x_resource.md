Связь потоков с ресурсами
====

# Алгоритм

## Общий подход

1. Обрабатываем каждый поток (контрольный и рабочий) и пытаемся определить его связь с ресурсами (см. ниже). Полученные 
   данные загружаем в промежуточную таблицу - mart_flow_resource.
   1. По возможности определяем значения параметров для имён и атрибутов ресурсов.
   2. При необходимости обрабатываем импортируемые потоки (скорее всего не нужно).
2. Используя ранее установленную связь между контрольными и рабочими потоками - link_flow - и описание ресурса - bridge_resource,
   определяем так же связь между ресурсами-источниками и ресурсами-приёмниками (см. ниже) - link_flow_resource.
   1. Приоритет для установления такой связи отдаётся контрольным потокам, в рабочих потоках эта связь считается устаревшей
      и должна использоваться, только если нет другой возможности её определить.

## Для рабочих потоков

Для оператора типа `reg_delta_operator`/`multi_reg_delta_operator` в следующих атрибутах задаются
 - `target_resource_cd`/`target_resource_name` - имя ресурса-приёмника
 - `source_resource_names` - имена ресурсов-источников (legacy)
 - `target_dataset`/`target_datasets`/`target_table_desc` - _dataset_ ресурса
 - `algorithm_uid`/`algorithm_name` - _id_ алгоритма

### Ресурсы-приёмники

Так же список имён ресурсов-приёмников задаётся в атрибуте `target_resource_names` оператора типа `locked_resources_operator`
или в атрибуте `resource_cds` оператора типа `lock_resources_step`


## Для контрольных потоков

1. Для большинства контрольных потоков *IDL* (~3k) достаточно определить для оператора `get_max_loaded_version_operator` 
   следующие атрибуты:
   - `target_resource_cd`/`target_resource_name` - имя ресурса-приёмника
   - `source_resource_cds`/`source_resource_names` - имена ресурсов-источников
   - `algorithm_uid`/`algo_name` - _id_ алгоритма
2. Для остальных (~350) используется шаблон `cf_ceh_template` со следующими атрибутами:
   - `target_resource_cds` - имена ресурсов-приёмников
   - `source_resource_cds` - имена ресурсов-источников
   - `algorithm_uid` - _id_ алгоритма

### Ресурсы-приёмники

Так же список имён ресурсов-источников задаётся в атрибуте `source_resource_names` шаблона потока `ceh_frm_idl_utils.source_resourse_transfer`

### _ID_ алгоритм

Часто конкретное значение _id_ алгоритма задаётся только в управляющем потоке и передаётся в рабочий через параметры
посредством атрибута `algorithm_uid` операторов типа `trigger_dag_operator`/`sequential_trigger_dag_operator` 
или `trigger_job_step`/`trigger_batch_job_step`.

> Имена ресурсов как и другие значения атрибутов могут быть параметризованы, поэтому их конкретные значения необходимо 
извлекать и списка параметров потока или из выходных параметров других операторов при возможности.

---

IDL
====

# Ресурсы для записи (target)

## Оператор блокировки ресурсов (legacy)

Нужен для получения `target_resource_names` для рабочих потоков.

 - Тип `ceh_core_idl.app.operators.services.locked_resources_operator`
 - Код `ceh_core_idl/libs/af_lib/complex_operators/resource_management/locked_resources_operator/locked_resources_operator.py`
 - Примеры
    - general_ledger/rdv_idl/flow_dumps/wrk_deal_rtls_idl_bbridge_product.yaml
    ```yaml
      - id: block_resources
        description: Блокировка ресурсов
        type: ceh_core_idl.app.operators.services.locked_resources_operator
        metadata:
          - name: version_id
            datatype: int
            type: out
        properties:
          transaction_uid: ${open_transactions.tx_uid}
          target_resource_names:
            - ceh.idl.bbridge_product.rtls
            - ceh.idl.sal_product.rtls
        sequencer:
          dependencies:
            - ref: open_transactions
    ```
    - general_ledger/rdv_idl/flow_dumps/wrk_ins_rdv_idl_bbridge_1395_insurance.yaml
    ```yaml
          - id: block_resources
            type: ceh_core_idl.app.operators.services.locked_resources_operator
            description: Блокировка ресурсов
            properties:
              transaction_uid: ${open_transaction.tx_uid}
              target_resource_names:
                - ${target_resource_name_bbridge_bin_wayn_wvtb}
            sequencer:
              dependencies:
                - ref: open_transaction
    ```

## Оператор блокировки ресурсов (new)

Нужен для получения `resource_cds`.

 - Тип `ceh_core_libs.steps.services.lock_resources_step`
 - Код `ceh_core_libs/steps/services/lock_resources_step.py`
 - Примеры
    - general_ledger/rdv_idl/flow_dumps/wrk_ins_rdv_idl_bbridge_insurance_risk.yaml
    ```yaml
      - id: block_resources
        type: ceh_core_libs.steps.services.lock_resources_step
        description: Блокировка ресурсов
        metadata:
          - name: version_id
            datatype: int
            type: out
        properties:
          transaction_uid: ${open_transaction.result.transaction_uid}
          transaction_token: ${open_transaction.result.transaction_token}
          resource_cds:
            - ceh.idl.bbridge_insurance_risk.ins
        sequencer:
          dependencies:
            - ref: open_transaction
    ```
    - general_ledger/rdv_idl/flow_dumps/wrk_com_rdv_idl_bbridge_eodflags.yaml
    ```yaml
      - id: block_resources
        type: ceh_core_libs.steps.services.lock_resources_step
        description: Блокировка ресурсов
        properties:
          transaction_uid: ${open_transaction.result.transaction_uid}
          transaction_token: ${open_transaction.result.transaction_token}
          resource_cds:
            - ${target_resource_cd}
        sequencer:
          dependencies:
            - ref: open_transaction
    ```

## Оператор загрузки дельты

Нужен для получения `algorithm_uid` и `target_dataset`, а так же `algorithm_name`/`algorithm_uid` для рабочих потоков.

 - Тип `ceh_core_idl.app.operators.services.reg_delta_operator`/`multi_reg_delta_operator`
 - Код `ceh_core_idl/libs/af_lib/complex_operators/resource_management/reg_delta_operator/reg_delta_operator.py`/`multi_reg_delta_operator/multi_reg_delta_operator.py`
 - Примеры
    - general_ledger/rdv_idl/flow_dumps/wrk_deal_rtls_idl_bbridge_product.yaml
    ```yaml
      - id: update_resource_state_bbridge_product
        description: Обновление состояния ресурса
        type: ceh_core_idl.app.operators.services.reg_delta_operator
        properties:
          transaction_uid: ${open_transactions.tx_uid}
          stable_version_id: ${stable_version_id}
          target_resource_name: ceh.idl.bbridge_product.rtls
          target_table_names:
            - ${target_table_name}
          source_resource_names: ${source_resource_names}
          max_src_version_id: ${src_version_id}
          load_id: ${get_load_id.load_id}
          algorithm_name: alg_product_rtls
          txi_table_name: idl_dlt.t_dlt_lg_${get_load_id.load_id}_bbridge_product
        sequencer:
          dependencies:
            - ref: load_delta_bbridge_product
    ```
    - general_ledger/rdv_idl/flow_dumps/wrk_ins_rdv_idl_bbridge_1395_insurance.yaml
    ```yaml
      - id: update_resource_state_blink_insurance_x_counterparty_ins
        type: ceh_core_idl.app.operators.services.reg_delta_operator
        description: Обновление состояния ресурса
        properties:
          transaction_uid: ${open_transaction.result.transaction_uid}
          common_version_id: ${common_version_id}
          target_resource_cd: ${target_resource_name_blink_insurance_x_counterparty_ins}
          target_dataset:
            name: blink_insurance_x_counterparty
            schema: idl
          load_id: ${load_id_blink_insurance_x_counterparty_2}
          delta_dataset: ${load_delta_blink_insurance_x_counterparty.result_dataset}
          algorithm_uid: ${algorithm_uid}
        sequencer:
          dependencies:
            - ref: load_delta_blink_insurance_x_counterparty
    ```
    - general_ledger/rdv_idl/flow_dumps/wrk_ins_rdv_idl_bbridge_insurance_risk.yaml
    ```yaml
      - id: update_resource_state_hub_insurance_risk
        type: ceh_core_idl.app.operators.services.reg_delta_operator
        description: Обновление состояния ресурса
        properties:
          transaction_uid: ${open_transaction.result.transaction_uid}
          common_version_id: ${common_version_id}
          target_resource_cd: ${target_resource_name_hub_insurance_risk}
          target_dataset:
            name: hub_insurance_risk
            schema: idl
          load_id: ${load_id_hub_insurance_risk_2}
          delta_dataset: ${load_delta_hub_insurance_risk.result_dataset}
          algorithm_uid: ${algorithm_uid}
        sequencer:
          dependencies:
            - ref: load_delta_hub_insurance_risk
    ```
    - general_ledger/rdv_idl/flow_dumps/wrk_ins_rdv_idl_bbridge_insurance_risk.yaml
    ```yaml
      - id: multi_update_resource_state_bbridge_insurance_risk_ins
        type: ceh_core_idl.app.operators.services.multi_reg_delta_operator
        description: Обновление состояния ресурса
        properties:
          transaction_uid: ${open_transaction.result.transaction_uid}
          common_version_id: ${common_version_id}
          target_resource_cd: ${target_resource_name_bbridge_insurance_risk_ins}
          target_datasets:
            - target_dataset:
                schema: idl
                name: bbridge_insurance_risk
              delta_dataset: ${load_delta_bbridge_insurance_risk.result_dataset}
              load_id: ${load_id_bbridge_insurance_risk_0}
            - target_dataset:
                schema: idl
                name: sal_insurance_risk
              delta_dataset: ${load_dummy_sal_insurance_risk.result_dataset}
              load_id: ${load_id_sal_insurance_risk_1}
          algorithm_uid: ${algorithm_uid}
    ```

# Ресурсы для чтения (source) и так же для записи (target)

## Оператор получения максимальной загруженной версии ресурсов

Нужен для получения `target_resource_name` и `source_resource_names`, а так же `algo_name` для контрольных потоков.

 - Тип `ceh_core_idl.app.operators.services.get_max_loaded_version_operator`
 - Код `ceh_core_idl/libs/af_lib/complex_operators/resource_management/get_max_loaded_version_operator/get_max_loaded_version_operator.py`
 - Примеры
    - general_ledger/rdv_idl/flow_dumps/cf_deal_rtls_idl_bbridge_product.yaml
    ```yaml
      - id: get_max_loaded_version
        description: Проверим test оператор с out параметрами
        type: ceh_core_idl.app.operators.services.get_max_loaded_version_operator
        metadata:
          - name: max_loaded_version
            datatype: int
            type: out
          - name: date_last_download
            datatype: str
            type: out
        properties:
          target_resource_name: ${target_resource_name}
          source_resource_names: ${source_resource_names}
          stable_version: ${get_common_version.common_version}
          condition: any
          algo_name: alg_product_rtls
        sequencer:
          dependencies:
            - ref: get_common_version
    ```


## Поток получения метрик

Нужен для получения `source_resource_names` и `source_resource_names`, а так же `algorithm_uid` для контрольных потоков.

 - Тип `ceh_frm_idl_utils.source_resourse_transfer`
 - Код `general_ledger/rdv_idl/flow_dumps/ceh_frm_idl_utils.yaml`
 - Примеры
    - general_ledger/rdv_idl/flow_dumps/cf_deal_rtls_idl_bbridge_product.yaml
    ```yaml
      - id: get_metrics
        builder: ceh_core_idl.app.builders.include_flow_builder
        description: Получение метрик
        properties:
          ref: source_resourse_transfer
          properties:
            source_resource_names: ${source_resource_names}
            target_resource_names:
              - ${target_resource_name}
            algorithm_uid: alg_product_rtls
        sequencer:
          dependencies:
            - ref: get_workflow_parameters
    ```

## Шаблон контрольного потока

Нужен для получения `source_resource_names` и `source_resource_names`, а так же `algorithm_uid` для контрольных потоков.

 - Тип `idl_cf_template.cf_ceh_template`
 - Код `general_ledger/rdv_idl/flow_dumps/idl_cf_template.yaml`
 - Примеры
    - general_ledger/rdv_idl/flow_dumps/cf_ins_rdv_idl_bbridge_insurance_risk.yaml
    ```yaml
      - id: control_flow
        builder: ceh_core_idl.app.builders.include_flow_builder
        description: Управляющий поток
        properties:
          ref: cf_ceh_template
          properties:
            work_flow_id: wrk_ins_rdv_idl_bbridge_insurance_risk
            target_resource_cds:
              - ceh.idl.bbridge_insurance_risk.ins
            source_resource_cds:
              - ceh.rdv.mart_product_ins_rb_retailinsuranceproduct_ins
              - ceh.rdv.mart_object_ins_rb_insuranceobject_ins
              - ceh.rdv.mart_ins_rb_insuranceobjectrisk_ins
              - ceh.rdv.mart_app_ins_store_retailinsurancedeal_ins
              - ceh.rdv.mart_ins_store_retailinsuranceagreementref_ins
              - ceh.rdv.mart_agr_ins_store_retailinsuranceagreement_ins
              - ceh.rdv.mart_app_ins_rb_retailinsuranceapplication_ins
              - ceh.rdv.mart_client_ins_rb_retailinsuranceparticipant_ins
            algorithm_uid: zi21_ins_rdv_idl_bbridge_insurance_risk
    ```


# Передача параметров от контрольного потока рабочему

## Оператор запуска потока (legacy)

Нужен для получения `conf`.

 - Тип `ceh_core_idl.app.operators.core.trigger_dag_operator`/`sequential_trigger_dag_operator`
 - Код `ceh_core_idl/libs/af_lib/complex_operators/core/trigger_dag_operator/trigger_dag_operator.py`/`sequential_trigger_dag_operator/sequential_trigger_dag_operator.py`
 - Примеры
    - general_ledger/rdv_idl/flow_dumps/cf_deal_rtls_idl_bbridge_product.yaml
    ```yaml
      - id: trigger_wrk
        description: Проверим test оператор с out параметрами
        type: ceh_core_idl.app.operators.core.trigger_dag_operator
        properties:
          trigger_dag_id: wrk_deal_rtls_idl_bbridge_product
          conf: ${get_workflow_parameters}
        sequencer:
          dependencies:
            - ref: get_metrics
    ```
    - general_ledger/rdv_idl/flow_dumps/cf_deal_rtls_ods_rdv_mart_agreement_depn_rtls_cycles.yaml
    ```yaml
      - id: trigger_wf
        description: Последовательно запускаем поток.
        type: ceh_core_idl.app.operators.core.sequential_trigger_dag_operator
        properties:
          trigger_dag_id: wf_deal_rtls_ods_rdv_mart_agreement_depn_rtls
          conf: ${transform_statement_result.jq_response}
    ```
   - general_ledger/rdv_idl/flow_dumps/cf_acqb_rdv_idl_bbridge_technical_service_package.yaml
   ```yaml
      - id: trigger_wrk
        type: ceh_core_idl.app.operators.core.trigger_dag_operator
        description: Запуск рабочего потока
        properties:
          trigger_dag_id: wrk_acqb_rdv_idl_bbridge_technical_service_package
          conf:
            instance_id: ${get_workflow_parameters.instance_id}
            src_version_id: ${get_workflow_parameters.src_version_id}
            stable_version_id: ${get_workflow_parameters.stable_version_id}
            init_load_flg: ${get_workflow_parameters.init_load_flg}
            algorithm_uid: acqb_rdv_idl_bbridge_technical_service_package
            by_src: ${get_metrics.result_data}
        sequencer:
          dependencies:
            - ref: get_metrics 
    ``` 

## Оператор запуска потока (new)

Нужен для получения `conf`.

 - Тип `ceh_core_libs.steps.core.trigger_job_step`/`trigger_batch_job_step`
 - Код `ceh_core_libs/steps/core/trigger_job_step.py`/`trigger_batch_job_step.py`
 - Примеры
    - general_ledger/rdv_idl/flow_dumps/idl_cf_template.yaml
    ```yaml
      - id: trigger_wrk
        type: ceh_core_libs.steps.core.trigger_job_step
        description: Запуск рабочего потока
        properties:
          job_id: ${work_flow_id}
          conf:
            src_version_id: ${get_target_versions.result.resource_state_data.values() | min}
            common_version_id: ${get_common_version.result.jq_data}
            init_load_flg: false
            algorithm_uid: ${algorithm_uid}
            by_src: ${get_metrics.result_data}
        sequencer:
          dependencies:
            - ref: get_metrics
    ```

RDV
====

# Контрольные потоки

## Шаблон управляющего потока

Необходимо получить `algos_map`/`resources` - связь ресурса-источника с ресурсом-приёмником через алгоритм для рабочего потока

 - Тип `rdv_cf_uni_template.cf_uni_template`/`cf_rdv_cf_uni_template_v2`
 - Код `general_ledger/src_rdv/flow_dumps/rdv_cf_uni_template.yaml`/`rdv_cf_uni_template_v2.yaml`
 - Примеры
    - general_ledger/src_rdv/flow_dumps/cf_cprt_cftm_ods_rdv_kpp_list_cftm.yaml
    ```yaml
      - id: cf_cprt_cftm_ods_rdv_kpp_list_cftm
        description: Управляющий поток для загрузки источника CFT2MAIN_SOH.H2_Z#KPP_LIST
        builder: ceh_core_idl.app.builders.simple_flow_builder
        tasks:
          - id: run_wf_cprt_cftm_ods_rdv_kpp_list_cftm
            builder: ceh_core_idl.app.builders.include_flow_builder
            properties:
              ref: cf_uni_template
              properties:
                mode: ANY
                actual_dttm_prefix: cftm
                work_flow_id: wf_cprt_cftm_ods_rdv_kpp_list_cftm
                algos_map:
                  1.CFTM.ZI1.24:
                    - ceh: ceh.rdv_dict.ref_kpp_list_cftm
                      uni: ods.CFT2MAIN_SOH.H2_Z#KPP_LIST
                  1.CFTM.ZI1.24:
                    - ceh: ceh.rdv.lsat_kpp_list_collection_cftm
                      uni: ods.CFT2MAIN_SOH.H2_Z#KPP_LIST
    ```
    - general_ledger/src_rdv/flow_dumps/cf_card_wayn_ods_rdv_hsat_card_secure_wayn_s44.yaml
    ```yaml
      - id: cf_card_wayn_ods_rdv_hsat_card_secure_wayn_s44
        description: Управляющий поток
        builder: ceh_core_idl.app.builders.simple_flow_builder
        tasks:
          - id: run_wf_card_wayn_ods_rdv_hsat_card_secure_wayn_s44
            builder: ceh_core_idl.app.builders.include_flow_builder
            properties:
              ref: cf_uni_template
              properties:
                work_flow_id: wf_card_wayn_ods_rdv_hsat_card_secure_wayn_s44
                wf_metric: ods_wf_max_date_to
                cf_metric: dataset_max_date_to
                timeout: 1800
                mode: ANY
                resources:
                  - ceh: ceh.rdv.hsat_card_secure_wayn_s44
                    uni: ods.WAY4VTB_SOH.H2_CARD_INFO
    ```
   
> Существуют и другие шаблоны, например `rdv_cf_ods_template.cf_ods_template`, `rdv_cf_snapshot_template.cf_snapshot` 
и другие. Вероятно, все они удовлетворяют шаблону `general_ledger/src_rdv/flow_dumps/rdv_cf_*template*.yaml` (кроме
`general_ledger/src_rdv/flow_dumps/eod_template.yaml`) и имеют схожую структуру входных параметров: `work_flow_id` и 
`algos_map`. 


## `cf_uni_map`/`cf_uni_ref` или MAC справочники

Необходимо получить `resource_cd_tgt`/`resource_cd_src` - связь ресурса-источника с ресурсом-приёмником из
шаблонов `cf_uni_map_S2T.yaml`/`cf_uni_ref_S2T.yaml`

 - Тип `cf_mac_unified_loader`
 - Код `general_ledger/src_rdv/dags/cf_mac_unified_loader.py`
   - `general_ledger/src_rdv/schema/work_flows/wf_uni_map.yaml` 
   - `general_ledger/src_rdv/schema/work_flows/wf_uni_ref.yaml` 
   - `general_ledger/src_rdv/uni_load/mac_meta_files/map_meta.yaml`
   - `general_ledger/src_rdv/uni_load/mac_meta_files/ref_meta.yaml`
   - `general_ledger/src_rdv/uni_load/cf_uni_map_S2T.yaml`
   - `general_ledger/src_rdv/uni_load/cf_uni_ref_S2T.yaml`
 - Примеры
    - general_ledger/src_rdv/uni_load/cf_uni_map_S2T.yaml
    ```yaml
      - resource_cd_tgt: ceh.rdv.currency_src
        resource_cd_src: ACPD.ORS_CEH.C_V_D_CURRENCY_SRC
        schema_name: ORS_CEH
        table_name: C_V_D_CURRENCY_SRC
      - resource_cd_tgt: ceh.rdv.map_financial_instrument_type
        resource_cd_src: ACPD.ORS_CEH.C_DTPL_FIN_INSTR_TP_SRC
        schema_name: ORS_CEH
        table_name: C_DTPL_FIN_INSTR_TP_SRC
    ```
    - general_ledger/src_rdv/uni_load/cf_uni_ref_S2T.yaml
    ```yaml
      - resource_cd_tgt: ceh.rdv.currency
        resource_cd_src: ACPD.ORS_CEH.C_V_D_CURRENCY
        schema_name: ORS_CEH
        table_name: C_V_D_CURRENCY
      - resource_cd_tgt: ceh.rdv_dict.ref_financial_instrument_type
        resource_cd_src: ACPD.ORS_CEH.C_DTPL_FIN_INSTRMNT_TP
        schema_name: ORS_CEH
        table_name: C_DTPL_FIN_INSTRMNT_TP
    ```

# Рабочие потоки

## Ресурсы потоков

Сначала необходимо получить `sources.resource_cd` и `targets.resource_cd`, а затем определить их связь через
`algorithm_uid`/`_2` в `mappings` посредством их алиасов `short_name`, в общем простом случае это выглядит так:

general_ledger/src_rdv/schema/work_flows/wf_acqr_wfbm_ods_rdv_acquiring_terminal_device_rec.yaml
```yaml
schema_version: '1.13'
name: wf_acqr_wfbm_ods_rdv_acquiring_terminal_device_rec
type: WORK_FLOW
version: 1
tags:
  - "team: ZI19"
# ...
targets:
  - short_name: mart_device_rec
    resource_cd: ceh.rdv.mart_acquiring_terminal_device_rec_wfbm
    table: mart_acquiring_terminal_device_rec_wfbm
    schema: rdv
  - short_name: hub_acquiring_terminal
    resource_cd: ceh.rdv.hub_acquiring_terminal
    table: hub_acquiring_terminal
    schema: rdv
  - short_name: hub_deal
    resource_cd: ceh.rdv.hub_deal
    table: hub_deal
    schema: rdv
  - short_name: hub_department
    resource_cd: ceh.rdv.hub_department
    table: hub_department
    schema: rdv
sources:
  - short_name: h2_device_rec
    type: DB_TABLE
    object: h2_device_rec
    resource_cd: ods.W4BM_SOH.H2_DEVICE_REC
mappings:
  marts:
    - target: mart_device_rec
      source: h2_device_rec
      short_name: mart_device_rec
      algorithm_uid: 27.WFBM.ZI19.19
      algorithm_uid_2: '1'
      delta_mode: new
# ...
```

### `satellit`'ы и `reference`'ы

Кроме `marts` в `mappings` могут быть так же следующие атрибуты со схожей структурой:
 - `references`
 - `hub_satellites`
 - `link_satellites`


### `COMPUTED_TABLE'ы

Так же в `targets` и `sources` могут быть так называемые `COMPUTED_TABLE`, которые не содержат `resource_cd` 
непосредственно, а ссылаются на `targets`/`sources` посредством `sub_sources_map`:

general_ledger/src_rdv/schema/work_flows/wf_app_dks_dapp_rdv_personlocation_dks.yaml
```yaml
name: wf_app_dks_dapp_rdv_personlocation_dks
type: WORK_FLOW
schema_version: '1.13'
version: 1
tags:
  - 'wf'
# ...
sources:
  - short_name: dks_pl
    type: DB_TABLE
    resource_cd: dapp.prod_repl_subo_dks.dks_drp_personlocation
    object: dks_drp_personlocation
  - short_name: dks_dcappq
    type: DB_TABLE
    resource_cd: dapp.prod_repl_subo_dks.dks_drp_dcapplicantquestionnaire
    object: dks_drp_dcapplicantquestionnaire
  - short_name: ct1
    type: COMPUTED_TABLE
    object: ct_app_dks_dapp_rdv_personlocation_dks
    sub_sources_map: 
      dks_pl: dks_pl
      dks_dcappq: dks_dcappq
targets:
  - short_name: mart_dks_pl
    schema: rdv
    table: mart_personlocation_dks
    resource_cd: ceh.rdv.mart_personlocation_dks
  - short_name: hub_address
    schema: rdv
    table: hub_address
    resource_cd: ceh.rdv.hub_address
# ...
mappings:
  marts:
    - short_name: rdv_dks_pl
      algorithm_uid: '20.DKS.ZI21.11'
      algorithm_uid_2: '1'
      target: mart_dks_pl
      source: ct1
      delta_mode: new
# ...
```

### `business_key_schema'ы

Кроме того, ресурсы hub'ов в mart'ах блокируются не целиком, а только в части указанной схемы ключа `business_key_schema`:

general_ledger/src_rdv/schema/work_flows/wf_acqr_wfbm_ods_rdv_acquiring_terminal_device_rec.yaml
```yaml
schema_version: '1.13'
name: wf_acqr_wfbm_ods_rdv_acquiring_terminal_device_rec
type: WORK_FLOW
version: 1
tags:
  - "team: ZI19"
  - "wf" 
# ...
targets:
  - short_name: mart_device_rec
    resource_cd: ceh.rdv.mart_acquiring_terminal_device_rec_wfbm
    table: mart_acquiring_terminal_device_rec_wfbm
    schema: rdv
  - short_name: hub_acquiring_terminal
    resource_cd: ceh.rdv.hub_acquiring_terminal
    table: hub_acquiring_terminal
    schema: rdv
  - short_name: hub_deal
    resource_cd: ceh.rdv.hub_deal
    table: hub_deal
    schema: rdv
  - short_name: hub_department
    resource_cd: ceh.rdv.hub_department
    table: hub_department
    schema: rdv
sources:
  - short_name: h2_device_rec
    type: DB_TABLE
    object: h2_device_rec
    resource_cd: ods.W4BM_SOH.H2_DEVICE_REC
mappings:
  marts:
    - target: mart_device_rec
      source: h2_device_rec
      short_name: mart_device_rec
      algorithm_uid: 27.WFBM.ZI19.19
      algorithm_uid_2: '1'
      delta_mode: new
      # ...
      field_map:
        # ...
      hub_map:
        - target: hub_acquiring_terminal
          rk_field: acquiring_terminal_rk
          business_key_schema: BK-rdv-acquiring_terminal-acquiring_terminal_seq-WFBM
          field_map:
            acquiring_terminal_id:
              type: column
              value: id
          on_full_null: good_default
        - target: hub_deal
          rk_field: deal_rk
          business_key_schema: BK-rdv-deal-deal_acnt_contract_seq-WFBM
          field_map:
            deal_id:
              type: column
              value: acnt_contract__oid
          on_full_null: good_default
        - target: hub_department
          rk_field: department_rk
          business_key_schema: BK-rdv-department-department_seq-WFBM
          field_map:
            department_id:
              type: column
              value: f_i
          on_full_null: good_default
      history_reducer: ODS.SOH_SCD2
# ...
```

тут блокируются не ресурсы
 - `hub_acquiring_terminal`
 - `hub_deal`
 - `hub_department`

а ресурсы уточнённые до схемы ключа:
 - `ceh.rdv.hub_acquiring_terminal.BK-rdv-acquiring_terminal-acquiring_terminal_seq-WFBM`
 - `ceh.rdv.hub_deal.BK-rdv-deal-deal_acnt_contract_seq-WFBM`
 - `ceh.rdv.hub_department.BK-rdv-department-department_seq-WFBM`


### `dynamic_business_key_schema'ы

Однако, существуют ещё и динамические схемы ключей - `dynamic_business_key_schema`, где указывается имя маппинга схем:

general_ledger/src_rdv/schema/work_flows/wf_serv_ods_ods_rdv_mart_payment_instruction_v_ods.yaml
```yaml
name: wf_serv_ods_ods_rdv_mart_payment_instruction_v_ods
type: WORK_FLOW
schema_version: '1.16'
version: 1
tags:
# ...
orientation: TB
sources:
- short_name: s2_instruction_v1
  type: DB_TABLE
  object: ods_dtpl_union_soh_h2_instruction_v
  resource_cd: ods.DTPL_UNION_SOH.H2_INSTRUCTION_V
targets:
- short_name: mart_payment_ins_1
  schema: rdv
  table: mart_payment_instruction_v_ods
  resource_cd: ceh.rdv.mart_payment_instruction_v_ods
- short_name: hub_payment
  schema: rdv
  table: hub_payment
  resource_cd: ceh.rdv.hub_payment
- short_name: hub_account
  schema: rdv
  table: hub_account
  resource_cd: ceh.rdv.hub_account
- short_name: hub_currency
  schema: rdv
  table: hub_currency
  resource_cd: ceh.rdv.hub_currency
# ...
mappings:
  marts:
  - short_name: mart_pay_uction_v_1
    algorithm_uid: 35.ODS.CKB7.1
    algorithm_uid_2: '1'
    target: mart_payment_ins_1
    source: s2_instruction_v1
    # ...
    field_map:
      # ...
    hub_map:
    - target: hub_payment
      rk_field: payment_rk
      on_full_null: new_rk
      field_map:
        payment_id:
          type: column
          value: id_id
      dynamic_business_key_schema:
        map_bk_name: MAP-rdv-payment-common_services_sys_ods_view
        use_default_bk: false
        key_fields:
          id_sys: system_code
    # ...
```

В маппинге схемы ключа имена схем указываются в `map_entries.entry.bk_schema`, а схема по умолчанию в
`default_entry.bk_schema` (она используется, если в потоке `dynamic_business_key_schema.use_default_bk: true`)

general_ledger/src_rdv/schema/bk_map/MAP-rdv-payment-common_services_sys_ods_view.yaml
```yaml
type: BK_MAP  
name: MAP-rdv-payment-common_services_sys_ods_view
schema_version: '1.0'    
map_bk_name: MAP-rdv-payment-common_services_sys_ods_view
map_hub: hub_payment
key_map_fields:
    - field:
        field_name: id_sys        
default_entry: 
    bk_schema: null
map_entries:
    - entry: 
        description: 'Источник SAVC'
        bk_schema: BK-rdv-payment-payment_uuid-SAVC
        keys:
          id_sys: SAVC
    - entry: 
        description: 'Источник PSRS'
        bk_schema: BK-rdv-payment-payment_uuid-PSRS
        keys:
          id_sys: PSRS
    - entry: 
        description: 'Источник SPKZ'
        bk_schema: BK-rdv-payment-payment_uuid-SPKZ
        keys:
          id_sys: SPKZ
    # ...
```

### Ресурсы `satellite`'ов

Для `hub_satellites`/`link_satellites` схема ключей для ресурсов указывается в `rk_map`:

general_ledger/src_rdv/schema/work_flows/wf_card_wayn_ods_rdv_card_info.yaml
```yaml
name: wf_card_wayn_ods_rdv_card_info
type: WORK_FLOW
schema_version: '1.15'
version: 1
tags:
  - 'area: card'
  # ...
sources:
  - short_name: w4_soh_card_info
    type: DB_TABLE
    object: w4_soh_card_info
    resource_cd: ods.W4_SOH.W4_SOH_CARD_INFO
targets:
  - short_name: hsat_card_slow_wayn
    resource_cd: ceh.rdv.hsat_card_slow_wayn
    table: hsat_card_slow_wayn
    schema: rdv
  - short_name: hub_card
    resource_cd: ceh.rdv.hub_card
    table: hub_card
    schema: rdv
  - table: hub_production_event
    schema: rdv
    short_name: hub_production_event
    resource_cd: ceh.rdv.hub_production_event
  - table: lnk_production_event_card
    schema: rdv
    short_name: lnk_prod_ev_crd
    resource_cd: ceh.rdv.lnk_production_event_card
  - table: lsat_production_event_card_wayn
    schema: rdv
    short_name: lsat_prod_ev_crd
    resource_cd: ceh.rdv.lsat_production_event_card_wayn
  - table: hub_deal
    schema: rdv
    short_name: hub_deal
    resource_cd: ceh.rdv.hub_deal
  - table: lnk_deal_card
    schema: rdv
    short_name: lnk_deal_card
    resource_cd: ceh.rdv.lnk_deal_card
  # ...
  - table: lsat_deal_card_wayn
    schema: rdv
    short_name: lsat_deal_card_wayn121
    resource_cd: ceh.rdv.lsat_deal_card_wayn
  - short_name: hsat_card_fast_wayn
    resource_cd: ceh.rdv.hsat_card_fast_wayn
    table: hsat_card_fast_wayn
    schema: rdv
  - short_name: hsat_card_secure_wayn
    resource_cd: ceh.rdv.hsat_card_secure_wayn
    table: hsat_card_secure_wayn
    schema: rdv
# ...
mappings:
  hub_satellites:
    - target: hsat_card_slow_wayn
      source: w4_soh_card_info
      short_name: w4_s17waynzi13
      algorithm_uid: 17.WAYN.ZI1.3
      algorithm_uid_2: '1'
      delta_mode: new
      synthetic_history:
        syn_history_function: rdv.syn_bussiness_date_trunc
        func_param:
          - param_source: field
            param_value: ods$effective_from_dt
          - param_source: const
            param_value: day
      field_map:
        order_from:
          type: column
          value: order_from
        order_n:
          type: column
          value: order_n
        pm_code:
          type: column
          value: pm_code
        prod_date:
          type: column
          value: prod_date
        deleted_flg:
          type: sql_expression
          value: ods$deleted_flg = '1'
          field_type: BOOLEAN
      rk_map:
        target: hub_card
        business_key_schema: BK-rdv-card-card_seq-WAYN
        field_map:
          card_id:
            type: column
            value: id
      # ...
    - target: hsat_card_fast_wayn
      source: w4_soh_card_info
      short_name: w4_s17waynzi12
      algorithm_uid: 17.WAYN.ZI1.2
      algorithm_uid_2: '1'
      delta_mode: new
      field_map:
        comment_text:
          type: column
          value: comment_text
        status:
          type: column
          value: status
        deleted_flg:
          type: sql_expression
          value: ods$deleted_flg = '1'
          field_type: BOOLEAN
      rk_map:
        target: hub_card
        business_key_schema: BK-rdv-card-card_seq-WAYN
        field_map:
          card_id:
            type: column
            value: id
      # ...
    - target: hsat_card_secure_wayn
      source: w4_soh_card_info
      short_name: w4_s17waynzi11
      algorithm_uid: 17.WAYN.ZI1.1
      algorithm_uid_2: '1'
      delta_mode: new
      synthetic_history:
        syn_history_function: rdv.syn_bussiness_date_trunc
        func_param:
          - param_source: field
            param_value: ods$effective_from_dt
          - param_source: const
            param_value: day
      field_map:
        card_expire:
          type: column
          value: card_expire
        deleted_flg:
          type: sql_expression
          value: ods$deleted_flg = '1'
          field_type: BOOLEAN
      rk_map:
        target: hub_card
        business_key_schema: BK-rdv-card-card_seq-WAYN
        field_map:
          card_id:
            type: column
            value: id
      # ...
    - algorithm_uid: 17.WAYN.ZI1.9
      algorithm_uid_2: '1'
      target: lsat_prod_ev_crd
      source: w4_soh_card_info
      short_name: w4_s17waynzi19
      delta_mode: new
      synthetic_history:
        syn_history_function: rdv.syn_bussiness_date_trunc
        func_param:
          - param_source: field
            param_value: ods$effective_from_dt
          - param_source: const
            param_value: day
      field_map: 
        deleted_flg:
          type: sql_expression
          value: ods$deleted_flg = '1'
          field_type: BOOLEAN
      rk_map:
        target: lnk_prod_ev_crd
        rk_map:
          - rk_field: production_event_rk
            target: hub_production_event
            business_key_schema: BK-rdv-production_event-production_event_code-WAYN
            on_full_null: good_default
            field_map:
              production_event_id:
                type: column
                value: production_event
          - rk_field: card_rk
            target: hub_card
            business_key_schema: BK-rdv-card-card_seq-WAYN
            on_full_null: good_default
            field_map:
              card_id:
                type: column
                value: id
        # ...
    - algorithm_uid: 17.WAYN.ZI1.6
      algorithm_uid_2: '1'
      short_name: clie17waynzi16
      delta_mode: new
      target: lsat_deal_card_wayn121
      source: w4_soh_card_info
      field_map:
        deleted_flg:
          type: sql_expression
          value: ods$deleted_flg = '1'
          field_type: BOOLEAN
      where_clause:
        engine: jq
        template: ods$effective_from_dt >= '{from_dttm}' and ods$effective_from_dt  < '{to_dttm}'
        vars:
          from_dttm: .conf.algos[.this_algo_uid].by_src[.this_source_cd].wf_dataset_max_date_to
          to_dttm: .conf.algos[.this_algo_uid].by_src[.this_source_cd].dataset_max_date_to
      metrics:
        by_src:
          - wf_dataset_max_date_to
      rk_map:
        target: lnk_deal_card
        rk_map:
          - rk_field: deal_rk
            target: hub_deal
            business_key_schema: BK-rdv-deal-deal_contract_subtype_seq-WAYN
            on_full_null: good_default
            field_map:
              deal_id:
                type: column
                value: card_subtype
          - rk_field: card_rk
            target: hub_card
            business_key_schema: BK-rdv-card-card_seq-WAYN
            on_full_null: good_default
            field_map:
              card_id:
                type: column
                value: id
        # ...
    - target: lsat_deal_card_wayn121
      source: w4_soh_card_info
      short_name: w4_s17waynzi15
      algorithm_uid: 17.WAYN.ZI1.5
      algorithm_uid_2: '1'
      delta_mode: new
      field_map:
        deleted_flg:
          type: sql_expression
          value: ods$deleted_flg = '1'
          field_type: BOOLEAN
      where_clause:
        engine: jq
        template: ods$effective_from_dt >= '{from_dttm}' and ods$effective_from_dt  < '{to_dttm}'
        vars:
          from_dttm: .conf.algos[.this_algo_uid].by_src[.this_source_cd].wf_dataset_max_date_to
          to_dttm: .conf.algos[.this_algo_uid].by_src[.this_source_cd].dataset_max_date_to
      metrics:
        by_src:
          - wf_dataset_max_date_to
      rk_map:
        target: lnk_deal_card
        rk_map:
          - rk_field: deal_rk
            target: hub_deal
            business_key_schema: BK-rdv-deal-deal_acnt_contract_seq-WAYN
            on_full_null: good_default
            field_map:
              deal_id:
                type: column
                value: acnt_contract__oid
          - rk_field: card_rk
            target: hub_card
            business_key_schema: BK-rdv-card-card_seq-WAYN
            on_full_null: good_default
            field_map:
              card_id:
                type: column
                value: id
        ref_map:
          - on_full_null: good_default
            cardinality: ONE
            field_map:
              deal_role_cd:
                type: literal
                value: acnt_contract  ###
      synthetic_history:
        syn_history_function: rdv.syn_bussiness_date_trunc
        func_param:
          - param_source: field
            param_value: ods$effective_from_dt
          - param_source: const
            param_value: day
```

### Ресурсы `hub`'ов

В описании `hub`'ов так же указываются их ресурсы `resource_cd` и схемы ключей `business_key_schemas.name`:

general_ledger/src_rdv/schema/ceh/rdv/hub_agreement.yaml
```yaml
schema_version: '1.2'
name: hub_agreement
schema: rdv
type: HUB
columns:
  - name: agreement_rk
    nullable: false
    type: BIGINT
  # ...
rk_field: agreement_rk
bk_fields:
  - name: agreement_id
resource_cd: ceh.rdv.hub_agreement
business_key_schemas:
  - name: BK-rdv-agreement-agreement_num_id-CFTM
    src_cd: CFTM
    bk_type: agreement_num_id
    fields:
      - name: agreement_id
        field_data_quality_checks:
          - name: IsEmpty
        field_transform_chains:
          - name: Cast
            params:
              to_type: VARCHAR
          - name: Coalesce
            params:
              value: ~null~
  - name: BK_agreement_RTLL_seq
    src_cd: RTLL
    bk_type: seq_num_id
    fields:
      - name: agreement_id
        field_data_quality_checks:
          - name: IsEmpty
        field_transform_chains:
          - name: Cast
            params:
              to_type: VARCHAR
          - name: Coalesce
            params:
              value: ~null~
    split_resource: true
  # ...
```
