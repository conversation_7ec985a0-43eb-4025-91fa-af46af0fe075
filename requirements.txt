amqp==5.3.1 ; python_version >= "3.9" and python_version < "4.0"
annotated-types==0.7.0 ; python_version >= "3.9" and python_version < "4.0"
anyio==4.8.0 ; python_version >= "3.9" and python_version < "4.0"
async-timeout==5.0.1 ; python_version >= "3.9" and python_full_version < "3.11.3"
billiard==4.2.1 ; python_version >= "3.9" and python_version < "4.0"
celery[redis]==5.4.0 ; python_version >= "3.9" and python_version < "4.0"
certifi==2024.12.14 ; python_version >= "3.9" and python_version < "4.0"
click-didyoumean==0.3.1 ; python_version >= "3.9" and python_version < "4.0"
click-plugins==1.1.1 ; python_version >= "3.9" and python_version < "4.0"
click-repl==0.3.0 ; python_version >= "3.9" and python_version < "4.0"
click==8.1.8 ; python_version >= "3.9" and python_version < "4.0"
colorama==0.4.6 ; python_version >= "3.9" and python_version < "4.0" and (sys_platform == "win32" or platform_system == "Windows")
dnspython==2.7.0 ; python_version >= "3.9" and python_version < "4.0"
ecdsa==0.19.0 ; python_version >= "3.9" and python_version < "4.0"
email-validator==2.2.0 ; python_version >= "3.9" and python_version < "4.0"
et-xmlfile==2.0.0 ; python_version >= "3.9" and python_version < "4.0"
exceptiongroup==1.2.2 ; python_version >= "3.9" and python_version < "3.11"
fastapi-cli==0.0.7 ; python_version >= "3.9" and python_version < "4.0"
fastapi==0.111.1 ; python_version >= "3.9" and python_version < "4.0"
greenlet==3.1.1 ; python_version < "3.14" and (platform_machine == "aarch64" or platform_machine == "ppc64le" or platform_machine == "x86_64" or platform_machine == "amd64" or platform_machine == "AMD64" or platform_machine == "win32" or platform_machine == "WIN32") and python_version >= "3.9"
h11==0.14.0 ; python_version >= "3.9" and python_version < "4.0"
httpcore==1.0.7 ; python_version >= "3.9" and python_version < "4.0"
httptools==0.6.4 ; python_version >= "3.9" and python_version < "4.0"
httpx==0.28.1 ; python_version >= "3.9" and python_version < "4.0"
idna==3.10 ; python_version >= "3.9" and python_version < "4.0"
jinja2==3.1.5 ; python_version >= "3.9" and python_version < "4.0"
kombu==5.4.2 ; python_version >= "3.9" and python_version < "4.0"
markdown-it-py==3.0.0 ; python_version >= "3.9" and python_version < "4.0"
markupsafe==3.0.2 ; python_version >= "3.9" and python_version < "4.0"
mdurl==0.1.2 ; python_version >= "3.9" and python_version < "4.0"
numpy==2.0.2 ; python_version >= "3.9" and python_version < "3.10"
numpy==2.2.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "4.0"
openpyxl==3.1.5 ; python_version >= "3.9" and python_version < "4.0"
orjson==3.10.16 ; python_version >= "3.9" and python_version < "4.0"
pandas==2.2.3 ; python_version >= "3.9" and python_version < "4.0"
prometheus-client==0.21.1 ; python_version >= "3.9" and python_version < "4.0"
prompt-toolkit==3.0.48 ; python_version >= "3.9" and python_version < "4.0"
psycopg2-binary==2.9.10 ; python_version >= "3.9" and python_version < "4.0"
pyasn1==0.6.1 ; python_version >= "3.9" and python_version < "4.0"
pydantic-core==2.27.2 ; python_version >= "3.9" and python_version < "4.0"
pydantic==2.10.5 ; python_version >= "3.9" and python_version < "4.0"
pygments==2.19.1 ; python_version >= "3.9" and python_version < "4.0"
python-dateutil==2.9.0.post0 ; python_version >= "3.9" and python_version < "4.0"
python-dotenv==1.0.1 ; python_version >= "3.9" and python_version < "4.0"
python-jose==3.3.0 ; python_version >= "3.9" and python_version < "4.0"
python-multipart==0.0.20 ; python_version >= "3.9" and python_version < "4.0"
pytz==2024.2 ; python_version >= "3.9" and python_version < "4.0"
pyxlsb==1.0.10 ; python_version >= "3.9" and python_version < "4.0"
pyyaml==6.0.2 ; python_version >= "3.9" and python_version < "4.0"
redis==5.2.1 ; python_version >= "3.9" and python_version < "4.0"
rich-toolkit==0.13.2 ; python_version >= "3.9" and python_version < "4.0"
rich==13.9.4 ; python_version >= "3.9" and python_version < "4.0"
rsa==4.9 ; python_version >= "3.9" and python_version < "4"
shellingham==1.5.4 ; python_version >= "3.9" and python_version < "4.0"
six==1.17.0 ; python_version >= "3.9" and python_version < "4.0"
sniffio==1.3.1 ; python_version >= "3.9" and python_version < "4.0"
sqlalchemy==2.0.37 ; python_version >= "3.9" and python_version < "4.0"
starlette==0.37.2 ; python_version >= "3.9" and python_version < "4.0"
structlog==21.5.0 ; python_version >= "3.9" and python_version < "4.0"
typer==0.15.1 ; python_version >= "3.9" and python_version < "4.0"
typing-extensions==4.12.2 ; python_version >= "3.9" and python_version < "4.0"
tzdata==2024.2 ; python_version >= "3.9" and python_version < "4.0"
uvicorn==0.30.6 ; python_version >= "3.9" and python_version < "4.0"
uvicorn[standard]==0.30.6 ; python_version >= "3.9" and python_version < "4.0"
uvloop==0.21.0 ; (sys_platform != "win32" and sys_platform != "cygwin") and platform_python_implementation != "PyPy" and python_version >= "3.9" and python_version < "4.0"
vine==5.1.0 ; python_version >= "3.9" and python_version < "4.0"
watchfiles==1.0.4 ; python_version >= "3.9" and python_version < "4.0"
wcwidth==0.2.13 ; python_version >= "3.9" and python_version < "4.0"
websockets==14.1 ; python_version >= "3.9" and python_version < "4.0"
