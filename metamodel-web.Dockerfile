# Подготовка бинар<PERSON><PERSON><PERSON><PERSON> helm
FROM scratch as metamodel-helm
ADD helm.tar.gz /

#Сборка основного образа
FROM docker.repo-ci.sfera.inno.local/dtpl-docker-lib/external/ubi8/python-39:1-154
#docker.repo-ci.sfera.inno.local/dtpl-docker-lib/external/ubi7/python-38:1-112.1718868396
ENV PIP_DISABLE_PIP_VERSION_CHECK=1
ENV LD_LIBRARY_PATH=${LD_LIBRARY_PATH}:/usr/local/lib
ENV LIBRARY_PATH=${LIBRARY_PATH}:/usr/local/lib
ENV CPATH=${CPATH}:/usr/local/include
ENV PYTHONPATH=${PYTHONPATH}:.
ENV PATH="$PATH:/opt/helm"
USER root

ENV REQUESTS_CA_BUNDLE=/etc/pki/ca-trust/extracted/pem/tls-ca-bundle.pem
ADD ./_certs/ /etc/pki/ca-trust/source/anchors/

COPY --from=metamodel-helm \
    /linux-amd64 \
    /opt/helm

RUN --mount=type=secret,source=rpm8-repo,target=/etc/yum.repos.d/ubi.repo \
    --mount=type=secret,source=pip-repo,target=/etc/pip.conf \
    update-ca-trust force-enable \
    && useradd -s /sbin/nologin -d /app -u 5000 -U -c "Application" app \
    && pip install --upgrade pip setuptools wheel \ 
    && chmod -R 775 /app \
    && yum install nc -y \
    && yum clean all

WORKDIR /app

RUN --mount=type=bind,source=./requirements.txt,target=/app/src/requirements.txt \
    --mount=type=secret,source=pip-repo,target=/etc/pip.conf \
    pip install --no-cache-dir --no-input -r /app/src/requirements.txt \
    && find / -name '__pycache__' | xargs rm -rf

COPY --chown=app:app *.py ./
COPY --chown=app:app helm helm/
COPY --chown=app:app *.yml ./
COPY --chown=app:app src/metaloader_rest_api metaloader_rest_api
# включил в образ, чтобы на ИФТ стало возможно запустить ручной тест
COPY --chown=app:app tests tests

USER app

EXPOSE 8000

ENTRYPOINT ["uvicorn", "metaloader_rest_api.app:app", "--host", "0.0.0.0", "--port", "8000"]
